#!/bin/bash

# fetch-ssm-params.sh
# Script to fetch parameters from SSM and create .env file

ENV=$1
if [ -z "$ENV" ]; then
    echo "Usage: ./fetch-ssm-params.sh <environment>"
    echo "Example: ./fetch-ssm-params.sh dev"
    exit 1
fi

# Path where we'll store the .env file
ENV_FILE=".env.${ENV}"

# Remove existing env file if it exists
if [ -f "$ENV_FILE" ]; then
    echo "Removing existing $ENV_FILE..."
    rm -vf "$ENV_FILE"
fi

# Fetch parameters from SSM based on environment path
echo "Fetching SSM parameters for ${ENV} environment..."
aws ssm get-parameters-by-path \
    --path "/pasco/${ENV}/" \
    --recursive \
    --with-decryption \
    --query "Parameters[*].[Name,Value]" \
    --output text | while read -r line; do
    # Extract parameter name and value
    param_name=$(echo "$line" | cut -f1)
    param_value=$(echo "$line" | cut -f2)
    
    # Convert SSM parameter name to env variable name
    # Example: /dev/DATABASE_URL -> DATABASE_URL
    env_name=$(basename "$param_name")
    
    # Write to env file
    echo "${env_name}=${param_value}" >> "$ENV_FILE"
done

echo "Environment file created at $ENV_FILE"
