import streamlit as st
import asyncio
import json
from app.routes.chat_route import interactive_chat


def init_session_state():
    """Initialize session state variables"""
    if "started" not in st.session_state:
        st.session_state.started = False
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "chat_id" not in st.session_state:
        st.session_state.chat_id = None
    if "welcome_requested" not in st.session_state:
        st.session_state.welcome_requested = False


def handle_start_click():
    """Handle start button click"""
    st.session_state.started = True
    st.session_state.messages = []
    st.session_state.chat_id = None
    st.session_state.welcome_requested = False


async def get_welcome_message():
    """Get initial welcome message from the chat"""
    try:
        response = await interactive_chat(
            message="", chat_id=None, node_id=1121, stream=True
        )
        return response
    except Exception as e:
        st.error(f"Error getting welcome message: {str(e)}")
        return None


def parse_sse_data(data_line):
    """Parse SSE data line and extract message content"""
    if not data_line.startswith("data: "):
        return None

    try:
        json_str = data_line[6:]  # Remove 'data: ' prefix
        data = json.loads(json_str)
        return data
    except json.JSONDecodeError:
        return None


async def process_streaming_response(response, placeholder):
    """Process streaming response and update placeholder"""
    full_response = ""
    try:
        async for chunk in response.body_iterator:
            if isinstance(chunk, bytes):
                chunk = chunk.decode("utf-8")

            # Process each line in the chunk
            for line in chunk.split("\n"):
                if not line.strip():
                    continue

                data = parse_sse_data(line)
                if not data:
                    continue

                message = data.get("message", {})
                new_content = message.get("content", "")
                chat_id = data.get("chat_id")
                is_complete = message.get("is_stream_complete", False)

                # Update chat ID in session state if not set
                if st.session_state.chat_id is None and chat_id:
                    st.session_state.chat_id = chat_id

                # Update the display
                placeholder.markdown(new_content + "▌")
                full_response = new_content

                # If stream is complete, update without cursor
                if is_complete:
                    placeholder.markdown(new_content)
                    return full_response

        return full_response
    except Exception as e:
        st.error(f"Error processing stream: {str(e)}")
        return None


async def process_message(user_input, node_id=16):
    """Process user message and get response"""
    try:
        response = await interactive_chat(
            message=user_input,
            chat_id=st.session_state.chat_id,
            node_id=node_id,
            stream=True,
        )
        return response
    except Exception as e:
        st.error(f"Error processing message: {str(e)}")
        return None


def main():
    st.title("Interactive Chat")

    # Initialize session state
    init_session_state()

    # Start button - Only show if not started
    if not st.session_state.started:
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            if st.button(
                "Start Conversation", use_container_width=True, key="start_btn"
            ):
                handle_start_click()
                st.rerun()
        return

    # Display chat ID if available
    if st.session_state.chat_id:
        st.sidebar.text(f"Chat ID: {st.session_state.chat_id}")

    # Display chat history
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # Handle welcome message
    if not st.session_state.welcome_requested:
        st.session_state.welcome_requested = True
        with st.chat_message("assistant"):
            response_placeholder = st.empty()
            with st.spinner("Getting welcome message..."):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                welcome_response = loop.run_until_complete(get_welcome_message())
                if welcome_response:
                    full_response = loop.run_until_complete(
                        process_streaming_response(
                            welcome_response, response_placeholder
                        )
                    )
                    if full_response:
                        st.session_state.messages.append(
                            {"role": "assistant", "content": full_response}
                        )
                loop.close()
        st.rerun()

    # Chat input
    if user_input := st.chat_input("Type your message here..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": user_input})
        with st.chat_message("user"):
            st.markdown(user_input)

        # Get bot response
        with st.chat_message("assistant"):
            response_placeholder = st.empty()
            with st.spinner("Processing..."):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                response = loop.run_until_complete(process_message(user_input))
                if response:
                    full_response = loop.run_until_complete(
                        process_streaming_response(response, response_placeholder)
                    )
                    if full_response:
                        st.session_state.messages.append(
                            {"role": "assistant", "content": full_response}
                        )
                loop.close()

    # Reset button in sidebar
    if st.sidebar.button("Reset Conversation"):
        st.session_state.started = False
        st.session_state.messages = []
        st.session_state.chat_id = None
        st.session_state.welcome_requested = False
        st.rerun()


if __name__ == "__main__":
    main()
