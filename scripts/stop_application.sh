#!/bin/bash
if [ "$DEPLOYMENT_GROUP_NAME" == "Dev" ]
then
    cd /home/<USER>/pasco-govtech-ai-dev

    # Stop running containers
    docker compose down
fi 

if [ "$DEPLOYMENT_GROUP_NAME" == "QA" ]
then
    cd /home/<USER>/pasco-govtech-ai-qa

    # Stop running containers
    docker compose down
else
    cd /home/<USER>/app

    # Stop running containers
    docker compose down
fi

# Remove unused containers and images
docker system prune -f