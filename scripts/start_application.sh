#!/bin/bash

if [ "$DEPLOYMENT_GROUP_NAME" == "Dev" ]
then
    # Create a directory if it doesn't exist
    mkdir -p /home/<USER>/pasco-govtech-ai-dev

    # Copy files to the env directory
    cp -rvf /home/<USER>/app/. /home/<USER>/pasco-govtech-ai-dev
    cd /home/<USER>/pasco-govtech-ai-dev

    # Load environment variables from SSM
    chmod +x fetch-ssm.sh
    bash fetch-ssm.sh dev

    # Build and start containers
    docker compose down
    STAGE=dev docker compose build
    ENV_FILE=.env.dev STAGE=dev docker compose up -d --no-deps connector
fi 

if [ "$DEPLOYMENT_GROUP_NAME" == "QA" ]
then
    # Create a directory if it doesn't exist
    mkdir -p /home/<USER>/pasco-govtech-ai-qa

    # Copy files to the env directory
    cp -rvf /home/<USER>/app/. /home/<USER>/pasco-govtech-ai-qa
    cd /home/<USER>/pasco-govtech-ai-qa

    # Load environment variables from SSM
    chmod +x fetch-ssm.sh
    bash fetch-ssm.sh qa

    # Build and start containers
    docker compose down
    STAGE=qa docker compose build
    ENV_FILE=.env.qa STAGE=qa API_PORT=8010 docker compose up -d --no-deps connector
fi


# Clean up old images
docker image prune -f