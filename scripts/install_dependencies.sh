#!/bin/bash
# Install Docker if not installed
if ! command -v docker &> /dev/null; then
    sudo yum update -y
    sudo yum install -y docker
    sudo service docker start
    sudo usermod -a -G docker ec2-user
else
    echo "Docker is already installed"
fi

# Install Docker Compose if not installed
if ! command -p docker compose version &> /dev/null; then
    mkdir -p /usr/local/lib/docker/cli-plugins
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/lib/docker/cli-plugins/docker-compose
    sudo chmod +x /usr/local/lib/docker/cli-plugins/docker-compose
else
    echo "Docker Compose is already installed"
fi

# Create app directory if it doesn't exist
mkdir -p /home/<USER>/app