
def lambda_handler(event, context):
    if event['triggerSource'] == "CustomMessage_SignUp":
        return create_email_message(event, 'signup')
    elif event['triggerSource'] == "CustomMessage_ForgotPassword":
        return create_email_message(event, 'forgot_password')
    elif event['triggerSource'] == "CustomMessage_ResendCode":
        return create_email_message(event, 'resend_code')
    else:
        return event


def capitalize_first(string):
    if not string:  # Check if the string is empty
        return string
    return ' '.join(word.capitalize() for word in string.split())


def create_html_template(title, message_content):
    return f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{title}</title>
        <style media="all" type="text/css">

          .main p,
          .main td,
          .main span {{
           font-size: calc(8px + 0.5vw) !important;
          }}
          .wrapper {{
            padding: calc(6px + 0.5vw) !important;
          }}
          .content {{
            padding: 0 !important;
          }}

          .container {{
            padding: 0 !important;
            padding-top: calc(8px + 0.5vw) !important;
            width: 100% !important;
          }}

          .main {{
            border-left-width: 10px !important;
            border-radius: 10px !important;
            border-right-width: 10px !important;
          }}


          .ExternalClass {{
            width: 100%;
          }}

          .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {{
            line-height: 100%;
          }}

          .apple-link a {{
            color: inherit !important;
            font-family: inherit !important;
            font-size: inherit !important;
            font-weight: inherit !important;
            line-height: inherit !important;
            text-decoration: none !important;
          }}
        </style>
    </head>
    <body style="font-family: Helvetica, sans-serif; -webkit-font-smoothing: antialiased; font-size: 16px; line-height: 1.3; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; background-color: #f4f5f6; margin: 0; padding: 0;">
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #f4f5f6; width: 100%;" width="100%" bgcolor="#f4f5f6">
      <tr>
        <td style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top;" valign="top">&nbsp;</td>
        <td class="container" style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; max-width: 600px; padding: 0; padding-top: 24px; width: 600px; margin: 0 auto;" width="600" valign="top">
          <div class="content" style="box-sizing: border-box; display: block; margin: 0 auto; max-width: 600px; padding: 0;">

            <!-- START CENTERED WHITE CONTAINER -->
            <span class="preheader" style="color: transparent; display: none; height: 0; max-height: 0; max-width: 0; opacity: 0; overflow: hidden; mso-hide: all; visibility: hidden; width: 0;">Verification code for your account</span>
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background: #ffffff; border: 1px solid #eaebed; border-radius: 16px; width: 100%;" width="100%">

        <div class="container">
            {message_content}
        </div>
        </table>

        <!-- START FOOTER -->
        <div class="footer" style="clear: both; padding-top: 24px; text-align: center; width: 100%;">
              <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
                <tr>
                  <td class="content-block" style="font-family: Helvetica, sans-serif; vertical-align: top; color: #9a9ea6; font-size: 16px; text-align: center;" valign="top" align="center">
                    <span class="apple-link" style="color: #9a9ea6; font-size: 16px; text-align: center;"> Please do not reply to this mail as it is auto-generated. </span>
                    <br/>
                    <span class="apple-link" style="color: #9a9ea6; font-size: 16px; text-align: center;">Powered By SkillRank</span>
                  </td>
                </tr>

              </table>
            </div>
             <!-- END FOOTER -->

        <!-- END CENTERED WHITE CONTAINER --></div>

        </td>
        <td style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top;" valign="top">&nbsp;</td>
      </tr>
    </table>
    </body>
    </html>
    """


def create_email_message(event, email_type):
    code = event['request']['codeParameter']
    user_name_raw = event['request']['userAttributes']['name']
    user_name = capitalize_first(user_name_raw)

    # Common content parts
    intro = f"<p style=\"font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;\">Hi {user_name},</p>"
    code_content = f"<p style=\"font-family: Helvetica, sans-serif; font-size: 1.5rem; font-weight: bold; margin: 0; margin-bottom: 16px; color: #000000;\">{code}</p>"

    if email_type == 'signup':
        subject = "Welcome to Pasco County - Verify Your Email"
        title = "Pasco County Email Verification"
        body = f"""
            <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">Thank you for signing up! To complete your registration, please use the following One-Time Password (OTP):</p>
            {code_content}
            <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">This OTP is valid for the next 10 minutes. Please enter it on the sign-up page to verify your account.</p>
            <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">If you did not request this OTP, please disregard this email.</p>
        """

    elif email_type == 'forgot_password':
        subject = "Pasco County Password Reset Request"
        title = "Pasco County Password Reset"
        body = f"""
            <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">You have requested to reset your password. Please use the following code to complete your password reset process:</p>
            {code_content}
            <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">Please enter this code on the verification page to proceed.<br/>If you didn't request this code, please ignore this email.</p>
        """

    elif email_type == 'resend_code':
        subject = "Pasco County - Your New Verification Code"
        title = "Pasco County New Verification Code"
        body = f"""
            <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">You requested to resend the code. Please use the following code to complete the process:</p>
            {code_content}
            <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">Please enter this code on the verification page to proceed.<br/>If you didn't request this code, please ignore this email.</p>
        """

    message_content = f"""
        <tr>
            <td class="wrapper" style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; box-sizing: border-box; padding: 24px;" valign="top">
                {intro}
                {body}
            </td>
        </tr>
    """

    event['response']['emailSubject'] = subject
    event['response']['emailMessage'] = create_html_template(title, message_content)

    return event
