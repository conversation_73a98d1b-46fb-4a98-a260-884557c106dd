# Stage 1: Build dependencies
FROM public.ecr.aws/docker/library/python:3.11-slim AS builder

COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Install only essential build dependencies
RUN apt-get update && apt-get install -y \
    poppler-utils \
    curl \
    build-essential \
    libgl1 \
    libglib2.0-0 \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# Install Rust
RUN curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y --profile minimal
ENV PATH="/root/.cargo/bin:${PATH}"

# Create virtual environment and upgrade pip
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN uv pip install --no-cache-dir -U pip setuptools wheel

# Install Python dependencies
WORKDIR /build
COPY requirements.txt .
RUN uv pip install --no-cache-dir --compile -r requirements.txt

# Stage 2: Runtime dependencies preparation
FROM public.ecr.aws/docker/library/debian:bookworm-slim AS runtime-deps
RUN apt-get update && apt-get install -y \
    poppler-utils \
    libgl1 \
    libglib2.0-0 \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/* \
    && mkdir -p /app

# Stage 3: Final minimal image
FROM public.ecr.aws/docker/library/python:3.11-slim AS final

# Copy only necessary runtime files from previous stages
COPY --from=runtime-deps /usr/lib /usr/lib
COPY --from=runtime-deps /usr/bin/pdf* /usr/bin/
COPY --from=runtime-deps /lib /lib
COPY --from=builder /opt/venv /opt/venv

# Set Python path and environment variables
ENV PYTHONPATH=/opt/venv/lib/python3.11/site-packages:/fastapi \
    PATH=/opt/venv/bin:$PATH \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PORT=8000

# Set working directory
WORKDIR /fastapi

# Copy application code
COPY app ./app
COPY tests ./tests

# Expose port
EXPOSE ${PORT}

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]