[{"ParameterKey": "CodebuildRoleName", "ParameterValue": ""}, {"ParameterKey": "CodePipelineRoleName", "ParameterValue": ""}, {"ParameterKey": "EcsTaskRoleName", "ParameterValue": ""}, {"ParameterKey": "SnsTopicName", "ParameterValue": ""}, {"ParameterKey": "S3BucketName", "ParameterValue": ""}, {"ParameterKey": "MongoTdName", "ParameterValue": ""}, {"ParameterKey": "MilvusTdName", "ParameterValue": ""}, {"ParameterKey": "MongoServiceName", "ParameterValue": ""}, {"ParameterKey": "MilvusServiceName", "ParameterValue": ""}, {"ParameterKey": "MongoNlb", "ParameterValue": ""}, {"ParameterKey": "MongoTg", "ParameterValue": ""}, {"ParameterKey": "MilvusNlb", "ParameterValue": ""}, {"ParameterKey": "MilvusTg", "ParameterValue": ""}, {"ParameterKey": "EcrRepoName", "ParameterValue": ""}, {"ParameterKey": "EcsTargetGroupName", "ParameterValue": ""}, {"ParameterKey": "EcsAlb", "ParameterValue": ""}, {"ParameterKey": "VpcID", "ParameterValue": ""}, {"ParameterKey": "EcsClusterName", "ParameterValue": ""}, {"ParameterKey": "EcsTaskDefinitionName", "ParameterValue": ""}, {"ParameterKey": "EcsContainerName", "ParameterValue": ""}, {"ParameterKey": "EcsServiceName", "ParameterValue": ""}, {"ParameterKey": "Subnets", "ParameterValue": ""}, {"ParameterKey": "SecurityGroups", "ParameterValue": ""}, {"ParameterKey": "CodebuildName", "ParameterValue": ""}, {"ParameterKey": "CodecommitRepoHttp", "ParameterValue": ""}, {"ParameterKey": "CodecommitRefName", "ParameterValue": ""}, {"ParameterKey": "CodepipelineName", "ParameterValue": ""}, {"ParameterKey": "CodepipelineBucketName", "ParameterValue": ""}, {"ParameterKey": "CodecommitRepoName", "ParameterValue": ""}, {"ParameterKey": "CodecommitBranchName", "ParameterValue": ""}, {"ParameterKey": "ImageDefinitionName", "ParameterValue": ""}, {"ParameterKey": "PipelineNotificationName", "ParameterValue": "pasco-pipeline-connector-notify-dev"}, {"ParameterKey": "ServiceNamespace", "ParameterValue": "pasco-dev"}, {"ParameterKey": "Stage", "ParameterValue": "dev"}]