AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template to create a CodeBuild project

Parameters:
  Subnets:
    Type: List<AWS::EC2::Subnet::Id>
    Description: List of Subnets
  SecurityGroups:
    Type: List<AWS::EC2::SecurityGroup::Id>
    Description: List of security groups
  CodebuildRoleName:
    Type: String
    Description: name of codebuild role
  CodePipelineRoleName:
    Type: String
    Description: name of codepipeline role
  EcsTaskRoleName:
    Type: String
    Description: name of ecs task role
  SnsTopicName:
    Type: String
    Description: name of SNS topic
  S3BucketName:
    Type: String
    Description: name of S3 bucket
  EcrRepoName:
    Type: String
    Description: name of ecr repo
  EcsClusterName:
    Type: String
    Description: name of ecs cluster
  EcsTaskDefinitionName:
    Type: String
    Description: name of connector task definition
  EcsTargetGroupName:
    Type: String
    Description: name of connector target group
  MongoTdName:
    Type: String
    Description: name of mongo task definition
  MilvusTdName:
    Type: String
    Description: name of milvus task definition
  MongoServiceName:
    Type: String
    Description: name of mongo service name
  MilvusServiceName:
    Type: String
    Description: name of milvus service name
  EcsAlb:
    Type: String
    Description: name of Connector ALB
  VpcID:
    Type: String
    Description: name of ecs task definition
  EcsContainerName:
    Type: String
    Description: name of ecs container
  EcsServiceName:
    Type: String
    Description: name of ecs service
  CodebuildName:
    Type: String
    Description: name of codebuild
  CodecommitRepoHttp:
    Type: String
    Description: name of CodecommitRepoHttp
  CodecommitRefName:
    Type: String
    Description: name of codecommit-ref
  CodepipelineName:
    Type: String
    Description: name of codepipeline
  CodepipelineBucketName:
    Type: String
    Description: name of codepipeline-bucket
  CodecommitRepoName:
    Type: String
    Description: name of codecommit-repo
  CodecommitBranchName:
    Type: String
    Description: name of codecommit-branch
  ImageDefinitionName:
    Type: String
    Description: name of image-definition
  PipelineNotificationName:
    Type: String
    Description: name of pipeline-notification
  ServiceNamespace:
    Type: String
    Description: name of service-namespace
  Stage:
    Type: String
    Default: "dev"
    Description: name of stage deployed

Resources:
  # required roles
  CodeBuildServiceRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Ref CodebuildRoleName
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: codebuild.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: codebuild-policy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - s3:*
                  - codecommit:*
                  - events:*
                  - codebuild:*
                  - sns:*
                  - ecr:*
                Resource: "*"
  CodePipelineServiceRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Ref CodePipelineRoleName
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: codepipeline.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: codepipeline-service-role
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Action:
                  - iam:PassRole
                Resource: "*"
                Effect: Allow
                Condition:
                  StringEqualsIfExists:
                    iam:PassedToService:
                      - cloudformation.amazonaws.com
                      - elasticbeanstalk.amazonaws.com
                      - ec2.amazonaws.com
                      - ecs-tasks.amazonaws.com
              - Action:
                  - codecommit:CancelUploadArchive
                  - codecommit:GetBranch
                  - codecommit:GetCommit
                  - codecommit:GetRepository
                  - codecommit:GetUploadArchiveStatus
                  - codecommit:UploadArchive
                Resource: "*"
                Effect: Allow
              - Action:
                  - codedeploy:CreateDeployment
                  - codedeploy:GetApplication
                  - codedeploy:GetApplicationRevision
                  - codedeploy:GetDeployment
                  - codedeploy:GetDeploymentConfig
                  - codedeploy:RegisterApplicationRevision
                Resource: "*"
                Effect: Allow
              - Action:
                  - codestar-connections:UseConnection
                Resource: "*"
                Effect: Allow
              - Action:
                  - elasticbeanstalk:*
                  - ec2:*
                  - elasticloadbalancing:*
                  - autoscaling:*
                  - cloudwatch:*
                  - s3:*
                  - sns:*
                  - cloudformation:*
                  - rds:*
                  - sqs:*
                  - ecs:*
                Resource: "*"
                Effect: Allow
              - Action:
                  - lambda:InvokeFunction
                  - lambda:ListFunctions
                Resource: "*"
                Effect: Allow
              - Action:
                  - opsworks:CreateDeployment
                  - opsworks:DescribeApps
                  - opsworks:DescribeCommands
                  - opsworks:DescribeDeployments
                  - opsworks:DescribeInstances
                  - opsworks:DescribeStacks
                  - opsworks:UpdateApp
                  - opsworks:UpdateStack
                Resource: "*"
                Effect: Allow
              - Action:
                  - cloudformation:CreateStack
                  - cloudformation:DeleteStack
                  - cloudformation:DescribeStacks
                  - cloudformation:UpdateStack
                  - cloudformation:CreateChangeSet
                  - cloudformation:DeleteChangeSet
                  - cloudformation:DescribeChangeSet
                  - cloudformation:ExecuteChangeSet
                  - cloudformation:SetStackPolicy
                  - cloudformation:ValidateTemplate
                Resource: "*"
                Effect: Allow
              - Action:
                  - codebuild:BatchGetBuilds
                  - codebuild:StartBuild
                  - codebuild:BatchGetBuildBatches
                  - codebuild:StartBuildBatch
                Resource: "*"
                Effect: Allow
              - Action:
                  - devicefarm:ListProjects
                  - devicefarm:ListDevicePools
                  - devicefarm:GetRun
                  - devicefarm:GetUpload
                  - devicefarm:CreateUpload
                  - devicefarm:ScheduleRun
                Resource: "*"
                Effect: Allow
              - Action:
                  - servicecatalog:ListProvisioningArtifacts
                  - servicecatalog:CreateProvisioningArtifact
                  - servicecatalog:DescribeProvisioningArtifact
                  - servicecatalog:DeleteProvisioningArtifact
                  - servicecatalog:UpdateProduct
                Resource: "*"
                Effect: Allow
              - Action:
                  - cloudformation:ValidateTemplate
                Resource: "*"
                Effect: Allow
              - Action:
                  - ecr:DescribeImages
                Resource: "*"
                Effect: Allow
              - Action:
                  - states:DescribeExecution
                  - states:DescribeStateMachine
                  - states:StartExecution
                Resource: "*"
                Effect: Allow
              - Action:
                  - appconfig:StartDeployment
                  - appconfig:StopDeployment
                  - appconfig:GetDeployment
                Resource: "*"
                Effect: Allow
  ECSTaskExecutionRole:
    Type: "AWS::IAM::Role"
    Properties:
      RoleName: !Ref EcsTaskRoleName
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ecs-tasks.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: ECSExecutionPolicy
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - ecr:GetAuthorizationToken
                  - ecr:BatchCheckLayerAvailability
                  - ecr:GetDownloadUrlForLayer
                  - ecr:BatchGetImage
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - s3:*
                  - ssm:Describe*
                  - ssm:Get*
                  - ssm:List*
                  - autoscaling:Describe*
                  - cloudwatch:*
                  - logs:*
                  - sns:*
                  - iam:GetPolicy
                  - iam:GetPolicyVersion
                  - iam:GetRole
                  - oam:ListSinks
                Resource: "*"

  # security group
  ConnectorSecurityGroup:
    Type: "AWS::EC2::SecurityGroup"
    Properties:
      GroupDescription: "Allow access to MongoDB, Milvus and connector from anywhere"
      VpcId: !Ref VpcID
      SecurityGroupIngress:
        - Description: Allow inbound MongoDB traffic from anywhere
          IpProtocol: "tcp"
          FromPort: 27017
          ToPort: 27017
          CidrIp: "0.0.0.0/0"
        - Description: Allow inbound HTTP traffic from anywhere
          IpProtocol: "tcp"
          FromPort: 80
          ToPort: 80
          CidrIp: "0.0.0.0/0"
        - Description: Allow inbound MilvusDB traffic from anywhere
          IpProtocol: "tcp"
          FromPort: 19530
          ToPort: 19530
          CidrIp: "0.0.0.0/0"
      SecurityGroupEgress:
        - Description: Allow all outbound traffic
          IpProtocol: -1
          FromPort: -1
          ToPort: -1
          CidrIp: 0.0.0.0/0

  SNSTopic:
    Type: "AWS::SNS::Topic"
    Properties:
      TopicName: !Ref SnsTopicName
  SNSTopicPolicy:
    Type: "AWS::SNS::TopicPolicy"
    Properties:
      Topics:
        - !Ref SNSTopic
      PolicyDocument:
        Version: "2008-10-17"
        Statement:
          - Sid: "CodeNotification_publish"
            Effect: "Allow"
            Principal:
              Service: "codestar-notifications.amazonaws.com"
            Action: "SNS:Publish"
            Resource: !Ref SNSTopic
  EmailSubscription:
    Type: "AWS::SNS::Subscription"
    Properties:
      Protocol: email
      TopicArn: !Ref SNSTopic
      Endpoint: <EMAIL>

  # S3 Bucket
  S3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Ref S3BucketName
      AccessControl: Private
      VersioningConfiguration:
        Status: Enabled
      Tags:
        - Key: Name
          Value: !Ref S3BucketName
        - Key: Environment
          Value: !Ref Stage

  #ECR repo
  ECRRepository:
    Type: AWS::ECR::Repository
    Properties:
      RepositoryName: !Ref EcrRepoName
      ImageTagMutability: MUTABLE
      EncryptionConfiguration:
        EncryptionType: AES256
      Tags:
        - Key: Name
          Value: !Ref EcrRepoName
        - Key: Environment
          Value: !Ref Stage

  #ECS cluster
  ECSCluster:
    Type: AWS::ECS::Cluster
    Properties:
      ClusterName: !Ref EcsClusterName
      CapacityProviders:
        - FARGATE
        - FARGATE_SPOT
      ClusterSettings:
        - Name: containerInsights
          Value: disabled
      Configuration:
        ExecuteCommandConfiguration:
          Logging: DEFAULT
      ServiceConnectDefaults:
        Namespace: !Ref ServiceNamespace
      Tags:
        - Key: Name
          Value: !Ref EcsClusterName
        - Key: Environment
          Value: !Ref Stage

  # ECS Taksdefinition
  PascoConnectorTaskDefinition:
    Type: "AWS::ECS::TaskDefinition"
    Properties:
      Family: !Ref EcsTaskDefinitionName
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !Ref ECSTaskExecutionRole
      TaskRoleArn: !Ref ECSTaskExecutionRole
      NetworkMode: awsvpc
      Cpu: 1024
      Memory: 3072
      ContainerDefinitions:
        - Name: !Ref EcsContainerName
          Image: 905418237839.dkr.ecr.us-east-1.amazonaws.com/pasco-connector-dev:latest
          Essential: true
          Environment:
            - Name: MONGO_URL
              Value: !Sub "mongodb://root:password@${MongoServiceName}.${ServiceNamespace}:27017"
            - Name: MILVUS_URI
              Value: !Sub "http://${MilvusServiceName}.${ServiceNamespace}:19530"
            - Name: MILVUS_DEFAULT_DATABASE
              Value: default
          Secrets:
            - Name: STAGE
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/pasco/${Stage}/STAGE"
            - Name: AWS_COGNITO_APP_CLIENT_ID
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/pasco/${Stage}/AWS_COGNITO_APP_CLIENT_ID"
            - Name: AWS_USER_POOL_ID
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/pasco/${Stage}/AWS_USER_POOL_ID"
            - Name: OPENAI_API_KEY
              ValueFrom: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/pasco/${Stage}/OPENAI_API_KEY"
          PortMappings:
            - ContainerPort: 8000
              HostPort: 8000
              Name: connector
          LogConfiguration:
            LogDriver: "awslogs"
            Options:
              awslogs-create-group: "true"
              awslogs-group: !Sub "awslogs-${EcsContainerName}"
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: "ecs"
    DependsOn:
      - ECSCluster

  MongoDBTaskDefinition:
    Type: "AWS::ECS::TaskDefinition"
    Properties:
      Family: !Ref MongoTdName
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !Ref ECSTaskExecutionRole
      TaskRoleArn: !Ref ECSTaskExecutionRole
      NetworkMode: awsvpc
      Cpu: 1024
      Memory: 3072
      ContainerDefinitions:
        - Name: mongodb
          Image: "mongo:latest"
          Essential: true
          Memory: 500
          Cpu: 256
          PortMappings:
            - ContainerPort: 27017
              HostPort: 27017
              name: mongodb
          Environment:
            - Name: MONGO_INITDB_ROOT_USERNAME
              Value: "root"
            - Name: MONGO_INITDB_ROOT_PASSWORD
              Value: "password"
          LogConfiguration:
            LogDriver: "awslogs"
            Options:
              awslogs-create-group: "true"
              awslogs-group: !Sub "awslogs-${MongoTdName}-mongodb"
              awslogs-region: !Sub "${AWS::Region}"
              awslogs-stream-prefix: "ecs"

  MilvusTaskDefinition:
    Type: "AWS::ECS::TaskDefinition"
    Properties:
      Family: !Ref MilvusTdName
      RequiresCompatibilities:
        - FARGATE
      ExecutionRoleArn: !Ref ECSTaskExecutionRole
      TaskRoleArn: !Ref ECSTaskExecutionRole
      NetworkMode: awsvpc
      Cpu: 512
      Memory: 2048
      ContainerDefinitions:
        - Name: etcd
          Image: quay.io/coreos/etcd:v3.5.5
          Essential: true
          Memory: 512
          Cpu: 64
          Environment:
            - Name: ETCD_AUTO_COMPACTION_MODE
              Value: "revision"
            - Name: ETCD_AUTO_COMPACTION_RETENTION
              Value: "1000"
            - Name: ETCD_QUOTA_BACKEND_BYTES
              Value: "4294967296"
            - Name: ETCD_SNAPSHOT_COUNT
              Value: "50000"
          PortMappings:
            - ContainerPort: 2379
              HostPort: 2379
          Command:
            - etcd
            - -advertise-client-urls=http://127.0.0.1:2379
            - -listen-client-urls=http://0.0.0.0:2379
            - --data-dir=/etcd

        - Name: minio
          Image: minio/minio:RELEASE.2023-03-20T20-16-18Z
          Essential: true
          Memory: 512
          Cpu: 64
          Environment:
            - Name: MINIO_ACCESS_KEY
              Value: "minioadmin"
            - Name: MINIO_SECRET_KEY
              Value: "minioadmin"
          PortMappings:
            - ContainerPort: 9000
              HostPort: 9000
          Command:
            - minio
            - server
            - /minio_data
          HealthCheck:
            Command:
              - "CMD"
              - "curl"
              - "-f"
              - "http://localhost:9000/minio/health/live"
            Interval: 300
            Timeout: 20
            Retries: 3

        - Name: milvusdb
          Image: milvusdb/milvus:latest
          Essential: true
          Memory: 1024
          Cpu: 384
          Environment:
            - Name: ETCD_ENDPOINTS
              Value: "localhost:2379"
            - Name: MINIO_ADDRESS
              Value: "localhost:9000"
          PortMappings:
            - ContainerPort: 19530
              HostPort: 19530
              name: milvusdb
            - ContainerPort: 9091
              HostPort: 9091
          DependsOn:
            - ContainerName: etcd
              Condition: START
            - ContainerName: minio
              Condition: START
          Command:
            - milvus
            - run
            - standalone

  #ECS service
  PascoConnectorService:
    Type: AWS::ECS::Service
    Properties:
      Cluster: !Ref ECSCluster
      ServiceName: !Ref EcsServiceName
      TaskDefinition: !Ref PascoConnectorTaskDefinition
      DesiredCount: 1
      LaunchType: FARGATE
      LoadBalancers:
        - ContainerName: !Ref EcsContainerName
          ContainerPort: 8000
          LoadBalancerName: !Ref AWS::NoValue
          TargetGroupArn: !Ref TargetGroup
      PlatformVersion: LATEST
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: ENABLED
          Subnets: !Ref Subnets
          SecurityGroups: !Ref SecurityGroups
      ServiceConnectConfiguration:
        Enabled: true
        Namespace: !Ref ServiceNamespace
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: 100
      Tags:
        - Key: Name
          Value: !Ref EcsServiceName
        - Key: Environment
          Value: !Ref Stage
        - Key: Type
          value: ConnectorAPI
    DependsOn:
      - Listener
      - MongoDBService
      - MilvusService

  MongoDBService:
    Type: "AWS::ECS::Service"
    Properties:
      Cluster: !Ref ECSCluster
      ServiceName: !Ref MongoServiceName
      DesiredCount: 1
      TaskDefinition: !Ref MongoDBTaskDefinition
      LaunchType: FARGATE
      ServiceConnectConfiguration:
        Enabled: true
        Namespace: !Ref ServiceNamespace
        Services:
          - PortName: mongodb
            DiscoveryName: !Ref MongoServiceName
            ClientAliases:
              - Port: 27017
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: ENABLED
          Subnets: !Ref Subnets
          SecurityGroups: !Ref SecurityGroups
      Tags:
        - Key: Name
          Value: !Ref MongoServiceName
        - Key: Environment
          Value: !Ref Stage
        - Key: Type
          value: Database

  MilvusService:
    Type: "AWS::ECS::Service"
    Properties:
      Cluster: !Ref ECSCluster
      ServiceName: !Ref MilvusServiceName
      DesiredCount: 1
      TaskDefinition: !Ref MilvusTaskDefinition
      LaunchType: FARGATE
      ServiceConnectConfiguration:
        Enabled: true
        Namespace: !Ref ServiceNamespace
        Services:
          - PortName: milvusdb
            DiscoveryName: !Ref MilvusServiceName
            ClientAliases:
              - Port: 19530
      NetworkConfiguration:
        AwsvpcConfiguration:
          AssignPublicIp: ENABLED
          Subnets: !Ref Subnets
          SecurityGroups: !Ref SecurityGroups
      Tags:
        - Key: Name
          Value: !Ref MilvusServiceName
        - Key: Environment
          Value: !Ref Stage
        - Key: Type
          value: Database

  LoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Type: application
      Name: !Ref EcsAlb
      SecurityGroups: !Ref SecurityGroups
      Subnets: !Ref Subnets
      Tags:
        - Key: Name
          Value: !Ref EcsAlb
        - Key: Environment
          Value: !Ref Stage
  TargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      HealthCheckPath: /
      Name: !Ref EcsTargetGroupName
      Port: 8000
      Protocol: HTTP
      TargetType: ip
      HealthCheckProtocol: HTTP
      VpcId: !Ref VpcID
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: "300"
      Tags:
        - Key: Name
          Value: !Ref EcsTargetGroupName
        - Key: Environment
          Value: !Ref Stage
  Listener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref TargetGroup
      LoadBalancerArn: !Ref LoadBalancer
      Port: 80
      Protocol: HTTP
      Tags:
        - Key: Name
          Value: !Ref EcsAlb
        - Key: Environment
          value: !Ref Stage

  # code build
  CodeBuildProject:
    Type: AWS::CodeBuild::Project
    Properties:
      Name: !Ref CodebuildName
      Source:
        Type: CODECOMMIT
        Location: !Ref CodecommitRepoHttp
      Environment:
        ComputeType: BUILD_GENERAL1_SMALL
        Image: aws/codebuild/amazonlinux2-x86_64-standard:5.0
        Type: LINUX_CONTAINER
        PrivilegedMode: true
      ServiceRole: !GetAtt CodeBuildServiceRole.Arn
      Artifacts:
        Type: NO_ARTIFACTS
      SourceVersion: !Ref CodecommitRefName

  # CodePipeline
  CodePipeline:
    Type: AWS::CodePipeline::Pipeline
    Properties:
      Name: !Ref CodepipelineName
      RoleArn: !GetAtt CodePipelineServiceRole.Arn
      PipelineType: V2
      ArtifactStore:
        Type: S3
        Location: !Ref CodepipelineBucketName

      Stages:
        - Name: Source
          Actions:
            - Name: SourceAction
              ActionTypeId:
                Category: Source
                Owner: AWS
                Provider: CodeCommit
                Version: "1"
              OutputArtifacts:
                - Name: SourceOutput
              Configuration:
                RepositoryName: !Ref CodecommitRepoName
                BranchName: !Ref CodecommitBranchName
        - Name: Build
          Actions:
            - Name: BuildAction
              ActionTypeId:
                Category: Build
                Owner: AWS
                Provider: CodeBuild
                Version: "1"
              InputArtifacts:
                - Name: SourceOutput
              OutputArtifacts:
                - Name: BuildOutput
              Configuration:
                ProjectName: !Ref CodeBuildProject
        - Name: ManualApproval
          Actions:
            - Name: ManualApprovalAction
              ActionTypeId:
                Category: Approval
                Owner: AWS
                Provider: Manual
                Version: "1"
              Configuration:
                CustomData: "Please review the changes before deployment"
                NotificationArn: !Ref SNSTopic
        - Name: Deploy
          Actions:
            - Name: DeployAction
              ActionTypeId:
                Category: Deploy
                Owner: AWS
                Provider: ECS
                Version: "1"
              InputArtifacts:
                - Name: BuildOutput
              Configuration:
                ClusterName: !Ref ECSCluster
                ServiceName: !Ref PascoConnectorService
                FileName: !Ref ImageDefinitionName

  PipelineNotificationRule:
    Type: AWS::CodeStarNotifications::NotificationRule
    Properties:
      Name: !Ref PipelineNotificationName
      Resource: !Sub arn:aws:codepipeline:${AWS::Region}:${AWS::AccountId}:${CodepipelineName}
      DetailType: FULL
      EventTypeIds:
        - codepipeline-pipeline-pipeline-execution-succeeded
        - codepipeline-pipeline-pipeline-execution-superseded
        - codepipeline-pipeline-pipeline-execution-canceled
        - codepipeline-pipeline-pipeline-execution-failed
        - codepipeline-pipeline-stage-execution-canceled
        - codepipeline-pipeline-stage-execution-failed
        - codepipeline-pipeline-manual-approval-succeeded
        - codepipeline-pipeline-manual-approval-needed
        - codepipeline-pipeline-manual-approval-failed
        - codepipeline-pipeline-action-execution-canceled
        - codepipeline-pipeline-action-execution-failed
      Targets:
        - TargetType: SNS
          TargetAddress: !Ref SNSTopic
