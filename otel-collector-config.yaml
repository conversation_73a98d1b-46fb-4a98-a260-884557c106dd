receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317

exporters:
  debug:
    verbosity: detailed
  otlphttp/openobserve_pasco:
    endpoint: https://api.openobserve.ai/api/ebrahim_organization_51278_5N00UZORTekbNNC
    headers:
      Authorization: Basic ************************************************************
      stream-name: pasco-govtech-ai-connector

  otlphttp/openobserve_chat:
    endpoint: https://api.openobserve.ai/api/ebrahim_organization_51278_5N00UZORTekbNNC
    headers:
      Authorization: Basic ************************************************************
      stream-name: pasco-govtech-ai-chat

processors:
  batch:
    timeout: 1s
    send_batch_size: 1024
  memory_limiter:
    check_interval: 1s
    limit_mib: 1500
    spike_limit_mib: 512
  filter/pasco:
    logs:
      include:
        match_type: regexp
        resource_attributes:
          - key: log.type
            value: "connector"
  filter/chat:
    logs:
      include:
        match_type: regexp
        resource_attributes:
          - key: log.type
            value: "chat"
  resource/pasco:
    attributes:
      - key: service.name
        value: pasco
        action: upsert

service:
  pipelines:
    logs/pasco:
      receivers: [otlp]
      processors: [batch, filter/pasco]
      exporters: [debug, otlphttp/openobserve_pasco]
    logs/chat:
      receivers: [otlp]
      processors: [batch, filter/chat]
      exporters: [debug, otlphttp/openobserve_chat]
    traces:
      receivers: [otlp]
      processors: [memory_limiter, batch]
      exporters: [debug, otlphttp/openobserve_pasco]
    metrics:
      receivers: [otlp]
      processors: [memory_limiter, batch]
      exporters: [debug, otlphttp/openobserve_pasco]
