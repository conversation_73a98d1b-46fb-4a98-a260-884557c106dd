#!/bin/bash

# Run flake8 and filter files with errors
files_with_errors=$(flake8 --exit-zero --format='%(path)s' | sort -u)

# Check if there are any files with errors
if [ -z "$files_with_errors" ]; then
  echo "No files with errors found by flake8."
  exit 0
fi

# Print all files with errors
# echo "Files with Flake8 errors:"
# for file in $files_with_errors; do
#   echo "$file"
# done

# Apply autopep8 to each file with errors
echo "Applying autopep8 to files with errors..."
for file in $files_with_errors; do
  autopep8 --in-place --aggressive --aggressive "$file"
  echo "Formatted $file"
done

echo "Formatting completed."
