{"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": {"MinimumLength": 6, "RequireLowercase": false, "RequireNumbers": false, "RequireSymbols": false, "RequireUppercase": false}}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": false, "IdTokenValidity": 1413, "AccessTokenValidity": 30, "RefreshTokenValidity": 1, "TokenValidityUnits": {"AccessToken": "minutes", "IdToken": "minutes", "RefreshToken": "days"}}}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": "serverless-framework-deployments-us-east-1-4f241e70-af29", "Export": {"Name": "sls-pasco-govtech-ai-dev-ServerlessDeploymentBucketName"}}}}