{"service": {"service": "pasco-govtech-ai", "serviceObject": {"name": "pasco-govtech-ai"}, "provider": {"name": "aws", "region": "us-east-1", "stage": "dev", "versionFunctions": true, "compiledCloudFormationTemplate": {"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": {"MinimumLength": 6, "RequireLowercase": false, "RequireNumbers": false, "RequireSymbols": false, "RequireUppercase": false}}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": false, "IdTokenValidity": 1413, "AccessTokenValidity": 30, "RefreshTokenValidity": 1, "TokenValidityUnits": {"AccessToken": "minutes", "IdToken": "minutes", "RefreshToken": "days"}}}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": "serverless-framework-deployments-us-east-1-4f241e70-af29", "Export": {"Name": "sls-pasco-govtech-ai-dev-ServerlessDeploymentBucketName"}}}}}, "pluginsData": {}, "functions": {}, "resources": {"Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": {"MinimumLength": 6, "RequireLowercase": false, "RequireNumbers": false, "RequireSymbols": false, "RequireUppercase": false}}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": false, "IdTokenValidity": 1413, "AccessTokenValidity": 30, "RefreshTokenValidity": 1, "TokenValidityUnits": {"AccessToken": "minutes", "IdToken": "minutes", "RefreshToken": "days"}}}}, "Outputs": {}}, "configValidationMode": "warn", "serviceFilename": "serverless.yaml", "initialServerlessConfig": {"useDotenv": true, "service": "pasco-govtech-ai", "provider": {"$ref": "$[\"service\"][\"provider\"]"}, "resources": {"$ref": "$[\"service\"][\"resources\"]"}}, "appId": null, "orgId": null, "layers": {}}, "package": {"artifactDirectoryName": "serverless/pasco-govtech-ai/dev/1728654975889-2024-10-11T13:56:15.889Z", "artifact": ""}}