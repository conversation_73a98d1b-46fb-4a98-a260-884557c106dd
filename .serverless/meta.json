{"/home/<USER>/Work/pasco/pasco-govtech-ai": {"versionSfCore": null, "versionFramework": "4.4.5", "isWithinCompose": false, "isCompose": false, "composeOrgName": null, "composeResolverProviders": {"env": {"instance": {"credentials": "<REDACTED>", "serviceConfigFile": {"useDotenv": true, "service": "pasco-govtech-ai", "provider": {"name": "aws", "region": "us-east-1", "stage": "dev", "versionFunctions": true, "compiledCloudFormationTemplate": {"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": "<REDACTED>"}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": "<REDACTED>", "IdTokenValidity": "<REDACTED>", "AccessTokenValidity": "<REDACTED>", "RefreshTokenValidity": "<REDACTED>", "TokenValidityUnits": "<REDACTED>"}}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": "serverless-framework-deployments-us-east-1-4f241e70-af29", "Export": {"Name": "sls-pasco-govtech-ai-dev-ServerlessDeploymentBucketName"}}}}}, "resources": {"Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": "<REDACTED>"}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": "<REDACTED>", "IdTokenValidity": "<REDACTED>", "AccessTokenValidity": "<REDACTED>", "RefreshTokenValidity": "<REDACTED>", "TokenValidityUnits": "<REDACTED>"}}}, "Outputs": {}}}, "configFileDirPath": "/home/<USER>/Work/pasco/pasco-govtech-ai", "config": {"type": "env"}, "options": {"stage": "dev", "aws-profile": "pasco-admin"}, "stage": "dev", "logger": {"namespace": "s:core:resolver:env", "prefix": null, "prefixColor": null}, "_credentialsPromise": "<REDACTED>"}, "resolvers": {}}, "default-aws-credential-resolver": "<REDACTED>", "opt": {"instance": {"credentials": "<REDACTED>", "serviceConfigFile": {"useDotenv": true, "service": "pasco-govtech-ai", "provider": {"name": "aws", "region": "us-east-1", "stage": "dev", "versionFunctions": true, "compiledCloudFormationTemplate": {"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": "<REDACTED>"}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": "<REDACTED>", "IdTokenValidity": "<REDACTED>", "AccessTokenValidity": "<REDACTED>", "RefreshTokenValidity": "<REDACTED>", "TokenValidityUnits": "<REDACTED>"}}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": "serverless-framework-deployments-us-east-1-4f241e70-af29", "Export": {"Name": "sls-pasco-govtech-ai-dev-ServerlessDeploymentBucketName"}}}}}, "resources": {"Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": "<REDACTED>"}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": "<REDACTED>", "IdTokenValidity": "<REDACTED>", "AccessTokenValidity": "<REDACTED>", "RefreshTokenValidity": "<REDACTED>", "TokenValidityUnits": "<REDACTED>"}}}, "Outputs": {}}}, "configFileDirPath": "/home/<USER>/Work/pasco/pasco-govtech-ai", "config": {"type": "opt"}, "options": {"stage": "dev", "aws-profile": "pasco-admin"}, "stage": "dev", "dashboard": null, "logger": {"namespace": "s:core:resolver:opt", "prefix": null, "prefixColor": null}, "_credentialsPromise": "<REDACTED>"}, "resolvers": {}}, "self": {"instance": {"credentials": "<REDACTED>", "serviceConfigFile": {"useDotenv": true, "service": "pasco-govtech-ai", "provider": {"name": "aws", "region": "us-east-1", "stage": "dev", "versionFunctions": true, "compiledCloudFormationTemplate": {"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": "<REDACTED>"}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": "<REDACTED>", "IdTokenValidity": "<REDACTED>", "AccessTokenValidity": "<REDACTED>", "RefreshTokenValidity": "<REDACTED>", "TokenValidityUnits": "<REDACTED>"}}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": "serverless-framework-deployments-us-east-1-4f241e70-af29", "Export": {"Name": "sls-pasco-govtech-ai-dev-ServerlessDeploymentBucketName"}}}}}, "resources": {"Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": "<REDACTED>"}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": "<REDACTED>", "IdTokenValidity": "<REDACTED>", "AccessTokenValidity": "<REDACTED>", "RefreshTokenValidity": "<REDACTED>", "TokenValidityUnits": "<REDACTED>"}}}, "Outputs": {}}}, "configFileDirPath": "/home/<USER>/Work/pasco/pasco-govtech-ai", "config": {"type": "self"}, "options": {"stage": "dev", "aws-profile": "pasco-admin"}, "stage": "dev", "dashboard": null, "logger": {"namespace": "s:core:resolver:self", "prefix": null, "prefixColor": null}, "_credentialsPromise": "<REDACTED>"}, "resolvers": {}}}, "composeServiceName": null, "servicePath": "/home/<USER>/Work/pasco/pasco-govtech-ai", "serviceConfigFileName": "serverless.yaml", "service": {"useDotenv": true, "service": "pasco-govtech-ai", "provider": {"name": "aws", "region": "us-east-1", "stage": "dev", "versionFunctions": true, "compiledCloudFormationTemplate": {"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": "<REDACTED>"}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": "<REDACTED>", "IdTokenValidity": "<REDACTED>", "AccessTokenValidity": "<REDACTED>", "RefreshTokenValidity": "<REDACTED>", "TokenValidityUnits": "<REDACTED>"}}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": "serverless-framework-deployments-us-east-1-4f241e70-af29", "Export": {"Name": "sls-pasco-govtech-ai-dev-ServerlessDeploymentBucketName"}}}}}, "resources": {"Resources": {"CognitoUserPool": {"Type": "AWS::Cognito::UserPool", "Properties": {"UserPoolName": {"Fn::Sub": "pasco-govtech-ai-userpool-dev"}, "AccountRecoverySetting": {"RecoveryMechanisms": [{"Name": "verified_email", "Priority": 1}]}, "AdminCreateUserConfig": {"AllowAdminCreateUserOnly": false}, "MfaConfiguration": "OFF", "AutoVerifiedAttributes": ["email"], "UsernameAttributes": ["email"], "Policies": {"PasswordPolicy": "<REDACTED>"}, "Schema": [{"Name": "email", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "name", "AttributeDataType": "String", "Mutable": true, "Required": true}, {"Name": "role", "AttributeDataType": "String", "Mutable": true, "Required": false}, {"Name": "county", "AttributeDataType": "String", "Mutable": true, "Required": false}], "VerificationMessageTemplate": {"DefaultEmailOption": "CONFIRM_WITH_CODE", "EmailSubject": "Pasco Govtech Verification Code"}}}, "CognitoUserPoolClient": {"Type": "AWS::Cognito::UserPoolClient", "Properties": {"ClientName": {"Fn::Sub": "AppClient-dev"}, "UserPoolId": {"Ref": "CognitoUserPool"}, "SupportedIdentityProviders": ["COGNITO"], "ExplicitAuthFlows": ["ALLOW_ADMIN_USER_PASSWORD_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_CUSTOM_AUTH"], "GenerateSecret": "<REDACTED>", "IdTokenValidity": "<REDACTED>", "AccessTokenValidity": "<REDACTED>", "RefreshTokenValidity": "<REDACTED>", "TokenValidityUnits": "<REDACTED>"}}}, "Outputs": {}}}, "serviceRawFile": "useDotenv: true\nservice: pasco-govtech-ai # Service name\nprovider:\n  name: aws\n  region: ${env:REGION}\n  stage: ${opt:stage, 'dev'}\n\nresources:\n  Resources:\n    CognitoUserPool:\n      Type: AWS::Cognito::UserPool\n      Properties:\n        UserPoolName: !Sub pasco-govtech-ai-userpool-${self:provider.stage}\n        AccountRecoverySetting:\n          RecoveryMechanisms:\n            - Name: verified_email\n              Priority: 1\n        AdminCreateUserConfig:\n          AllowAdminCreateUserOnly: false\n        MfaConfiguration: \"OFF\"\n        AutoVerifiedAttributes:\n          - email\n        UsernameAttributes:\n          - email\n        Policies:\n          PasswordPolicy:\n            MinimumLength: 6\n            RequireLowercase: False\n            RequireNumbers: False\n            RequireSymbols: False\n            RequireUppercase: False\n        Schema:\n          - Name: email\n            AttributeDataType: String\n            Mutable: true\n            Required: true\n          - Name: name\n            AttributeDataType: String\n            Mutable: true\n            Required: true\n          - Name: role\n            AttributeDataType: String\n            Mutable: true\n            Required: false\n          - Name: county\n            AttributeDataType: String\n            Mutable: true\n            Required: false\n        VerificationMessageTemplate:\n          DefaultEmailOption: CONFIRM_WITH_CODE\n          EmailSubject: \"Pasco Govtech Verification Code\"\n\n    CognitoUserPoolClient:\n      Type: AWS::Cognito::UserPoolClient\n      Properties:\n        ClientName: !Sub AppClient-${self:provider.stage}\n        UserPoolId: !Ref CognitoUserPool\n        SupportedIdentityProviders:\n          - COGNITO\n        ExplicitAuthFlows:\n          - ALLOW_ADMIN_USER_PASSWORD_AUTH\n          - ALLOW_USER_PASSWORD_AUTH\n          - ALLOW_REFRESH_TOKEN_AUTH\n          - ALLOW_USER_SRP_AUTH\n          - ALLOW_CUSTOM_AUTH\n        GenerateSecret: false\n        IdTokenValidity: 1413 # 23.55 Hours\n        AccessTokenValidity: 30\n        RefreshTokenValidity: 1\n        TokenValidityUnits:\n          AccessToken: \"minutes\"\n          IdToken: \"minutes\"\n          RefreshToken: \"days\"\n", "command": ["deploy"], "options": {"stage": "dev", "aws-profile": "pasco-admin"}, "error": null, "params": {}, "machineId": "2575e35ecd3cb76a548ddacd818afea8", "stage": "dev", "accessKeyV2": "<REDACTED>", "accessKeyV1": "<REDACTED>", "orgId": "860c5cfd-736d-402c-adfa-26a478a3e273", "orgName": "nothing2", "userId": "jlpyvdzsGmnfdK94xz", "dashboard": {"isEnabledForService": false, "requiredAuthentication": false, "orgFeaturesInUse": null, "orgObservabilityIntegrations": null, "serviceAppId": null, "serviceProvider": null, "instanceParameters": null}, "userName": "nothing2", "subscription": null, "userEmail": "<EMAIL>", "serviceProviderAwsRegion": "us-east-1", "serviceProviderAwsCredentials": "<REDACTED>", "serviceProviderAwsAccountId": "************", "projectType": "traditional", "versionSf": "4.4.5", "serviceProviderAwsCfStackName": "pasco-govtech-ai-dev", "integrations": {}, "serviceUniqueId": "arn:aws:cloudformation:us-east-1:************:stack/pasco-govtech-ai-dev/33f1fdc0-710f-11ef-ab28-0afff608bd7f", "serviceProviderAwsCfStackId": "arn:aws:cloudformation:us-east-1:************:stack/pasco-govtech-ai-dev/33f1fdc0-710f-11ef-ab28-0afff608bd7f", "serviceProviderAwsCfStackCreated": "2024-09-12T13:59:18.317Z", "serviceProviderAwsCfStackUpdated": null, "serviceProviderAwsCfStackStatus": "CREATE_COMPLETE", "serviceProviderAwsCfStackOutputs": [{"OutputKey": "ServerlessDeploymentBucketName", "OutputValue": "serverless-framework-deployments-us-east-1-4f241e70-af29", "ExportName": "sls-pasco-govtech-ai-dev-ServerlessDeploymentBucketName"}]}}