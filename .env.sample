PROJECT_NAME="Connector"
PROJECT_VERSION="0.0.1"
PROJECT_DESCRIPTION="Connector with fastapi and nosql stack"
MONGO_URL="mongodb://mongo:27017"
MONGO_USER="admin"
MONGO_PASSWORD="password"
MONG<PERSON>_DEFAULT_DATABASE="dummy"
MONGO_CONFIG_DATABASE="dummyConfig"
MONGO_CHAT_COLLECTION="chat_history"
MILVUS_URI="http://milvus:19530"
MILVUS_TOKEN="f13da7bbba23135a88c"
MILVUS_DEFAULT_DATABASE="dummy"
MILVUS_SEARCH_RESULTS_LIMIT=5
HISTORY_FETCH_LIMIT=5
OPENAI_BASE="http://localhost:11434/v1"
OPENAI_API_KEY=""
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_ACCESS_KEY_ID_S3_Textract=""
AWS_SECRET_ACCESS_KEY_S3_Textract=""
AWS_REGION="us-east-1"
STAGE="dev"
AWS_USER_POOL_ID=""
AWS_COGNITO_APP_CLIENT_ID=""
CELERY_BROKER_URL="redis://redis:6379/0"
CELERY_RESULT_BACKEND="redis://redis:6379/0"
NO_PAGE_FOR_PARAM_EXTRACT=2
NO_SECTIONS_PARAM_EXTRACT_PAGE=0
NEO4J_URI="neo4j://neo4j"
NEO4J_USERNAME="neo4j"
NEO4J_PASSWORD="password"
APP_SYNC_API_KEY=""
APP_SYNC_HTTP_DOMAIN=""
OTEL_SERVICE_NAME="fastapi-connector"
OTEL_EXPORTER_OTLP_ENDPOINT="http://otel-collector:4317"
OTEL_PYTHON_LOG_CORRELATION="true"
OTEL_TRACES_EXPORTER="otlp"
OTEL_METRICS_EXPORTER="otlp"
OTEL_LOGS_EXPORTER="otlp"
INSTANCE_ID="1"