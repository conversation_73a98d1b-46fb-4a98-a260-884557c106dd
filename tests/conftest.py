import os
import sys
from typing import Any, Generator
import pytest
from tests.mock_milvus import <PERSON>ck<PERSON>il<PERSON><PERSON>
from fastapi import Fast<PERSON>I
# from fastapi.testclient import TestClient
from starlette.testclient import TestClient
import mongomock
from app.database.database_storage import Database
# from app.main import get_application, shutdown_event
from app.database.database_storage import get_mongo_db
from app.database.database_storage import get_milvus_client
from dotenv import load_dotenv
import json
from tests.utils.mock_neo4j import mock_neo4j


load_dotenv()


sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


@pytest.fixture(scope="module")
def app() -> Generator[FastAPI, Any, None]:
    """
    Create a new FastAPI application.
    """
    app = FastAPI()
    return app

    # _app = get_application()
    # yield _app
    # shutdown_event()


@pytest.fixture(scope="module")
def db() -> Generator[Database, Any, None]:
    """
    Create a new MongoDB mock database.
    """
    clientTest = mongomock.MongoClient()
    yield clientTest['dummy']
    clientTest.close()


@pytest.fixture(scope="module")
def client(app: FastAPI, db, mock_milvus) -> Generator[TestClient, Any, None]:
    """
    Create a new FastAPI test client.
    """
    async def _get_test_db():
        try:
            yield db
        finally:
            pass

    async def milvus_test_db():
        try:
            yield mock_milvus
        finally:
            pass

    app.dependency_overrides[get_mongo_db] = _get_test_db
    app.dependency_overrides[get_milvus_client] = milvus_test_db

    with TestClient(app) as client:
        yield client


@pytest.fixture
def get_textract_data():
    with open("tests/data/textract_resp.json") as file:
        data = json.load(file)
    return data[0]


@pytest.fixture
def get_layout_data():
    with open("tests/data/textract_resp.json") as file:
        data = json.load(file)
    return data[0]["layout_data"]


@pytest.fixture
def get_file_name():
    with open("tests/data/textract_resp.json") as file:
        data = json.load(file)
    return data[0]["filePath"]


@pytest.fixture
def get_total_pages():
    with open("tests/data/textract_resp.json") as file:
        data = json.load(file)
    return data[0]["totalPages"]


@pytest.fixture
def get_mock_neo4j():
    return mock_neo4j()


@pytest.fixture(scope="module")
def mock_milvus():
    return MockMilvus()




