from app.crud.neo4j.schema import Node
import json


join_related_chunks_between_pages_args = [
    (
        Node(
            "TEXT",
            {
                "coordinates": {
                    "Width": 0.7,
                    "Height": 0.7,
                    "Left": 0.2,
                    "Top": 0.2,
                },
                "name": "node_1",
            },
        ),
        Node(
            "TEXT",
            {
                "coordinates": {
                    "Width": 0.3,
                    "Height": 0.2,
                    "Left": 0.1,
                    "Top": 0.1,
                },
                "name": "node_2",
            },
        ),
    ),
    (
        Node(
            "TEXT",
            {
                "coordinates": json.dumps({
                    "Width": 0.85,
                    "Height": 0.85,
                    "Left": 0.1,
                    "Top": 0.1,
                }),
                "name": "node_3",
            },
        ),
        Node(
            "TEXT",
            {
                "coordinates": {
                    "Width": 0.1,
                    "Height": 0.1,
                    "Left": 0.15,
                    "Top": 0.05,
                },
                "name": "node_4",
            },
        ),
    ),
    (
        Node(
            "TEXT",
            {
                "coordinates": {
                    "Width": 0.8,
                    "Height": 0.8,
                    "Left": 0.3,
                    "Top": 0.3,
                },
                "name": "node_5",
            },
        ),
        Node(
            "TEXT",
            {
                "coordinates": {
                    "Width": 0.3,
                    "Height": 0.3,
                    "Left": 0.2,
                    "Top": 0.1,
                },
                "name": "node_6",
            },
        ),
    ),
]

