import pytest
import mongomock
from app.core import config


# Mock function to replace `get_mongo_db`
def mock_get_mongo_db():
    print("Hello")
    return mongomock.MongoClient().db  # Returns a mock database


@pytest.fixture(scope="module")
def monkeymodule():
    with pytest.MonkeyPatch.context() as mp:
        yield mp


@pytest.fixture(scope="module")
def mock_mongo():
    mock_client = mongomock.MongoClient()
    mock_db = mock_client[config.settings.MONGO_DEFAULT_DATABASE]
    return mock_db


@pytest.mark.asyncio
async def test_create_notification(monkeymodule, mock_mongo):  # Pass the fixture here

    monkeymodule.setattr(
        "app.database.database_storage.mongo_db_client",
        mock_mongo.client,
    )

    from app.routes.ocr_route import create_notification

    # Use the monkeymodule fixture to patch the function
    monkeymodule.setattr(
        "app.database.database_storage.get_mongo_db",
        mock_get_mongo_db,
    )

    from app.crud.mongo.base_repository import BaseRepository

    monkeymodule.setattr(
        "app.utils.notification_utils._MONGO_REPO_NOTIFICATION",
        BaseRepository("notifications"),
    )

    # Test data
    user_id = "test_id"
    user_name = "Test"
    title = "Test Notification"
    description = "This is a test notification"
    url = "http://example.com"

    # Call the function with mocked DB
    result_id, notification_data = create_notification(
        user_id=user_id,
        user_name=user_name,
        title=title,
        description=description,
        url=url,
    )

    # Assertions
    assert result_id is not None
    assert notification_data["user_id"] == user_id
    assert notification_data["user_name"] == user_name
    assert notification_data["title"] == title
    assert notification_data["description"] == description
    assert notification_data["resource_url"] == url
