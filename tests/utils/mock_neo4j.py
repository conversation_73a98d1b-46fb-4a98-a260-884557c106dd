import copy
from app.crud.neo4j.schema import Node, Relationship


def mock_neo4j():
    nodes_and_relation = {}

    def mock_add_nodes(nodes: Node):
        for node in nodes:
            if node.properties["name"] not in nodes_and_relation:
                nodes_and_relation[f'{node.label}_{node.properties["name"]}'] = {}

    def mock_add_relationship(relations: list[Relationship]):
        for relation in relations:
            if (
                relation.rel_type
                in nodes_and_relation[
                    f'{relation.node1.label}_{relation.node1.properties["name"]}'
                ]
            ):
                nodes_and_relation[
                    f'{relation.node1.label}_{relation.node1.properties["name"]}'
                ][relation.rel_type].append(
                    f'{relation.node2.label}_{relation.node2.properties["name"]}',
                )
            else:
                nodes_and_relation[
                    f'{relation.node1.label}_{relation.node1.properties["name"]}'
                ] = {
                    relation.rel_type: [
                        f'{relation.node2.label}_{relation.node2.properties["name"]}',
                    ],
                }

    def mock_get_db():
        return copy.deepcopy(nodes_and_relation)

    return {
        "mock_add_nodes": mock_add_nodes,
        "mock_add_relationship": mock_add_relationship,
        "mock_get_db": mock_get_db,
    }
