import pytest
from app.crud import graph_generator
from tests.data.data_create_chunk_from_page import (
    join_related_chunks_between_pages_args,
)
from app.crud.neo4j.schema import Node


def counter():
    count = 0

    def increment(x, y, z):
        nonlocal count
        count += 1

    def get_count():
        return count

    return (increment, get_count)


@pytest.mark.parametrize("prev_node, cur_node", join_related_chunks_between_pages_args)
@pytest.mark.proper
def test_is_related(monkeypatch, get_mock_neo4j, prev_node, cur_node):
    monkeypatch.setattr(
        "app.crud.graph_generator.add_relationship",
        get_mock_neo4j["mock_add_relationship"],
    )
    get_mock_neo4j["mock_add_nodes"]([prev_node, cur_node])
    assert graph_generator.is_related(prev_node, cur_node) is True


# @pytest.mark.parametrize("prev_node, cur_node", join_related_chunks_between_pages_args)
@pytest.mark.proper
def test_get_title_header_text(
    monkeypatch, get_mock_neo4j, get_layout_data, get_file_name,
):
    document = Node("DOCUMENT", {"name": get_file_name})
    monkeypatch.setattr(
        "app.crud.graph_generator.add_relationship",
        get_mock_neo4j["mock_add_relationship"],
    )
    monkeypatch.setattr(
        "app.crud.graph_generator.add_nodes",
        get_mock_neo4j["mock_add_nodes"],
    )
    monkeypatch.setattr(
        "app.crud.graph_generator.get_last_node",
        lambda node, type: None,
    )
    get_mock_neo4j["mock_add_nodes"]([document])

    graph_generator.get_title_header_text(None, get_layout_data, document)
    db = get_mock_neo4j["mock_get_db"]()
    assert (
        len(db)
        == len(
            list(
                filter(
                    lambda x: "LAYOUT_TEXT" in x
                    or "LAYOUT_TITLE" in x
                    or "LAYOUT_SECTION_HEADER" in x,
                    get_layout_data,
                ),
            ),
        )
        - 1
    )

    assert (
        len(
            list(
                filter(
                    lambda x: "TITLE" in x,
                    db.keys(),
                ),
            ),
        )
        == 5
    )


@pytest.mark.proper
def test_do_create_chunk_from_page(monkeypatch, get_layout_data, get_file_name):
    increment, get_count = counter()
    monkeypatch.setattr(
        "app.crud.graph_generator.get_title_header_text",
        increment,
    )
    graph_generator.do_create_chunk_from_page(get_layout_data, None, get_file_name)

    assert get_count() == 1


@pytest.mark.proper
def test_convert_document_to_title_header_text(
    monkeypatch, get_layout_data, get_file_name,
):
    increment, get_count = counter()
    monkeypatch.setattr(
        "app.crud.graph_generator.do_create_chunk_from_page",
        increment,
    )
    graph_generator.convert_document_to_title_header_text(
        get_layout_data, None, get_file_name,
    )

    assert get_count() == 1
