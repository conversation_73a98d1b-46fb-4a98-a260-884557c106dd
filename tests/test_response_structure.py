import pytest
from unittest.mock import patch
from app.core.inspector_core import chat
from app.crud.inspector_repository import InspectorRepository
from neo4j.exceptions import ConfigurationError


def mock_inspector_function(message, messages):
    responses = {
        'hi': {
            'answer': 'Hello! How can I assist you today? If you have any questions or need information, feel free to ask!',
            'structured_content': [],
        },
        'list documents': {
            'answer': 'Here are the documents listed in the database:\n\n1. PEG24-7743 Serene Preserve MPUD Approval Pkt.pdf\n2. PEG24-7737 Orange State MPUD Sub Mod Approval Pkt.pdf\n\nThe second part of the result is an empty list, indicating there are no additional nodes collected.',
            'structured_content': [],
        },
        'give data about 50 feet': {
            'answer': 'Please take a look on the MPUD previewer',
            'structured_content': [
                {
                    'title': 'NOTICE OF PUBLIC HEARINGS ON REZONINGS FOR PROPERTIES LOCATED IN PASCO COUNTY',
                    'meta_data': {
                        'file_name': 'MPUD Records/MPUD Records/MPUD-2023-00015/PEG24-7743 Serene Preserve MPUD Approval Pkt.pdf',
                        'page_no': 30,
                        'coordinates': "{'Width': 0.23268777132034302, 'Height': 0.016248127445578575, 'Left': 0.6320480704307556, 'Top': 0.10292995721101761}",
                    },
                    'conditions': [
                        {
                            'text': '1. Blanton Creek Dev Corp/Timber Ridge MPUD RZ-7639 petition\nfor a rezoning from a R-3 Medium Density Residential District to a\nMPUD Master Planned Unit Development District for a parcel located\non the west side of 14th Street, approximately 1350 feet north of Long\nAvenue aka 15845 14th Street.',
                            'meta_data': {
                                'file_name': 'MPUD Records/MPUD Records/MPUD-2023-00015/PEG24-7743 Serene Preserve MPUD Approval Pkt.pdf',
                                'page_no': 30,
                                'coordinates': "{'Width': 0.2716456949710846, 'Height': 0.03405546024441719, 'Left': 0.6201328039169312, 'Top': 0.20946773886680603}",
                            },
                        },
                    ],
                },
                {
                    'title': 'Land Use',
                    'meta_data': {
                        'file_name': 'MPUD Records/MPUD Records/MPUD-2023-00015/PEG24-7743 Serene Preserve MPUD Approval Pkt.pdf',
                        'page_no': 20,
                        'coordinates': "{'Width': 0.07949712127447128, 'Height': 0.011136974208056927, 'Left': 0.4611838459968567, 'Top': 0.5032219290733337}",
                    },
                    'conditions': [
                        {
                            'text': '4\nMaximum Building Height of 50 Feet*',
                            'meta_data': {
                                'file_name': 'MPUD Records/MPUD Records/MPUD-2023-00011/PEG24-7737 Orange State MPUD Sub Mod Approval Pkt.pdf',
                                'page_no': 22,
                                'coordinates': "{'Width': 0.35202497243881226, 'Height': 0.014184543862938881, 'Left': 0.20614540576934814, 'Top': 0.12344197928905487}",
                            },
                        },
                    ],
                },
            ],
        },
    }
    return responses.get(message, {'answer': 'I don\'t understand that query.', 'structured_content': []})


@pytest.fixture
def mock_inspector_repository():
    with patch.object(InspectorRepository, 'inspector', side_effect=mock_inspector_function) as mock:
        yield mock


def test_chat_hi(mock_inspector_repository):
    result = chat([], 'hi')
    assert result == {
        'answer': 'Hello! How can I assist you today? If you have any questions or need information, feel free to ask!',
        'structured_content': [],
    }
    mock_inspector_repository.assert_called_once_with('hi', [])


def test_chat_list_documents(mock_inspector_repository):
    result = chat([], 'list documents')
    assert result == {
        'answer': 'Here are the documents listed in the database:\n\n1. PEG24-7743 Serene Preserve MPUD Approval Pkt.pdf\n2. PEG24-7737 Orange State MPUD Sub Mod Approval Pkt.pdf\n\nThe second part of the result is an empty list, indicating there are no additional nodes collected.',
        'structured_content': [],
    }
    mock_inspector_repository.assert_called_once_with('list documents', [])


def test_chat_give_data_about_50_feet(mock_inspector_repository):
    result = chat([], 'give data about 50 feet')
    assert result['answer'] == 'Please take a look on the MPUD previewer'
    assert len(result['structured_content']) == 2
    assert result['structured_content'][0]['title'] == 'NOTICE OF PUBLIC HEARINGS ON REZONINGS FOR PROPERTIES LOCATED IN PASCO COUNTY'
    assert result['structured_content'][1]['title'] == 'Land Use'
    mock_inspector_repository.assert_called_once_with('give data about 50 feet', [])


def test_chat_unknown_query(mock_inspector_repository):
    result = chat([], 'unknown query')
    assert result == {'answer': 'I don\'t understand that query.', 'structured_content': []}
    mock_inspector_repository.assert_called_once_with('unknown query', [])


def test_chat_neo4j_configuration_error(mock_inspector_repository):
    mock_inspector_repository.side_effect = ConfigurationError("Neo4j connection error")
    result = chat([], 'test query')
    assert result == {'error': 'Neo4j configuration error: Neo4j connection error'}
    mock_inspector_repository.assert_called_once_with('test query', [])


if __name__ == '__main__':
    pytest.main()
