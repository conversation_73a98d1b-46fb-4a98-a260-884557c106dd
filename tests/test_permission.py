import pytest
from fastapi.testclient import TestClient
from app.main import get_application, EnvEnum
import mongomock
from app.core import config
from app.models.user_model import Roles
from datetime import datetime, timezone
from app.crud.mongo.base_repository import BaseRepository


@pytest.fixture(scope="module")
def monkeymodule():
    with pytest.MonkeyPatch.context() as mp:
        yield mp


@pytest.fixture(scope="module")
def mock_mongo():
    mock_client = mongomock.MongoClient()
    mock_db = mock_client[config.settings.MONGO_DEFAULT_DATABASE]

    # Initialize user_config collection with test data
    user_config_collection = mock_db["user_config"]
    user_config_collection.insert_many([
        {
            "role": "admin",
            "permissions": {
                "viewUsers": True,
                "editUser": True,
                "manageRoles": False,
                "createCoa": True,
                "viewCoa": True,
                "createApplication": True,
                "viewApplication": True,
                "viewKnowledgeBase": True,
                "uploadKnowledgeBase": True,
            },
            "updated_at": datetime.now(timezone.utc),
        },
        {
            "role": "drafter",
            "permissions": {
                "viewUsers": True,
                "editUser": False,
                "manageRoles": False,
                "createCoa": True,
                "viewCoa": True,
                "createApplication": True,
                "viewApplication": True,
                "viewKnowledgeBase": False,
                "uploadKnowledgeBase": False,
            },
            "updated_at": datetime.now(timezone.utc),
        },
    ])
    return mock_db


@pytest.fixture(scope="module")
def patched_app(mock_mongo, monkeymodule):
    monkeymodule.setattr(
        "app.database.database_storage.mongo_db_client",
        mock_mongo.client,
    )

    def patched_get_mongo_db():
        return mock_mongo

    monkeymodule.setattr(
        "app.database.database_storage.get_mongo_db",
        patched_get_mongo_db,
    )

    # Mock the base repository
    monkeymodule.setattr(
        "app.utils.userconfig_utils._MONGO_REPO_USER_CONFIG",
        BaseRepository("user_config"),
    )

    app = get_application(env=EnvEnum.TEST)

    @app.middleware("http")
    async def auth_middleware(request, call_next):
        claims = {
            "cognito:username": "Testing",
            "email": "<EMAIL>",
            "custom:county": "Test County",
        }
        request.state.user = claims
        return await call_next(request)

    test_client = TestClient(app)
    return test_client


def test_get_all_permissions(patched_app):
    response = patched_app.get(
        "/api/permissions",
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "data" in data
    assert "permissions" in data["data"]
    assert data["message"] == "Permissions retrieved successfully"


@pytest.mark.parametrize(
    "role, expected_status_code",
    [
        ("admin", 200),
        ("drafter", 200),
    ],
)
def test_get_permissions_by_role(patched_app, role, expected_status_code):
    response = patched_app.get(
        f"/api/permissions/{role}",
        headers={"Authorization": "Bearer test"},
    )
    print("Response: -------------------------------->")
    print(response.json())
    assert response.status_code == expected_status_code

    if expected_status_code == 200:
        data = response.json()
        assert "message" in data
        assert data["message"] == "Role permissions retrieved successfully"
        assert "data" in data
        assert data["data"]["role"] == role
        assert isinstance(data["data"]["permissions"], dict)


@pytest.mark.parametrize(
    "role, permissions, expected_status_code",
    [
        ("admin", {
            "viewUsers": True,
            "editUser": True,
            "manageRoles": False,
            "createCoa": False,
            "viewCoa": True,
            "createApplication": True,
            "viewApplication": True,
            "viewKnowledgeBase": True,
            "uploadKnowledgeBase": True,
        }, 200),
        ("superadmin", {
            "viewUsers": True,
            "editUser": True,
            "manageRoles": True,
            "createCoa": True,
            "viewCoa": True,
            "createApplication": True,
            "viewApplication": True,
            "viewKnowledgeBase": True,
            "uploadKnowledgeBase": True,
        }, 403),
    ],
)
def test_update_permissions(patched_app, role, permissions, expected_status_code):
    response = patched_app.put(
        f"/api/permissions/{role}",
        json={"permissions": permissions},
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == expected_status_code

    if expected_status_code == 200:
        data = response.json()
        assert "message" in data
        assert data["message"] == "Role permissions updated successfully"


def test_get_permissions_nonexistent_role(patched_app):
    response = patched_app.get(
        "/api/permissions/nonexistent",
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 422  # FastAPI validation error for invalid enum


@pytest.mark.parametrize(
    "invalid_permissions, expected_status_code",
    [
        ({"permissions": None}, 422),  # None permissions
        ({"wrong_key": {}}, 422),  # Wrong key
        ({}, 422),  # Empty body
        # Invalid boolean values
        ({"permissions": {
            "viewUsers": "not_a_boolean",
            "editUser": True,
            "manageRoles": False,
            "createCoa": True,
            "viewCoa": True,
            "createApplication": True,
            "viewApplication": True,
            "viewKnowledgeBase": True,
            "uploadKnowledgeBase": True,
        }}, 422),
    ],
)
def test_update_permissions_invalid(patched_app, invalid_permissions, expected_status_code):
    response = patched_app.put(
        f"/api/permissions/{Roles.admin}",
        json=invalid_permissions,
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == expected_status_code


def test_initialize_default_permissions(patched_app):
    response = patched_app.get(
        "/api/permissions",
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 200
    data = response.json()
    permissions = data["data"]["permissions"]

    # Check if at least some default roles exist
    roles = [p["role"] for p in permissions]
    assert "admin" in roles
    assert "drafter" in roles
