import pytest
from app.core.function_tracer import FunctionTracer


async def sample_async_function(x, y):
    return x + y


def sample_sync_function(a, b):
    return a * b


def sample_sync_function_with_exception(a, b):
    raise Exception("This is a sample exception")


async def another_async_function(a, b):
    return a * b


async def combined_async_function():
    result1 = await sample_async_function(1, 2)
    result2 = await another_async_function(3, 4)
    return result1, result2


@pytest.mark.asyncio
async def test_set_check_functions():
    # Create an instance of FunctionTracer
    tracer = FunctionTracer()

    # Define the functions to check and stop
    check_functions = ['sample_async_function', 'sample_sync_function']
    stop_functions = ['sample_sync_function']

    # Call the set_check_functions method with the async function
    result_async = await tracer.set_check_functions(
        function_name=sample_async_function,
        kargs={'x': 1, 'y': 2},
        check_functions=check_functions,
        stop_functions=stop_functions,
    )
    assert result_async == 3

    # Call the set_check_functions method with the sync function
    result_sync = await tracer.set_check_functions(
        function_name=sample_sync_function,
        kargs={'a': 3, 'b': 4},
        check_functions=check_functions,
        stop_functions=stop_functions,
    )
    assert result_sync == 12

    # Check that the functions were traced correctly
    assert len(tracer.functions) == 2
    assert tracer.functions[0].name == 'sample_async_function'
    assert tracer.functions[0].status == 'returned'
    assert tracer.functions[1].name == 'sample_sync_function'
    assert tracer.functions[1].status == 'returned'


@pytest.mark.asyncio
async def test_set_check_functions_with_multiple_calls():
    # Create an instance of FunctionTracer
    tracer = FunctionTracer()

    # Define the functions to check
    check_functions = ['sample_async_function', 'another_async_function']
    stop_functions = []

    # Call the set_check_functions method with the combined async function
    result = await tracer.set_check_functions(
        function_name=combined_async_function,
        kargs={},
        check_functions=check_functions,
        stop_functions=stop_functions,
    )
    assert result == (3, 12)

    # Check that the functions were traced correctly
    assert len(tracer.functions) == 2
    assert tracer.functions[0].name == 'sample_async_function'
    assert tracer.functions[0].status == 'returned'
    assert tracer.functions[1].name == 'another_async_function'
    assert tracer.functions[1].status == 'returned'

if __name__ == "__main__":
    pytest.main([__file__])
