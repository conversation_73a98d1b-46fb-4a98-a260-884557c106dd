import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from app.core.lightragv2.lightrag_fine_tuned import CustomRag


@pytest.fixture
def custom_rag():
    class TestCustomRag(CustomRag):
        def __init__(self):
            # Add required attributes
            self.working_dir = "./temp"
            self.embedding_cache_config = {"cache_dir": "./temp/cache", "use_cache": False}
            self.node2vec_params = {"dimensions": 128}
            self.llm_model_kwargs = {"model": "gpt-4"}
            self.vector_db_storage_cls_kwargs = {"collection_name": "test"}
            self.graph_storage_cls_kwargs = {"url": "test"}
            self.addon_params = {}
            self.embedding_threshold = 0.5
            self.top_k = 10
            # Mock methods and attributes
            self.embedding_func = MagicMock()
            self.super_aquery = AsyncMock()
            self.llm_model_func = AsyncMock()
            self.full_docs = MagicMock()
            self.key_string_value_json_storage_cls = MagicMock()
            self.lightrag_batch = MagicMock()
            self.entities_vdb = MagicMock()
            self.chunks_vdb = MagicMock()
            self.relationships_vdb = MagicMock()
            self.chunk_entity_relation_grapch = MagicMock()
    rag = TestCustomRag()
    return rag


@pytest.mark.asyncio
async def test_stream_query_success(custom_rag):
    """Test successful query"""
    # Setup mock responses
    mock_results = [{"text": {"data": "test content", "coordinates": {}, "page": 1}, "file_name": "test.pdf"}]
    custom_rag.super_aquery.return_value = mock_results
    custom_rag.llm_model_func.return_value = '{"data": {"oneline": ["Test summary"], "score": [85]}}'
    with patch('app.core.function_tracer.FunctionTracer') as MockTracer:
        mock_tracer = MagicMock()
        mock_task = MagicMock()
        mock_tracer.start_trace.return_value = mock_task
        mock_task.done.side_effect = [False, True]
        mock_function = MagicMock()
        mock_function.name = "super_aquery"
        mock_function.status = "returned"
        mock_function.output = MagicMock()
        mock_function.output.value = mock_results
        mock_tracer.functions = [mock_function]
        MockTracer.return_value = mock_tracer
        results = []
        async for response in custom_rag.stream_query("test query"):
            results.append(response)
        assert len(results) > 0


@pytest.mark.asyncio
async def test_stream_query_error_handling(custom_rag):
    """Test error handling"""
    custom_rag.super_aquery.side_effect = Exception("test error")
    with patch('app.core.function_tracer.FunctionTracer') as MockTracer:
        mock_tracer = MagicMock()
        mock_task = MagicMock()
        mock_tracer.start_trace.return_value = mock_task
        mock_task.done.return_value = True
        mock_function = MagicMock()
        mock_function.status = "error"
        mock_tracer.functions = [mock_function]
        MockTracer.return_value = mock_tracer
        results = []
        async for response in custom_rag.stream_query("test query"):
            results.append(response)
        assert len(results) == 1


@pytest.mark.asyncio
async def test_stream_query_no_results(custom_rag):
    """Test no results case"""
    custom_rag.super_aquery.return_value = []
    with patch('app.core.function_tracer.FunctionTracer') as MockTracer:
        mock_tracer = MagicMock()
        mock_task = MagicMock()
        mock_tracer.start_trace.return_value = mock_task
        mock_task.done.return_value = True
        mock_function = MagicMock()
        mock_function.status = "returned"
        mock_function.name = "super_aquery"
        mock_function.output = MagicMock()
        mock_function.output.value = []
        mock_tracer.functions = [mock_function]
        MockTracer.return_value = mock_tracer
        results = []
        async for response in custom_rag.stream_query("test query"):
            results.append(response)
        assert len(results) == 1
        assert results[0]["answer"] == "No conditions found!"
