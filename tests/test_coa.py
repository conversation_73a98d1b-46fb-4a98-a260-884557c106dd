import pytest
from fastapi.testclient import TestClient
from app.main import get_application, EnvEnum
from datetime import date, datetime
import mongomock
from app.core import config


@pytest.fixture(scope="module")
def monkeymodule():
    with pytest.MonkeyPatch.context() as mp:
        yield mp


@pytest.fixture(scope="module")
def mock_mongo():
    mock_client = mongomock.MongoClient()
    mock_db = mock_client[config.settings.MONGO_DEFAULT_DATABASE]
    return mock_db


@pytest.fixture(scope="module")
def patched_app(mock_mongo, monkeymodule):
    monkeymodule.setattr(
        "app.database.database_storage.mongo_db_client",
        mock_mongo.client,
    )

    # Patch the get_mongo_db function
    def patched_get_mongo_db():
        return mock_mongo

    monkeymodule.setattr(
        "app.database.database_storage.get_mongo_db",
        patched_get_mongo_db,
    )

    from app.crud.mongo.base_repository import BaseRepository

    monkeymodule.setattr(
        "app.utils.application_utils._MONGO_REPO_APPLICATION",
        BaseRepository("applications"),
    )

    # Patch the settings to use a test database name
    monkeymodule.setattr(config.settings, "MONGO_DEFAULT_DATABASE", "test_db")

    app = get_application(env=EnvEnum.TEST)

    @app.middleware("http")
    async def auth_middleware(request, call_next):
        claims = {
            "cognito:username": "Testing",
            "email": "<EMAIL>",
            "custom:county": "Test County",
        }
        request.state.user = claims
        return await call_next(request)

    test_client = TestClient(app)

    return test_client


@pytest.fixture(scope="module")
def created_coa(patched_app):
    """Creates a COA and returns its ID for use in other tests."""
    request_body = {
        "title": "Test COA",
        "summary": "Test COA Summary",
        "priority": "mid",
        "due_date": datetime.combine(date.today(), datetime.min.time()).isoformat(),
        "assignee": "user123",
        "file_name": "testfile.pdf",
        "s3_url": "https://example.com/testfile.pdf",
        "coordinates": [12.34, 56.78],
        "page_numbers": [1, 2, 3],
        "accepting_user_id": "user456",
        "application_id": "app123",
        "chat_id": "",
    }

    response = patched_app.post(
        "/api/coa",
        json=request_body,
        headers={"Authorization": "Bearer test"},
    )

    assert response.status_code == 200
    data = response.json()
    return data["insert_id"]  # Use this COA ID in other tests


# Test COA creation route
def test_create_coa(patched_app):
    request_body = {
        "title": "Test COA Creation",
        "summary": "Creating a new COA",
        "priority": "high",
        "due_date": datetime.combine(date.today(), datetime.max.time()).isoformat(),
        "assignee": "user456",
        "file_name": "newfile.pdf",
        "s3_url": "https://example.com/newfile.pdf",
        "coordinates": [98.76, 54.32],
        "page_numbers": [5, 6, 7],
        "accepting_user_id": "user789",
        "application_id": "app456",
        "chat_id": "",
    }

    response = patched_app.post(
        "/api/coa",
        json=request_body,
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 200
    data = response.json()
    assert "insert_id" in data
    assert "coa" in data


# Test fetching all COAs for a specific application
def test_get_all_coas(patched_app):
    application_id = "app123"  # Replace with an actual application ID
    response = patched_app.get(
        f"/api/coas?application_id={application_id}",
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 200
    data = response.json()
    assert "total_coas" in data
    assert "coas" in data


# Test updating fields in a COA
@pytest.mark.parametrize(
    "update_payload, expected_status_code",
    [
        ({"title": "Updated COA Title"}, 200),
    ],
)
def test_update_coa(
    created_coa,
    update_payload,
    expected_status_code,
    patched_app,
):
    coa_id = created_coa
    response = patched_app.patch(
        f"/api/coa/{coa_id}",
        json=update_payload,
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == expected_status_code

    if expected_status_code == 200:
        result = response.json()
        assert result == {'message': 'Fields updated successfully'}
        # assert result["nModified"] == 1  # Assuming successful update


# Test deleting a COA
def test_delete_coa(created_coa, patched_app):
    coa_id = created_coa
    response = patched_app.delete(
        f"/api/coa/{coa_id}",
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == f"COA with id {coa_id} has been deleted."


# Test trying to fetch a deleted COA (should return 404)
def test_fetch_deleted_coa(created_coa, patched_app):
    application_id = "app123"  # Replace with an actual application ID
    coa_id = created_coa

    response = patched_app.get(
        f"/api/coas?application_id={application_id}",
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 200
    data = response.json()
    assert "total_coas" in data
    assert "coas" in data
    assert len(list(filter(lambda x: x["id"] == coa_id, data["coas"]))) == 0
