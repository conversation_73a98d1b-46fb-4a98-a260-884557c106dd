import pytest
from datetime import datetime
from unittest.mock import patch, MagicMock
from io import Bytes<PERSON>
from bson import ObjectId
import mongomock
from app.crud.gen_pdf_repository import create_pdf
from app.core.config import Settings

settings = Settings()

# Test data based on the provided paste.txt structure
TEST_DATA = {
    "mpud_info": {
        "title": "Project Arthur MPUD",
        "request": "Exchange 115 Single Family Detached Dwelling Unit Entitlements for 704 Charter School Student Entitlements",
        "mpud category": "zoning-amendment",
        "developer": "Len-Angeline, LLC",
        "reference": "LDC Section 402.2 and Section 522",
        "petition_no": "MPUD-2023-00015",
        "commission_district": "1",
    },
    "conditions": {
        "access management": [
            {
                "point": "The developer shall provide a secondary functional access and emergency access to each increment.",
            }
        ],
        "dedication of right-of-way": [
            {
                "point": "In light of the wetlands to the west and north, no street connections and rights-of-way to adjoining areas shall be required.",
            }
        ]
    }
}


class MockMongoRepo:
    def __init__(self, collection):
        self.collection = collection
        self.collection_mock = mongomock.MongoClient().db[collection]

    def update_one_by_query_upsert(self, query, fields, db=None):
        return self.collection_mock.update_one(query, {"$set": fields}, upsert=True)


_MONGO_REPO_APPLICATION = MockMongoRepo("applications")


@pytest.fixture(autouse=True)
def mock_mongo_repo(monkeypatch):
    monkeypatch.setattr('app.utils.application_utils._MONGO_REPO_APPLICATION', _MONGO_REPO_APPLICATION)


@pytest.fixture
def mock_datetime():
    with patch('app.crud.gen_pdf_repository.datetime') as mock_dt:
        mock_dt.now.return_value = datetime(2025, 2, 6, 12, 0)
        yield mock_dt


@patch('boto3.client')
@patch('app.utils.application_utils.update_application_pdf_s3_location')
def test_create_pdf(mock_update, mock_boto_client, mock_datetime, db):
    # Create a mock S3 client
    mock_s3_instance = MagicMock()
    mock_boto_client.return_value = mock_s3_instance

    # Prepare test data
    chat_id = "test_chat_123"
    application_id = str(ObjectId())
    expected_bucket = f'pasco-ocr-files-{settings.STAGE}'
    expected_key = f"MPUD-version_control_files/{chat_id}/02-06-2025-12:00.pdf"

    # Call the function being tested
    buffer = create_pdf(TEST_DATA, chat_id=chat_id, application_id=application_id)

    # Basic assertions
    assert isinstance(buffer, BytesIO)

    # Verify S3 upload
    mock_s3_instance.upload_fileobj.assert_called_once()
    call_args = mock_s3_instance.upload_fileobj.call_args[0]

    # Check upload parameters
    uploaded_buffer = call_args[0]
    uploaded_bucket = call_args[1]
    uploaded_key = call_args[2]

    assert isinstance(uploaded_buffer, BytesIO)
    assert uploaded_bucket == expected_bucket
    assert uploaded_key == expected_key

    # Verify application update
    mock_update.assert_called_once_with(
        application_id,
        f"s3://{expected_bucket}/{expected_key}"
    )

    # Verify PDF content
    pdf_content = buffer.getvalue()
    assert len(pdf_content) > 0  # Basic check that PDF has content
