# import pytest
# from starlette.testclient import TestClient
# from app.crud.ocr import Textract

# ocr = Textract()


# @pytest.mark.asyncio
# async def test_read_root(client: TestClient, db):
#     response = client.get("/")
#     assert response.status_code == 200
#     assert response.json() == {"message": "Hey!, welcome to the connector"}


# @pytest.mark.asyncio
# async def test_mock_mongoDb(client: TestClient, db):
#     assert db['dummy'].find_one({}) is None


# @pytest.mark.asyncio
# async def test_mock_milvus(client: TestClient, db, mock_milvus) :
#     collName = 'testCollection'
#     if not mock_milvus.has_collection(collName) :
#         resp1 = mock_milvus.create_collection(collName, fields=[{"name": "field1", "type": "float"}])

#     assert resp1 == {'message': f'Create collection failed: collection {collName} exist'}
#     assert mock_milvus.has_collection(collName) is True
