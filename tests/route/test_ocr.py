import pytest
from starlette.testclient import TestClient
from app.crud.ocr.main import Textract
from fastapi import UploadFile
from unittest.mock import patch
import json
import io

from app.models.document import UploadStatus

# @pytest.mark.asyncio
# async def test_extract_file_and_store_content(client: TestClient, db, mock_milvus):
#     file_path = "pasco-ocr-files-dev/test_folder/testFile.pdf" # "pasco-ocr-files-dev/PEG24-0358 Oakley Place MPUD LUEM Exch Approval Pkt.pdf"  # Use any file available in s3 bucket in pasco account
#     response = client.post("/api/extract_file_and_store_content", params={"s3Location": file_path})
#     coll_name_list = []
#     for collection_name in db.list_collection_names() :
#         coll_name_list.append(collection_name)

#     print('response-->',response)
#     print('coll_name_list',coll_name_list)
#     assert response.status_code == 200
#     assert response.json() == {"status": 'ok', "message": "extract running in background"}
#     assert ['files_status', 'files_data'] == coll_name_list


# @pytest.mark.asyncio
# async def test_extract_pdf_wrong_path_fail(client: TestClient, db, mock_milvus):
#     resp = ocr.extract_pdf('pasco-ocr-files-dev', 'text.pdf', db)
#     assert 'unable to get object metadata from s3' in resp['message'].lower()


# @pytest.mark.asyncio
# async def test_extract_pdf_correct_path(client: TestClient, db, mock_milvus):
#     resp = ocr.extract_pdf('pasco-ocr-files-dev', 'PEG24-0358 Oakley Place MPUD LUEM Exch Approval Pkt.pdf', db)
#     assert resp['status'] == 'ok'
#     assert resp['message'] == 'data stored to milvus'


# @pytest.mark.asyncio
# async def test_fetch_file_and_store(client: TestClient, db, mock_milvus):
#     response = client.post("/api/fetch_file_and_store", params={"s3Bucket": "TestBucket"})
#     assert response.status_code == 200
#     assert response.json() == {"status": 'ok', 'message': 'running ocr for new files in bucket, it can take a few minutes ... '}


# @pytest.mark.asyncio
# async def test_file_and_store_wrong_bucket(client: TestClient, db) :
#     resp = ocr.loop_files_in_s3Bucket('testBucket', db)
#     assert resp == {'message': 'Error in fetching file from s3 Bucket'}

@pytest.mark.asyncio
async def test_sample(client: TestClient, db) :
    ocr = Textract()
    with open("tests/Files/dummyBlocks.json", 'r') as file:
        data = json.load(file)
    db['files_data'].insert_one(data)
    resp = ocr.find_text_coordinates(1, "RECOMMENDED", "pasco-ocr-files-dev/test_folder/testFile.pdf", db, True)
    assert resp == {'RECOMMENDED': {'top_left': {'X': 0.1197294294834137, 'Y': 0.3891264796257019}, 'top_right': {'X': 0.27673643827438354, 'Y': 0.38919976353645325}, 'bottom_right': {'X': 0.2767352759838104, 'Y': 0.40114885568618774}, 'bottom_left': {'X': 0.11972774565219879, 'Y': 0.4010753333568573}}}

    resp = ocr.find_text_coordinates(1, "RECOMMENDED PASC", "pasco-ocr-files-dev/test_folder/testFile.pdf", db, True)
    assert resp == {'RECOMMENDED': {'top_left': {'X': 0.1197294294834137, 'Y': 0.3891264796257019}, 'top_right': {'X': 0.27673643827438354, 'Y': 0.38919976353645325}, 'bottom_right': {'X': 0.2767352759838104, 'Y': 0.40114885568618774}, 'bottom_left': {'X': 0.11972774565219879, 'Y': 0.4010753333568573}}, 'PASC': {'top_left': {'X': 0.0738723948597908, 'Y': 0.030308188870549202}, 'top_right': {'X': 0.2209361344575882, 'Y': 0.030370498076081276}, 'bottom_right': {'X': 0.22093252837657928, 'Y': 0.062347255647182465}, 'bottom_left': {'X': 0.07386747002601624, 'Y': 0.062284380197525024}}}

    resp = ocr.find_text_coordinates(1, "RECOMMENDED BOARD ACTION:", "pasco-ocr-files-dev/test_folder/testFile.pdf", db, True)
    assert resp == {'RECOMMENDED BOARD ACTION:': {'top_left': {'X': 0.11972944438457489, 'Y': 0.3890552818775177}, 'top_right': {'X': 0.44083917140960693, 'Y': 0.3892051577568054}, 'bottom_right': {'X': 0.4408385455608368, 'Y': 0.4013408422470093}, 'bottom_left': {'X': 0.1197277307510376, 'Y': 0.40119048953056335}}}


@pytest.mark.asyncio
async def test_upload(client: TestClient, db) :

    def mock_upload_fileobj(self, fileobj, bucket, key, ExtraArgs):
        print(f"Mock upload to {bucket}/{key}")

    class MockS3Client:
        def upload_fileobj(self, fileobj, bucket, key, ExtraArgs):
            mock_upload_fileobj(self, fileobj, bucket, key, {'ContentType': 'application/pdf'})

    mock_textract = Textract()
    mock_s3_client = MockS3Client()

    with open("tests/Files/test_mpud.pdf", 'rb') as file:
        data = file.read()

    mock_file = UploadFile(filename="test_mpud.pdf", file=io.BytesIO(data))

    with patch.object(mock_textract, 's3Client', mock_s3_client):
        resp = mock_textract.upload_file_to_s3(mock_file, "test-s3bucket", "test-MPUD", "dummy.pdf", "12345678", "test-user", "test-county", "6ca910ed-445a-4d72", UploadStatus.UPLOADING , db)
        assert resp.acknowledged

    assert 'documents' in db.list_collection_names(), "'documents' collection does not exist"
    assert db['documents'].find_one({})['file_name'] == 'dummy.pdf'

