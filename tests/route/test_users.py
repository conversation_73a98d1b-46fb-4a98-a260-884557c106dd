import pytest
from unittest.mock import patch, MagicMock
from app.routes.users_route import get_all_cognito_users, cognito_client

# Mock data for users returned by Cognito
mock_user_response_1 = {
    "Users": [
        {
            "Attributes": [{"Name": "email", "Value": "<EMAIL>"}],
        },
    ],
    "PaginationToken": "token1",
}

mock_user_response_2 = {
    "Users": [
        {
            "Attributes": [{"Name": "email", "Value": "<EMAIL>"}],
        },
    ],
}


# Test function using unittest.mock.patch
@pytest.mark.asyncio
async def test_get_all_cognito_users():
    # Use AsyncMock to mock an async function
    with patch.object(cognito_client, 'list_users', side_effect=[
        mock_user_response_1,  # First call returns a response with PaginationToken
        mock_user_response_2,   # Second call returns a response without PaginationToken
    ]) as mock_list_users:
        # Mock current_user dependency (adjust as needed)
        mock_current_user = MagicMock()
        mock_current_user.id = 1

        # Call the actual function with the mock
        response = await get_all_cognito_users(attribute="email", current_user=mock_current_user)
        # Assert that cognito_client.list_users was called twice
        assert mock_list_users.call_count == 2

        # Check the returned data
        assert response == {"attribute": ["<EMAIL>", "<EMAIL>"]}
