import pytest
from fastapi import HTT<PERSON><PERSON>xception
from unittest.mock import AsyncMock

# Mock fixture for view_pdf_page


@pytest.fixture
def mock_view_pdf_page():
    async def mock_func(file_path: str, page_number: int):
        if page_number in [1, 2, 3]:
            mock_response = AsyncMock()
            mock_response.media_type = 'image/png'
            return mock_response
        raise HTTPException(status_code=400, detail="Invalid page number")
    return mock_func


@pytest.mark.asyncio
@pytest.mark.parametrize("page_number, should_return_response", [
    (1, True),
    (2, True),
    (3, True),
    (100, False),
    (200, False),
    (300, False),
])
async def test_view_pdf_page(mock_view_pdf_page, page_number, should_return_response):
    file_path = 'MPUD Records/MPUD Records/MPUD-2024-00021/2020-2-18 - Oakley Place MPUD (1).pdf'

    if should_return_response:
        response = await mock_view_pdf_page(file_path=file_path, page_number=page_number)
        assert response is not None
        assert response.media_type == 'image/png'
    else:
        with pytest.raises(HTTPException) as exc_info:
            await mock_view_pdf_page(file_path=file_path, page_number=page_number)
        assert exc_info.value.status_code == 400
        assert exc_info.value.detail == "Invalid page number"
