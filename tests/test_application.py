import pytest
from fastapi.testclient import TestClient
from app.main import get_application, EnvEnum
from datetime import date, datetime, timedelta
import mongomock
from app.core import config


@pytest.fixture(scope="module")
def monkeymodule():
    with pytest.MonkeyPatch.context() as mp:
        yield mp


@pytest.fixture(scope="module")
def mock_mongo():
    mock_client = mongomock.MongoClient()
    mock_db = mock_client[config.settings.MONGO_DEFAULT_DATABASE]
    return mock_db


@pytest.fixture(scope="module")
def patched_app(mock_mongo, monkeymodule):
    monkeymodule.setattr(
        "app.database.database_storage.mongo_db_client",
        mock_mongo.client,
    )

    # Patch the get_mongo_db function
    def patched_get_mongo_db():
        return mock_mongo

    monkeymodule.setattr(
        "app.database.database_storage.get_mongo_db",
        patched_get_mongo_db,
    )

    from app.crud.mongo.base_repository import BaseRepository

    monkeymodule.setattr(
        "app.utils.application_utils._MONGO_REPO_APPLICATION",
        BaseRepository("applications"),
    )

    # Patch the settings to use a test database name
    monkeymodule.setattr(config.settings, "MONGO_DEFAULT_DATABASE", "test_db")

    app = get_application(env=EnvEnum.TEST)

    @app.middleware("http")
    async def auth_middleware(request, call_next):
        claims = {
            "cognito:username": "Testing",
            "email": "<EMAIL>",
            "custom:county": "Test County",
        }
        request.state.user = claims
        return await call_next(request)

    test_client = TestClient(app)

    return test_client


@pytest.fixture(scope="module")
def created_application(
    patched_app,
):
    """Creates an application and returns its ID for use in other tests."""
    request_body = {
        "summary": "Test Application for Dependency",
        "assignee": "user123",
        "priority": "mid",
        "due_date": datetime.combine(date.today(), datetime.max.time()).isoformat(),
    }

    response = patched_app.post(
        "/api/application",
        json=request_body,
        headers={"Authorization": "Bearer test"},
    )

    assert response.status_code == 200
    data = response.json()
    return data["application_id"]  # Use this ID in other tests


@pytest.mark.parametrize(
    "summary, assignee, due_date, priority, expected_status_code",
    [
        (
            "Test Application 1",
            "user123",
            datetime.combine(date.today(), datetime.max.time()).isoformat(),
            "mid",
            200,
        ),
        (
            "Test Application 2",
            "",
            # Use tomorrow's date instead of utcnow()
            (datetime.utcnow() + timedelta(days=1)).isoformat(),
            "high",
            200,
        ),
        ("Test Application 3", "user123", None, "low", 200),
        ("Test Application 4", "user123", "invalid-date", "mid", 422),
    ],
)
def test_create_new_application(
    summary: str,
    assignee: str,
    due_date: str,
    priority: str,
    expected_status_code: int,
    patched_app,
):
    request_body = {"summary": summary, "assignee": assignee, "priority": priority}
    if due_date:
        request_body["due_date"] = due_date
    response = patched_app.post(
        "/api/application",
        json=request_body,
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == expected_status_code


@pytest.mark.parametrize(
    "invalid_due_date",
    [
        "invalid-date",
        "2024-13-01T00:00:00",
        "",
    ],  # Test invalid date formats and empty strings
)
def test_create_application_invalid_due_date(
    invalid_due_date,
    patched_app,
):
    request_body = {
        "summary": "Test Application with Invalid Date",
        "assignee": "user123",
        "priority": "mid",
        "due_date": invalid_due_date,
    }

    response = patched_app.post(
        "/api/application",
        json=request_body,
        headers={"Authorization": "Bearer test"},  # Mock JWT token
    )

    assert response.status_code == 422  # Unprocessable Entity (Invalid date format)


def test_create_application_without_due_date(
    patched_app,
):
    request_body = {
        "summary": "Test Application without Due Date",
        "assignee": "user123",
        "priority": "mid",
    }

    response = patched_app.post(
        "/api/application",
        json=request_body,
        headers={"Authorization": "Bearer test"},  # Mock JWT token
    )

    assert response.status_code == 200
    data = response.json()
    assert data["application"]["due_date"] is None


# Test the fetch all applications route
def test_fetch_all_applications(
    patched_app,
):
    response = patched_app.get(
        "/api/applications",
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 200
    data = response.json()
    assert "total_applications" in data
    assert "applications" in data


# Test fetching a specific application by ID using the application created in the fixture
def test_fetch_application_by_id(
    created_application,
    patched_app,
):
    application_id = created_application
    response = patched_app.get(
        f"/api/application/{application_id}",
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == application_id


# Test updating an application using the application created in the fixture
@pytest.mark.parametrize(
    "update_payload, expected_status_code",
    [
        ({"summary": "Updated summary"}, 200),
    ],
)
def test_update_application(
    created_application,
    update_payload,
    expected_status_code,
    patched_app,
):
    application_id = created_application
    response = patched_app.patch(
        f"/api/application/{application_id}",
        json=update_payload,
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == expected_status_code

    if expected_status_code == 200:
        result = response.json()
        assert result == {'message': 'Successfully updated'}
        # assert result["nModified"] == 1  # Assuming the update was successful


# Test deleting the created application
def test_delete_application(
    created_application,
    patched_app,
):
    application_id = created_application
    response = patched_app.delete(
        f"/api/application/{application_id}",
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "Application deleted successfully"


# Test trying to fetch the deleted application (should return 404)
def test_fetch_deleted_application(
    created_application,
    patched_app,
):
    application_id = created_application
    response = patched_app.get(
        f"/api/application/{application_id}",
        headers={"Authorization": "Bearer test"},
    )
    assert response.status_code == 404
    assert response.json()["detail"] == "Application not found"
