# Project Name

pasco-govtech-ai-connector

## Description

This project is designed to be the connector of the pasco-govtech-ai.

## Requirements

- Docker

## Installation

1. Clone the repository
2. Run `docker compose up`

   - To use the docker compose in reload mode use `docker compose up -w`
     - Requires Docker version 24.0.7 or later
     - Requires Docker Compose version: 2.25.0 or later

3. Access the application:

   - Once Docker containers are up and running, access the application at [http://localhost:8000](http://localhost:8000).

4. Access MongoDB and Milvus DB:

- MongoDB is accessible at [mongodb://localhost:27017](mongodb://localhost:27017).
- Milvus DB is accessible at [http://localhost:19121](http://localhost:19121).
- Attu is accessible at [http://localhost:5000](http://localhost:5000).
- Mongo GUI is accessible at [http://localhost:4321](http://localhost:4321).

5. Run pytest Test cases
   - To run it outside docker
     - pip3 install -r requirements.txt
     - export AWS_ACCESS_KEY_ID=your_aws_access_key_id_value
     - export AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key_value
     - pytest or pytest -s (to print things which gets printed)
   - To run it inside docker
     - docker exec -it fastapi-connector pytest

# To run the cloudformation

`aws cloudformation deploy     --template-file cloudformation/main.yaml     --stack-name pasco-backend-dev     --capabilities CAPABILITY_NAMED_IAM     --parameter-overrides $(jq -r '.[] | "\(.ParameterKey)=\(.ParameterValue)"' cloudformation/parameters.json)`

# To run tests

## Ensure the Docker container is running before executing tests! ⚠️

`docker exec -it fastapi-connector pytest --cov-fail-under=80 --no-cov-on-fail`
