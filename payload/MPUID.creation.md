## MPUID Version Data API

This README describes how to use the API endpoint for posting MPUID version data.

### Insert Rule

<b>POST - /api/versionControl/post_mpud_version_data </b>

<details>

<summary> Query Parameters </summary>

- `version_id`: The unique identifier for the version (e.g., chat-1234-244833-999)
- `user_id`: The user identifier (e.g., test123)
- `county_name`: The name of the county (e.g., pasco)

</details>

<details>
<summary> Payload </summary>

```
{
  "version_data": {
    "chat-1234-244833-999": [
      {
        "id": "2a3e7dcfb-8ec1-4313-a602-781764a402c",
        "rule1": {
          "when was Planning Commission voted unanimously": "On April 5, 2024, the Planning Commission voted unanimously to recommend approval to the Board of County Commissioners"
        }
      }
    ]
  }
}
```

</details>

<details>
<summary> Output </summary>

```
{
  "insert_count": 1,
  "ids": [
    "815e74f6-c8c9-4498-bba3-cbc9a7e89b83"
  ]
}

```
</summary>
</details>

### Get Rules

<b>GET - /api/versionControl/get_mpud_version_data</b>

<details>
<summary>Query Parameters</summary>

- `version_id`: The unique identifier for the version (e.g., chat-1234-244833-999)
- `user_id`: The user identifier (e.g., test123)
- `county_name`: The name of the county (e.g., pasco)

</details>

<details>
<summary>Output</summary>

```
[
  {
    "county_name": "pasco",
    "id": "815e74f6-c8c9-4498-bba3-cbc9a7e89b83",
    "mpud_history_id": "chat-1234-244833-999",
    "mpud_history": {
      "chat-1234-244833-999": [
        {
          "id": "2a3e7dcfb-8ec1-4313-a602-781764a402c",
          "rule1": {
            "when was Planning Commission voted unanimously": "On April 5, 2024, the Planning Commission voted unanimously to recommend approval to the Board of County Commissioners"
          }
        },
        {
          "rule2": {
            "where is the project located": "This project is located in the South Market Area"
          },
          "id": "68114e8a-76a5-4fef-8aa5-5f230d50efb6"
        }
      ]
    },
    "user_id": "test123"
  }
]

```

</details>

### Create PDF

   >**Note:** it will run inside celery.

This endpoint initiates the creation of a new file version for a specific county.

<b>POST - /api/versionControl/create_file_version_for_county</b>

<details>
<summary>Query Parameters</summary>

- `county_name`: The name of the county (e.g., pasco)
- `user_id`: The user identifier (e.g., user123)
- `file_name`: The name of the file (e.g., planning)
- `mpud_history_id`: The unique identifier for the MPUD history (e.g., chat-1234-244833-999)

</details>

<details>
<summary>Output</summary>

```
{
  "message": "Worker running in background",
  "task_id": "a70d8a16-a047-44da-bee0-ac60d3603676"
}

```

</details>

### Check Celery Task Status

This endpoint allows you to check the status of a previously initiated Celery task.

<b>GET - /api/versionControl/celery-task-status</b>

<details>
<summary>Query Parameters</summary>

- `task_id`: The unique identifier of the Celery task (e.g., a70d8a16-a047-44da-bee0-ac60d3603676)

</details>


<details>
<summary> Output </summary>

```

{
  "task_id": "a70d8a16-a047-44da-bee0-ac60d3603676",
  "status": "SUCCESS",
  "result": {
    "message": "Created version 1"
  }
}

```

</details>

### Get generated PDF List

This endpoint retrieves version information for documents associated with a specific county, user, and collection.

<b>GET - /api/versionControl/get_version_for_docs</b>

<details>
<summary>Query Parameters</summary>

- `county_name`: The name of the county (e.g., pasco)
- `user_id`: The user identifier (e.g., test123)
- `coll_name`: The name of the collection (e.g., default)

</details>

<details>
<summary> Output </summary>

```
{
  "versions": [
    {
      "version": "mpud_plann_1",
      "S3Location": "pasco-ocr-files-dev/MPUD-version_control_files/test123/pasco_county/mpud_pasco_131aafae-65ed-4ca3-aed8-601d61e19aff.pdf",
      "created_dateTime": "2024-08-27T06:09:49.889000"
    },
    {
      "version": "mpud_plann_2",
      "S3Location": "pasco-ocr-files-dev/MPUD-version_control_files/test123/pasco_county/mpud_pasco_6f1da81c-379b-4200-bd44-7ba4c9c05dfb.pdf",
      "created_dateTime": "2024-08-27T08:50:15.457000"
    },
    {
      "version": "mpud_plann_3",
      "S3Location": "pasco-ocr-files-dev/MPUD-version_control_files/test123/pasco_county/mpud_pasco_7cc35344-78e7-47ad-8d21-10d0b545d09b.pdf",
      "created_dateTime": "2024-08-27T20:24:43.865000"
    }
  ]
}
```

</details>

### Download PDF file

This endpoint retrieves the actual file data for a specific version of a document.

<b>GET - /api/versionControl/get_file_data_for_version</b>

<details>
<summary>Query Parameters</summary>

- `S3Location`: The S3 location of the file (URL-encoded)
- `user_id`: The user identifier (e.g., test123)
- `coll_name`: The name of the collection (e.g., default)

</details>

<b>Output:</b> Download File
