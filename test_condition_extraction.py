#!/usr/bin/env python3
"""
Test script to simulate condition extraction process
"""

import os
import sys
from pymongo import MongoClient
from app.core.config import settings
from app.crud.knowledge_graph_v2_repository import KnowledgeGraphRepositoryV2

def test_extraction_for_file(filename):
    """Test the condition extraction process for a specific file"""
    print(f"\n=== Testing Condition Extraction for {filename} ===")
    
    # Initialize MongoDB connection
    client = MongoClient(settings.MONGO_URL)
    db = client[settings.MONGO_DEFAULT_DATABASE]
    
    # Initialize the repository
    repo = KnowledgeGraphRepositoryV2(db)
    
    try:
        # Get layout data from textract collection
        textract_doc = db.textract.find_one({"document_name": {"$regex": filename}})
        if not textract_doc:
            print(f"No textract data found for {filename}")
            return
        
        layout_data = textract_doc.get('layout_data', [])
        print(f"Found {len(layout_data)} layout items")
        
        # Step 1: Test COA header extraction
        print("\n--- Step 1: Testing COA Header Extraction ---")
        processed_layout = repo.extract_coa_headers(layout_data.copy())
        
        if processed_layout:
            coa_items = [item for item in processed_layout if 'is_coa_data' in item]
            print(f"Items marked with is_coa_data: {len(coa_items)}")
            
            if coa_items:
                print("Sample COA items:")
                for i, item in enumerate(coa_items[:3]):
                    print(f"  {i+1}. Header: {item.get('LAYOUT_HEADER', 'N/A')}")
                    print(f"     Text: {item.get('LAYOUT_TEXT', 'N/A')[:100]}...")
            else:
                print("No items marked with is_coa_data - this is the problem!")
                
                # Debug: Check what titles and headers exist
                titles = [item.get('LAYOUT_TITLE', '') for item in layout_data if 'LAYOUT_TITLE' in item]
                headers = [item.get('LAYOUT_HEADER', '') for item in layout_data if 'LAYOUT_HEADER' in item]
                
                print(f"Available titles: {len(titles)}")
                for title in titles[:5]:
                    if title:
                        print(f"  - {title}")
                
                print(f"Available headers: {len(headers)}")
                for header in headers[:10]:
                    if header:
                        print(f"  - {header}")
        
        # Step 2: Test condition extraction
        print("\n--- Step 2: Testing Condition Extraction ---")
        if processed_layout:
            s3_bucket = f"pasco-ocr-files-{settings.STAGE}"
            s3_key = f"uploads/{filename}"
            
            conditions = repo.extract_coa_from_pdf(processed_layout, s3_bucket, s3_key)
            print(f"Extracted {len(conditions)} conditions")
            
            if conditions:
                print("Sample conditions:")
                for i, condition in enumerate(conditions[:3]):
                    text_data = condition.get('text', {}).get('data', '')
                    print(f"  {i+1}. {text_data[:150]}...")
            else:
                print("No conditions extracted - this confirms the issue!")
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        client.close()

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_condition_extraction.py <filename>")
        print("Example: python test_condition_extraction.py 'R3.A._-_TM_CONDITIONS_OF_APPROVAL.pdf'")
        return
    
    filename = sys.argv[1]
    test_extraction_for_file(filename)

if __name__ == "__main__":
    main()
