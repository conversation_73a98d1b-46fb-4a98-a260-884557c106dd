from pydantic import BaseModel, Field
from typing import List, Optional
from .basic_model import DatabaseModel


class Message(BaseModel):
    message: str = Field(
        ...,
        description="The chat message, must be between 5 and 1500 characters.",
    )


class ChatMessageHistory(BaseModel):
    human: Optional[Message] = None
    ai: Optional[Message] = None
    created_at: str
    discussion_type: str


class ChatBaseModel(DatabaseModel):
    """A MPUD components capturing the comprehensive requirements and details of a system or component to be developed for conditions of approval."""

    title: str = Field(
        default="untitled",
        min_length=5,
        max_length=50,
        description="The chat Name, must be between 5 and 50 characters.",
    )


class ChatView(ChatBaseModel):
    id: str = Field(
        ...,
        description="The id of the chat",
    )
    history: List[ChatMessageHistory]


class ChatQueryParams(BaseModel):
    chat_id: str = Field(
        ...,
        min_length=1,
        max_length=36,
        description="The discussion ID, must be between 5 and 36 characters.",
    )
