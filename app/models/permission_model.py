from typing import Dict, Optional
from pydantic import BaseModel, Field
from datetime import datetime, timezone
from app.models.user_model import Roles


class Permissions(BaseModel):
    viewUsers: bool = Field(default=False)
    editUser: bool = Field(default=False)
    manageRoles: bool = Field(default=False)
    createCoa: bool = Field(default=False)
    viewCoa: bool = Field(default=False)
    createApplication: bool = Field(default=False)
    viewApplication: bool = Field(default=False)
    viewKnowledgeBase: bool = Field(default=False)
    uploadKnowledgeBase: bool = Field(default=False)


class RolePermission(BaseModel):
    role: Roles
    permissions: Permissions
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class RolePermissionUpdate(BaseModel):
    permissions: Permissions


class RolePermissionResponse(BaseModel):
    message: str
    data: Optional[Dict] = None
