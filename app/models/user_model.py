from typing import Optional
from pydantic import BaseModel, EmailStr, Field
from enum import Enum


class TimezoneModel(BaseModel):
    display: str = Field(..., description="Timezone display name")
    gmt: str = Field(..., description="GMT offset")


class ChatModel(str, Enum):
    GPT35Turbo = "gpt-3.5-turbo"
    GPT4 = "gpt-4"
    Claude2 = "claude-2"
    Claude3 = "claude-3"


class Roles(str, Enum):
    superadmin = "superadmin"
    admin = "admin"
    drafter = "drafter"
    enforcer = "enforcer"


class RoleUpdateModel(BaseModel):
    role: Roles


class BaseUserModel(BaseModel):
    username: str = Field(..., description="username of the user")
    name: str = Field(..., description="User's full name")
    email: EmailStr = Field(..., description="User's email address (used as the username in Cognito)")
    role: Roles = Field(..., description="The roles of the user")


class UserModel(BaseUserModel):
    county: str = Field(..., description="User's county")
    designation: Optional[str] = Field(None, description="User's Designation")
    department: Optional[str] = Field(None, description="User's Department")
    timezone: Optional[TimezoneModel] = Field(None, description="User's Timezone")
    profileImage: Optional[str] = Field(None, description="User's Profile Image")
    chatModel: Optional[ChatModel] = Field(None, description="User's chat model")


class UserUpdateRequestModel(BaseModel):
    name: Optional[str] = Field(None, description="User's full name")
    designation: Optional[str] = Field(None, description="User's Designation")
    department: Optional[str] = Field(None, description="User's Department")
    timezone: Optional[TimezoneModel] = Field(None, description="User's Timezone")
    chatModel: Optional[ChatModel] = Field(None, description="User's chat model")

    class Config:
        json_schema_extra = {
            "example": {
                "name": "John Doe",
                "designation": "Manager",
                "department": "IT",
                "timezone": {
                    "display": "New York",
                    "gmt": "GMT-4:00",
                },
                "chatModel": "gpt-3.5-turbo",
            },
        }


class UserUpdateResponseModel(BaseModel):
    id_token: str = Field(..., description="ID token of the user")

    class Config:
        json_schema_extra = {
            "example": {
                "id_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
            },
        }


class UserResponseModel(BaseModel):
    users: list[BaseUserModel] = Field(..., description="List of users")
    page: int = Field(..., description="Page number")
    page_size: int = Field(..., description="Page size")
    total: int = Field(..., description="Total number of users")
    has_more: bool = Field(..., description="Whether there are more users")

    class Config:
        json_schema_extra = {
            "example": {
                "users": [
                    {
                        "username": "c155b78d-1421-4cf6-ab42-1d026ce298d0",
                        "name": "John Doe",
                        "email": "<EMAIL>",
                        "roles": "user",
                    },
                ],
                "page": 1,
                "page_size": 10,
                "total": 50,  # This is an estimate, as we might not have fetched all users
                "has_more": False,
            },
        }


class getAllAttributeModel(BaseModel):
    attribute: list[str] = Field(..., description="List of attribute")

    class Config:
        json_schema_extra = {
            "example": {
                "attribute": ["user 1", "user 2"],
            },
        }
