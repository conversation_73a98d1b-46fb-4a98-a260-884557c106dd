from pydantic import BaseModel, Field
# from bson.objectid import ObjectId
from app.models.examples.ocr_example import example


class OcrResponseModel(BaseModel):
    status: str = Field(description="status for action")
    message: str = Field(description="response message for the action")

    class Config:
        json_schema_extra = {"example": example}

# class DashboardModel(BaseModel):

#     # res :  Optional[dict] = Field(description="contain all the data")
#     headers : Optional[list] = Field(description="the header detail for the charts")
#     rows : Optional[list] = Field(description="the values for header for the charts")

#     class Config:
#         populate_by_name = True
#         arbitrary_types_allowed = True
#         str_strip_whitespace = True
#         json_encoders = {ObjectId: ObjectId, datetime: custom_encoder}
#         extra = Extra.allow

# class InputDashboardModel(BaseModel):

#     agent : Optional[str] = Field(description="the name of the agent")
#     agent_uid : Optional[str] = Field(description="the id of the agent")
#     batch_id : Optional[str] = Field(description="the batch of the querry")
#     date_from : Optional[str] = Field(description="the from date")
#     date_to : Optional[str] = Field(description="the to date")

#     class Config:
#         populate_by_name = True
#         arbitrary_types_allowed = True
#         str_strip_whitespace = True
#         json_encoders = {ObjectId: ObjectId, datetime: custom_encoder}



