from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class Notification(BaseModel):
    user_id: str
    user_name: str = Field(..., description="User uploaded by")
    title: str
    description: str
    resource_url: str
    read: Optional[bool] = False
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Timestamp when the notification was created.",
    )
