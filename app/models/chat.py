from datetime import datetime
from typing import Dict, Literal, List, Optional
from pydantic import BaseModel, Field


class Chat(BaseModel):
    chat_id: str = Field(..., description="Unique id for the chat")
    title: str = Field(..., description="Chat title")
    user_id: str = Field(..., description="User's id")
    created_at: datetime = Field(..., description="Creation date time of the chat")
    application_id: Optional[str] = Field(
        None,
        description="Application this chat is related to",
    )


class Message(BaseModel):
    message: str = Field(..., description="Actual textual message")


class ChatHistory(BaseModel):
    chat_id: str
    content: Dict[Literal["ai", "human"], Message]
    document: List
    meta_data: List
    timestamp: datetime = Field(..., description="When this message was generated/sent")


class ChatUser(BaseModel):
    """Join table to have many to many relationship between users and chats"""

    chat_id: str
    user_id: str


class EditableChatHistory(BaseModel):
    chat_id: str
    content: Dict[Literal["ai", "human"], Message]
    document: List
    timestamp: datetime = Field(..., description="When this message was generated/sent")
    action: str
