from enum import Enum
from pydantic import BaseModel, Field, validator
from datetime import datetime
from dateutil.parser import parse


class MethodNames(str, Enum):
    Predict = "predict"
    COT = "COT"


class DatabaseModel(BaseModel):
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="The timestamp when the application was created. Defaults to the current timestamp.",
    )
    last_modified: datetime = Field(
        default_factory=datetime.utcnow,
        description="The timestamp when the application was last modified. Defaults to the current timestamp.",
    )
    user_name: str = Field(
        "",
        description="Name of the user the applicaiton belongs to",
    )

    @validator("created_at", "last_modified", pre=True, always=True)
    def validate_datetime(cls, value):
        """
        Validates that the value is a valid ISO 8601 datetime string.
        """
        if isinstance(value, str):
            try:
                value = parse(value)
                return value
            except ValueError as e:
                raise ValueError(f"Invalid ISO 8601 datetime string: {value}") from e
        return datetime(
            value.year,
            value.month,
            value.day,
            value.hour,
            value.minute,
            int(value.second),
            int(value.second * 1000000 % 1000000),
            tzinfo=value.tzinfo,
        )
