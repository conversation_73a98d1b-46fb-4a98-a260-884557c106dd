from dataclasses import dataclass, field
from app.core.interactive_discussion.base_model import BaseModel


def get_config_cache():
    from app.main import COA_Points

    return COA_Points


def return_schema_item(value):
    return {
        "type": "object",
        "required": ["point", "chunk_id"],
        "properties": {
            "point": {
                "type": "string",
                "description": f"The point under {value}, don't use the subheader for this {value} consider only point",
            },
            "chunk_id": {
                "type": "string",
                "description": f"The chunk id of the point under {value}",
            },
        },
    }


@dataclass
class LAYOUT(BaseModel):
    # """A LAYOUT components capturing the comprehensive requirements and details of a system or component to be developed for conditions of approval."""
    """A LAYOUT component capturing the sections needed for conditions of approval."""

    conditions: list = field(
        metadata={
            "items": {"type": "string"},
            "description": "List of all selected conditions",
        }
    )
    configuration_state: str = field(
        metadata={"description": "Current state of project configuration"}
    )

    @classmethod
    def get_system_prompt(cls):
        return """
    You are an AI assistant specializing **Content Strategist** - Helps users by gathering only the necessary layout name to structure the Master Plan Unit Development (MPUD).

    Task:
        Your task is to collect all the required layout name by following the below GOAL.
"""

    @classmethod
    def get_additional_prompt_message(cls):
        return """
    Generate a brief, context-appropriate Welcome message for the user containing following:
        - Greets the user
        - Introduces the purpose of configuring Layouts for the MPUD
        - Invites the user to begin the process

    Ensure the message is concise, friendly, and tailored to the specific 'MPUD' being configured or updated.


    Your task is to guide the user through manually setting up the LAYOUT Based on goal provided before. After the initial welcome message:
        1. Only start with display the predicted list of layouts could be the result from the available layouts by refering the previous discussion data.
        2. Don't display predicted and available at a time.
        2. Predicted list should not contains all the available layout.
        3. Always have a question to add more layout if not selected layout available.
        4. After collecting list of selected layout, summarize and confirm.
    """

    @classmethod
    def get_instructions(cls):
        return """
    ## GOAL ##
    1. Based on the data from the Result from previous discussion block, gave the probable layout can be chosen by the user.
    2. Have the option to select from the scratch by the user itself.
    3. If user want to choose from scratch, then display all the layout from the block "AVAILABLE LAYOUT" below mentioned and ask the user to select the layouts.
    4. Accumulate the selected layout through out the discussions.

    ## Instructions ##
        1. Use the selected sections as it is, do not add space or any other extra character.
        2. Only If user choose to choose manually, Display the all available layouts.
        3. After the first question If the user want to add more options, List all the available layout those are not chosen before.
        4. User can able to pick multiple layouts.
        5. Always have a crips and suitable heading about what the user intented to do in the current step.
        6. Once user finalize the selected the layouts do the function call
        7. After selected the layouts there are only two options, ask the user by giving below step to do
            1. finalize the layouts
            2. add more layout if available
        8. After gathering all the layout names, summarize it and ask the user to confirm.
        9. After selecting the layout and user finalize, call the capture_discussion_output function. Ensure it is called only once and includes all relevant details.
        10. If new information contradicts earlier details, prioritize the latest information when discussing or suggesting changes.
        11. Ensure your output is formatted in markdown. Highlight the layout sections in **bold** (e.g, **general**, **environmental**), use lists, and ensure proper spacing and organization.

    ## AVAILABLE LAYOUT ##
        general
        environmental
        open space/buffering
        transportation/circulation
        access management
        dedication of right-of-way
        design/construction specifications
        utilities/water service/wastewater disposal
        stormwater
        land use
        procedures
        """
