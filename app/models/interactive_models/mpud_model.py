from dataclasses import dataclass, field
from app.core.interactive_discussion.base_model import BaseModel


def get_config_cache():
    from app.main import COA_Points

    return COA_Points


def return_schema_item(value):
    return {
        "type": "object",
        "required": ["point", "chunk_id"],
        "properties": {
            "point": {
                "type": "string",
                "description": f"The point under {value}, don't use the subheader for this {value} consider only point",
            },
            "chunk_id": {
                "type": "string",
                "description": f"The chunk id of the point under {value}",
            },
        },
    }


def mpud_dynamic(chat_id: str, assets: list = []):
    @dataclass
    class MPUD(BaseModel):
        """A MPUD components capturing the comprehensive requirements and details of a system or component to be developed for conditions of approval."""

        title: str = field(
            metadata={
                "description": "The official name of the MPUD. It should be conscious and descriptive.",
                "validation": """
                Must be completed before proceeding to the next step.
                Ensure the title conveys the purpose clearly without being overly verbose or redundant.
                Titles like general should be flagged as invalid. You can check for common placeholders or non-specific words and reject those.
                ---
                """,
            }
        )
        request: str = field(
            metadata={
                "description": "A request in this context refers to a formal proposal made by a developer to change the zoning of a piece of property.",
                "validation": """
                Must be completed before proceeding to the next step.
                Ensure the request clearly describes the proposal, including the nature of the change and its intended impact.
                Avoid vague or overly general statements. The request should be specific and focused on the proposed zoning change.
                Ensure the request is sufficiently detailed to provide context but concise enough to maintain clarity.
                Do not use placeholders or non-specific language like "general" or "unspecified."
                ---
                """,
            }
        )

        mpud_category: str = field(
            metadata={
                "description": "The category of the MPUD, categorizing the nature of the development request.",
                "enum": [
                    "non-substantial-modification",
                    "substantial-modification",
                    "zoning-amendment",
                    "land-use-equivalency-request",
                ],
                "default": "zoning-amendment",
                "validation": """
                    Must be completed before proceeding to the next step.
                    Ensure the category accurately reflects the nature of the development request.
                    The valid categories are:
                    - 'non-substantial-modification'
                    - 'substantial-modification'
                    - 'zoning-amendment' (default)
                    - 'land-use-equivalency-request'

                    ---
                    """,
            }
        )

        developer_name: str = field(
            metadata={
                "description": "The official, registered name of the developer or development company responsible for the project.",
                "validation": """
                Must be completed before proceeding to the next step.
                Ensure the name entered is the official, registered name of the developer or development company.
                Do not use placeholder text or generic terms like developer or company. The name must be clear and specific.
                If the developer is an individual, provide their full legal name.
                The entry should avoid abbreviations or initials unless they are part of the official name.
                - Responses that lack specificity or fail to address these aspects may require further clarification before approval.
                """,
            }
        )

        project_location: str = field(
            metadata={
                "description": "The specific and accurate location of the project, including full address, intersection, or road frontage.",
                "validation": """
                Must be completed before proceeding to the next step.
                The location must be specific and accurate, including the full address, intersection, or road frontage.
                Avoid vague descriptions such as "near" or "around" without clear points of reference.
                If the location is on a large parcel or development site, include details such as parcel number, lot number, or any other relevant geographic identifiers.
                For intersections, provide both street names and the approximate location of the project site.
                Ensure the location is clear enough for easy identification by authorities, planners, and other stakeholders.
                """,
            }
        )

        parcel_id_numbers: str = field(
            metadata={
                "description": "All applicable parcel identification numbers (Parcel IDs) associated with the project site.",
                "validation": """
                Must be completed before proceeding to the next step.
                Provide all applicable parcel identification numbers (Parcel IDs) that are associated with the project site.
                Ensure that each Parcel ID is listed accurately and completely as it appears in official records.
                If there are multiple parcels involved, list each Parcel ID number separately, separated by commas or in a bulleted list.
                Double-check that no numbers are missing or incorrect, as this is crucial for accurate zoning and property identification.
                If the project site spans multiple parcels, ensure to include all relevant numbers without omitting any.
                """,
            }
        )

        total_project_acreage: str = field(
            metadata={
                "description": "The exact acreage for the entire project site, including all parcels.",
                "validation": """
                Must be completed before proceeding to the next step.
                Provide the exact acreage for the entire project site.
                Ensure the acreage is calculated based on the total land area involved in the project, including all parcels.
                Round the acreage to two decimal places, if necessary, for precision (e.g., 5.25 acres).
                Do not provide approximations or ranges (e.g., about 5 acres).
                If the project involves multiple parcels or phases, ensure the combined acreage is accurate.
                Ensure that the acreage corresponds with the official land survey or recorded documents.
                """,
            }
        )

        project_type: str = field(
            metadata={
                "description": "The overall type of development, such as Residential, Commercial, Mixed-Use, Industrial, or Institutional.",
                "validation": """
                Must be completed before proceeding to the next step.
                Clearly specify the overall type of development (e.g., Residential, Commercial, Mixed-Use, Industrial, Institutional, etc.).
                If the development is a mixed-use project, clearly define the proportions or specific mix of uses (e.g., "60% residential, 40% commercial").
                If the development includes multiple types of uses, ensure each type is specifically identified.
                Ensure the selected development type accurately reflects the purpose and nature of the project.
                """,
            }
        )

        residential_components: str = field(
            metadata={
                "description": "Details about the residential components of the project, if applicable, including types of units, number of units, proposed density, and any age restrictions.",
                "validation": """
                Only applicable if the development type includes Residential or Mixed-Use.

                If the development type is Residential, provide detailed responses to the following:

                Types of units:
                Clearly describe the types of residential units being proposed (e.g., single-family detached, townhomes, multi-family apartments, etc.).
                Number of units for each type:
                Provide the exact number of units for each residential type listed (e.g., 50 single-family detached, 100 townhomes, 200 apartments).
                Proposed density:
                Specify the proposed density in units per acre (e.g., 8 units per acre).
                Age restrictions or target demographics: If applicable, state any age restrictions or target demographic.
                """,
            }
        )

        commercial_components: str = field(
            metadata={
                "description": "Details about the commercial components of the project, if applicable, including types of commercial space, total square footage, and proposed Floor Area Ratio (FAR).",
                "validation": """
                Only applicable if the development type includes Commercial or Mixed-Use.
                If the development type includes Commercial, provide detailed responses to the following three questions:
                Types of Commercial Space:
                List the types of commercial spaces being proposed (e.g., retail, office, medical, restaurant, etc.).
                Ensure each type is clearly specified.
                Total Square Footage:
                Provide the total square footage for each type of commercial space proposed (e.g., 50,000 square feet of retail, 20,000 square feet of office, etc.).
                Proposed FAR (Floor Area Ratio):
                Specify the proposed FAR (Floor Area Ratio) for the project. This is the ratio of the total building floor area to the total site area (e.g., FAR of 1.5).
                """,
            }
        )

        environmental_features: str = field(
            metadata={
                "description": "Detailed information on the environmental features and site characteristics, including wetlands, floodplain areas, significant trees/uplands, water bodies, topography, and known wildlife habitats.",
                "validation": """
                Must be completed before proceeding to the next step.
                Please identify and provide detailed information on the following environmental features and site characteristics:
                Wetlands:
                Type: Specify the type of wetlands present (e.g., emergent, forested, scrub-shrub, etc.).
                Category: Identify the category of wetlands (e.g., isolated, jurisdictional, etc.).
                Acreage: Provide the total acreage of wetland areas on the site.

                Floodplain Areas:
                FEMA Zones: Identify the FEMA flood zones affecting the project site (e.g., Zone A, Zone X, etc.).
                Acreage: Provide the total acreage of the floodplain areas on the site.

                Significant Trees/Uplands:
                Preservation Areas: Identify areas of significant trees or uplands on the site that may be preserved. Provide details on the types of trees and any specific preservation requirements.

                Water Bodies:
                List any water bodies on or near the site, such as lakes, ponds, streams, or wetlands.
                Provide specific details about each water body, including size or area, if applicable.

                Topography:
                Describe any significant changes in elevation or topographical features on the site. This may include hills, valleys, slopes, or steep terrain that could impact development.

                Known Wildlife Habitats:
                Identify wildlife habitats on the site, especially if they support protected species.
                Provide details on species present and any habitat requirements or protections (e.g., migratory bird habitats, endangered species, etc.).
                """,
            }
        )

        transportation_and_access: str = field(
            metadata={
                "description": "Information about transportation and access for the project site, including access points, adjacent roadways, proposed road improvements, internal road network, pedestrian/bicycle facilities, and transit accommodations.",
                "validation": """
                Must be completed before proceeding to the next step.
                Please describe the following aspects of transportation and access for the project site:

                Access Points:
                Provide the location and type of all proposed entrances to the project site (e.g., primary entrance, secondary access, emergency access).
                Specify whether each entrance will be signalized or unsignalized and any other relevant details.

                Adjacent Roadways:
                List the names and classifications of the adjacent roadways (e.g., Main Street – arterial, Park Avenue – collector, etc.).
                Include any relevant information about roadway widths or characteristics that may impact access.

                Proposed Road Improvements:
                Specify any road improvements that are proposed to support the project, such as the addition of turn lanes, traffic signals, median modifications, or other roadway enhancements.
                Include information about the need for road widening or any changes to the traffic flow near the site.

                Internal Road Network:
                Describe the internal road network within the project (e.g., public streets, private roads, access roads).
                Clarify the ownership and maintenance responsibilities for internal roads, and whether they will be publicly accessible or privately maintained.

                Pedestrian/Bicycle Facilities:
                Specify the pedestrian and bicycle facilities proposed within the development (e.g., sidewalks, multi-use trails, bike lanes).
                If applicable, describe how the site will connect to existing or planned pedestrian/bicycle networks.

                Transit Accommodations:
                Identify any transit accommodations included in the project, such as bus stops, bus shelters, or other transit-related facilities.
                If applicable, describe how these facilities will integrate with existing or planned transit routes.
                """,
            }
        )

        open_space_and_amenities: str = field(
            metadata={
                "description": "Detailed information on open space and recreational amenities for the project, including open space percentage, parks/recreation areas, amenity centers, buffers, and landscaping features.",
                "validation": """
                Must be completed before proceeding to the next step.
                Please provide detailed information on the following aspects related to open space and recreational amenities for the project:

                Open Space Percentage:
                Specify the total percentage of the project site dedicated to open space (e.g., 15%, 20%, etc.).
                This should be based on the overall land area of the project, including parks, green areas, and recreational spaces.

                Parks/Recreation Areas:
                Provide the locations, sizes, and types of parks and recreation areas within the project (e.g., neighborhood park, community park, playground, etc.).
                If applicable, include any proposed features such as sports fields, dog parks, or picnic areas.

                Amenity Centers:
                Describe any proposed amenity centers within the development, including the facilities that will be offered (e.g., clubhouse, fitness center, pool, community room, etc.).
                Provide specific details on the amenities that will be accessible to residents or the public.

                Buffers:
                Specify the types, widths, and locations of any buffers within the project (e.g., vegetative buffers, landscaped buffers, setback areas, etc.).
                Describe how these buffers will provide visual screening, noise reduction, or environmental protection.

                Landscaping Features:
                List any proposed landscaping features such as street trees, entrance features, landscaped medians, or other decorative or functional landscape elements.
                Provide specifics on the types of features and their locations within the project.
                """,
            }
        )

        utilities_and_infrastructure: str = field(
            metadata={
                "description": "Information on utilities and infrastructure for the project, including water service, wastewater service, stormwater management, solid waste disposal, and electrical service.",
                "validation": """
                Must be completed before proceeding to the next step.
                Please confirm the following utility and infrastructure details for the project:

                Water Service Provider:
                Provide the name of the water service provider responsible for supplying water to the project site (e.g., City Water Department, Private Water Company, etc.).

                Wastewater Service Provider:
                Provide the name of the wastewater service provider responsible for collecting and treating wastewater from the site (e.g., City Sewer Department, Private Sewer Service, etc.).

                Stormwater Management:
                Describe the approach to stormwater management for the project, including any facilities proposed to manage stormwater runoff (e.g., retention ponds, detention basins, infiltration systems, etc.).
                Include information on how the project will comply with local stormwater regulations.

                Solid Waste Disposal:
                Provide the name of the solid waste disposal provider and describe the method of disposal or waste collection (e.g., private contractor, municipal service, recycling facilities, etc.).

                Electrical Service:
                Provide the name of the electrical service provider responsible for supplying electricity to the project site (e.g., Electric Utility Company, Private Electric Provider, etc.).
                """,
            }
        )

        phasing_and_implementation: str = field(
            metadata={
                "description": "Details about the phasing and implementation of the project, including development phases, infrastructure phasing, and anticipated buildout timeline.",
                "validation": """
                Must be completed before proceeding to the next step.
                Please outline the phasing and implementation details for the project, including the following:
                Development Phases:

                Provide the number of development phases for the project.
                For each phase, specify the timing (e.g., Phase 1: Year 1, Phase 2: Year 3, etc.) and the contents of each phase (e.g., Phase 1: Residential development, Phase 2: Commercial component, etc.).
                Infrastructure Phasing:

                Describe the sequencing of infrastructure improvements (e.g., roads, utilities, stormwater management, etc.).
                Specify which infrastructure components will be developed in each phase and the expected timing for each.
                Anticipated Buildout Timeline:

                Provide the overall buildout schedule for the project, including the start and completion dates for the entire development.
                Indicate any key milestones or critical dates during the project's timeline.
                """,
            }
        )

        community_features: str = field(
            metadata={
                "description": "Information on community features and design elements for the project, including whether it will be gated or non-gated, architectural standards, unique development features, and community management.",
                "validation": """
                Must be completed before proceeding to the next step.
                Please describe the following community features and design elements for the project:

                Gated/Non-Gated:
                Specify whether the development will be gated or non-gated.
                If gated, describe the access control approach (e.g., guard gate, keycard access, security gates, etc.).

                Architectural Standards:
                Describe the architectural style of the development (e.g., Mediterranean, modern, traditional, etc.).
                List the materials used in the design (e.g., brick, stone, stucco, etc.).
                Include any special architectural features (e.g., custom facades, green building elements, energy-efficient designs, etc.).

                Unique Development Features:
                Identify any distinguishing elements or unique features of the development that set it apart from others (e.g., themed landscaping, eco-friendly initiatives, water features, community art installations, etc.).

                Community Management:
                Describe the community management structure (e.g., Homeowners Association (HOA), Community Development District (CDD), Private Management Company, etc.).
                Provide details on the responsibilities of the managing entity, such as maintaining common areas, enforcing community rules, and handling finances.
                """,
            }
        )

        surrounding_context: str = field(
            metadata={
                "description": "Information on the surrounding context and compatibility of the project, including adjacent land uses, integration features, and compatibility measures.",
                "validation": """
                Must be completed before proceeding to the next step.
                Please explain the following aspects of the surrounding context and compatibility of the project:
                Adjacent Land Uses:

                Describe the neighboring properties and their current land uses (e.g., residential, commercial, industrial, parks, vacant land, etc.).
                Include information on the zoning and density of adjacent properties, if applicable.
                Integration Features:

                Explain how the project will connect to and integrate with the surrounding area.
                Describe any features such as pedestrian connections, vehicular access points, landscaping buffers, or architectural design elements that help the project blend with its surroundings.
                Compatibility Measures:

                Outline the measures taken to ensure the project is compatible with the surrounding land uses.
                This could include design strategies, such as building heights, setbacks, landscaping, or buffer zones, as well as any strategies to address potential concerns like traffic impact or noise mitigation.
                """,
            }
        )

        configuration_state: str = field(
            metadata={"description": "Current state of project configuration"}
        )

        @classmethod
        def get_system_prompt(cls):
            return """
[TASK]
You are an AI designed to guide users through the configuration of Master Planned Unit Developments (MPUDs).
** No need to summarize collected information twice, reduce the Redundancy in output messages. **

IMPORTANT: Your first message must include a greeting followed by this exact text in markdown bold format:

    - **Welcome to the MPUD configuration process. You can either start by answering questions directly, or if you have relevant documentation about your MPUD, you can upload it to help streamline the process.**

    - I'm here to guide you through creating your Master Planned Unit Development configuration step by step, whichever approach works best for you.
---

## MPUD CATEGORY FORMAT RULES
When processing MPUD Category, you MUST convert to these exact formats:
- If found "Substantial Modification" → use "substantial-modification"
- If found "Non-Substantial Modification" → use "non-substantial-modification"
- If found "Zoning Amendment" → use "zoning-amendment"
- If found "Land Use Equivalency Request" → use "land-use-equivalency-request"

Always convert to lowercase with hyphens, regardless of input format.

## Document Processing Instructions
1. When documents/Asset are provided, analyze ALL fields
2. For MPUD Category specifically:
   - Convert to exact required format using hyphens and lowercase
   - Example: "Substantial Modification" must be stored as "substantial-modification"
3. Handle missing fields sequentially:
   - Only ask about missing fields at the end of the process, after gathering all other available details.
   - Only ask questions for the missing information that wasn't covered in the documents or previous steps.
   - Ask question for missing fields one by one with context for each question.

## Important Rules
- Ask user question to gather information for all Necessary fields.
- If any field has `Not specified` ask question for those fields to get an answer (you can also suggest any).
- Ask about only ONE missing field per interaction.
- Provide context for questions.
- Wait for user response before proceeding.
- Never list multiple missing fields at once.
- Do not use `please hold on` while interacting.
- ALWAYS use hyphenated lowercase format for MPUD category.
- If you are about to present findings from the asset, ** do not summarize information ** in the same message.
** Summarize only at the end after collecting all necessary fields. else continue asking question untill collecting answer for all fields **
"""

        @classmethod
        def get_instructions(cls):
            return """
## Initial Document Processing
    ** Initial Message **:
        To get started, please upload any relevant documents that may contain information about your MPUD. This will help streamline the process and ensure we gather all necessary details.
        - Inform user that documents must be provided at the start as adding them later won't affect the process

    ** Complete Document Analysis **:
        - Analyze documents for ALL possible fields at once.
        - Extract every piece of relevant information.

    ** Batch Confirmation **:
        - Ask for single confirmation of all fields.
        - Ask question one by one, for fields you not not have data.

    ** After Confirmation **:
        - If all accepted: Proceed to completion
        - If modifications needed: Only ask about specified fields
        - If starting fresh: Begin standard field-by-field process

## Initial User Interaction
    ** Document Request **:
        - Start by asking if the user would like to upload relevant documents
        - Explain that documents will help streamline the process
        - Wait for user confirmation before proceeding
        - Do not use `please hold on` while interacting.
    ** Document Processing **:
        - After documents are uploaded, analyze all content
        - Extract relevant information for all MPUD fields

## Asset Management
    ** Document Priority **: Always check provided documents first for information.
    ** Information Extraction **: Extract relevant details from assets before requesting user input
    ** Confirmation Flow **:
        - Present all asset-derived information at once for initial review.
        - Always ask for confirmation before proceeding.
        - Allow user to approve or modify each field.
        - Always request new information by asking questions for fields not found in assets.

## Interaction Process
    ** Contextual Start **: Begin each interaction with a brief, context-setting welcome message.
    ** Question Structure **: If the user provides detailed input
        - Extract and infer key details.
        - Assume appropriate values for missing data.
        - Avoid asking questions already answered.
    ** Step Management **:
        - Once a step is validated, remove the header for that step.
        - Skip steps where answers can be inferred from prior inputs.

    ### Initial Engagement
    - Set clear context for each discussion phase
    - Establish information or decision requirements upfront

    ### Data Collection
    - Ask direct, precise questions for MPUD development data
    - Apply immediate validation to user inputs
    - Provide clear rectification guidance when needed

    ### Validation Rules
    - Strictly follow validation for all Fields.
    - The user answer for each Filed has to be very good and clear.
    - *Filed can only be skiped if mentioned it can be empty*.
    - For required fields, please gather the necessary information from the user by providing suggestions based on the data already collected.

    ### Response Structure
    - Format responses for maximum clarity
    - Use lists for complex concepts only when necessary
    - Ensure all answers comply with field-specific rules

    ### Progress Management
    1. After successful validation:
    - Remove completed step headers.
    - Include brief transition message.
    - Display next step's header.
    - Do not summarize previous answers, only show the final summary once all steps are complete.

    2. Summarization Rules:
    - Only summarize before `capture_discussion_output` function call.
    - ** Avoid intermediate summaries during question sequence **.

    ### Final Processing
    - Always Summarize the collected information before doing the function call.
    - Call capture_discussion_output function only after all field are gathered.
    - Include all validated information.
    - Ensure comprehensive data capture.

    ## Documentation Standards
    - Maintain complete documentation.
    - Ensure all fields have actual content.
    - Never leave fields as "not specified" (Ask questions to fill these up).
    - Structure information clearly.
"""

        @classmethod
        def get_additional_prompt_message(cls):
            asset_content = ""
            if assets:
                # Simply include the raw content for LLM analysis
                asset_content = "\n".join(
                    [
                        f"Document content:\n{asset.get('content', '')}"
                        for asset in assets
                    ]
                )

            print("ASSET CONTENT:", asset_content)
            additional_message = f"""
Welcome to the MPUD configuration process. You can either start by answering questions directly, or if you have relevant documentation about your MPUD, you can upload it to help streamline the process.

<assets>
Documents: {'Documents provided' if assets else 'No documents provided'}

{asset_content}
</assets>

Process to follow if asset/document is present :
1. Do not use hold on when analysing, analyse it in a single message.
2. First, Analyze all documents and extract every possible field (** Do not ask question for fields inferred **).
3. Identify any missing required fields.
4. Start asking questions ** only ** for the missing field ones we were not able to get from assets (request input for missing fields).
5. Based on user response:
   - For found fields: You can approve or modify them based on their request.
   - For missing fields: guide user through providing the necessary information.
   - If user prefers: user can start fresh with manual input (not recomended).


** do not show extracted data and summarized data in the same message (Both are the same). **
"""
            # Let me analyze the documents and present all findings together.
            return additional_message

    return MPUD
