from dataclasses import dataclass, field
from app.core.interactive_discussion.base_model import BaseModel
import traceback
from app.core.interactive_discussion.utils.question_retriever import QuestionRetriever
import json
from app.core.interactive_discussion.prompts.coa_prompts import coa_prompt


def get_config_cache():
    from app.main import COA_Points

    return COA_Points


def coa_dynamic(
    header, neo4j_db_connection, chat_id, description=None, common_points=[]
):
    try:
        question_retriever = QuestionRetriever()
        questions = question_retriever.get_questions(
            header, description, common_points, neo4j_db_connection, chat_id
        )  # we have to add here

        @dataclass
        class COA(BaseModel):
            """A LAYOUT components capturing the comprehensive requirements and details of a system or component to be developed for conditions of approval."""

            points: list = field(
                metadata={
                    "description": "The list of all sumarized points should come in this structure",
                    "items": {
                        "type": "object",
                        "required": ["point", "chunk_id", "extracted_data"],
                        "properties": {
                            "point": {
                                "type": "string",
                                "description": f"The point under {header}, don't use the subheader for this {header} consider only point",
                            },
                            "chunk_id": {
                                "type": "string",
                                "description": f"The chunk id which is in the 'chunk_id' key for each point under {header}. the data is present in 'question and respective ids and points' section",
                            },
                            "is_actionable": {
                                "type": "boolean",
                                "description": "The point is actionable or not",
                            },
                            "extracted_data": {
                                "type": "object",
                                "properties": {
                                    "trigger": {
                                        "type": "string",
                                        "description": "The trigger word for the point",
                                    },
                                    "action_required": {
                                        "type": "string",
                                        "description": "The action required for the point",
                                    },
                                    "responsible_party": {
                                        "type": "string",
                                        "description": "The responsible party for the point",
                                    },
                                    "deliverable": {
                                        "type": "string",
                                        "description": "The deliverable for the point",
                                    },
                                    "enforcing_department": {
                                        "type": "string",
                                        "description": "The enforcing department for the point",
                                    },
                                    "validating_department": {
                                        "type": "string",
                                        "description": "The validating department for the point",
                                    },
                                },
                            },
                        },
                    },
                }
            )

            configuration_state: str = field(
                metadata={"description": "Current state of project configuration"}
            )

            @classmethod
            def get_system_prompt(cls):
                system_template = coa_prompt.get("coa_instructions_prompt", "")

                return system_template.format(header=header)

            @classmethod
            def get_additional_prompt_message(cls):
                return ""

            @classmethod
            def get_instructions(cls):
                sample_chunk_format = json.dumps(
                    [
                        {
                            "_id": "chunk ID",
                            "point": "sample point data",
                            "question": "question for the point",
                            "word": "keyword",
                        }
                    ]
                )
                generated_questions = json.dumps(questions, indent=4)
                instruction_template = coa_prompt.get("coa_system_prompt", "")

                return instruction_template.format(
                    header=header,
                    sample_chunk_format=sample_chunk_format,
                    questions=generated_questions,
                )

        return COA, questions

    except Exception as e:
        print("traceback", "".join(traceback.format_exc()), str(e))
