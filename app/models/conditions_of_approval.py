from pydantic import BaseModel, Field, validator
import json
from .basic_model import DatabaseModel


class ConditionsOfApprovalBaseModel(DatabaseModel):
    """A MPUD components capturing the comprehensive requirements and details of a system or component to be developed for conditions of approval."""

    condition: str = Field(
        ...,
        description="The list of all conditions",
    )
    chunk_id: str = Field(
        "",
        description="The chunk if of the conditions",
    )
    meta_data: dict = Field(
        {},
        description="The chunk if of the conditions",
    )

    @validator("meta_data", pre=True, always=True)
    def parse_additional_details(cls, value):
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON string for additional_details.")
        return value

    class Config:
        schema_extra = {
            "example": {
                "conditions": ["This is sample condition"],
            }
        }


class ConditionsOfApprovalInsert(ConditionsOfApprovalBaseModel):
    class Config:
        schema_extra = {
            "example": {
                "conditions": ["This is sample condition"],
            }
        }


class ConditionsOfApprovalView(ConditionsOfApprovalBaseModel):
    id: str = Field(
        ...,
        description="The id of the condition",
    )


class ConditionsQueryParams(BaseModel):
    layout_id: str = Field(
        ...,
        min_length=5,
        max_length=36,
        description="The layout ID, must be between 5 and 36 characters.",
    )
