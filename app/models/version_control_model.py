from typing import Optional, Any
from pydantic import BaseModel, Field
from app.models.examples.version_control_example import example_createMpudVersion


class VersionControlList(BaseModel):
    versions: Any = Field(description="The version details for the file")

    class Config :
        populate_by_name = True
        arbitrary_types_allowed = True
        str_strip_whitespace = True
        extra = 'allow'


class VersionControl(BaseModel):
    S3Location : Optional[str] = Field(description="the location fo the file")
    version : Optional[str] = Field(description="the version fo the file")
    message : Optional[str] = Field(description="message about the endpoint")
    fileData: Optional[str] = Field(description="base64 data")
    versions: Any = Field(description="The version details for the file")

    class Config :
        populate_by_name = True
        arbitrary_types_allowed = True
        str_strip_whitespace = True
        extra = 'allow'


class createFilesVersion(BaseModel):
    fileData: Optional[str] = Field(description="base64 data")

    class Config :
        populate_by_name = True
        arbitrary_types_allowed = True
        str_strip_whitespace = True
        extra = 'allow'


class createMpudVersion(BaseModel):
    version_data: Optional[Any] = Field(description="dictionary with all data")

    class Config :
        populate_by_name = True
        arbitrary_types_allowed = True
        str_strip_whitespace = True
        extra = 'allow'
        json_schema_extra = {"example": example_createMpudVersion}
