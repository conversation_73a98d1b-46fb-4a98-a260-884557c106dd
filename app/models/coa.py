from pydantic import BaseModel, Field, validator
from datetime import datetime, date
from uuid import uuid4
from enum import Enum
from typing import Optional


class StatusEnum(str, Enum):
    """Enumeration for the status of the coa."""

    TODO = "todo"
    IN_PROGRESS = "in-progress"
    COMPLETED = "completed"


class PriorityEnum(str, Enum):
    """Enumeration for the priority level of the coa."""

    HIGH = "high"
    MID = "mid"
    LOW = "low"


class COA(BaseModel):
    coa_id: str = Field(
        default_factory=lambda: str(uuid4()),
        description="Unique identifier for the COA, auto-generated UUID.",
    )

    title: str = Field(
        ...,
        description="Title of the COA.",  # Required field
    )

    summary: str = Field(
        ...,
        description="A brief summary of the COA.",  # Required field
    )

    status: StatusEnum = Field(
        StatusEnum.TODO,  # Default status
        description="Current status of the COA. One of 'todo', 'in-progress', 'completed'.",
    )

    priority: PriorityEnum = Field(
        PriorityEnum.LOW,  # Default priority
        description="Priority level of the COA. One of 'high', 'mid', 'low'.",
    )

    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Timestamp when the COA was created or the applicant applied.",
    )

    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Timestamp when the COA was last updated.",
    )

    due_date: Optional[datetime] = Field(
        None,
        description="Due date of the COA, selected by the applicant at creation.",
    )

    assignee: str = Field(
        default_factory="",
        description="Identifier of the user to whom the COA is assigned.",
    )

    file_name: str = Field(
        ...,
        description="File name this COA is related to.",
    )

    s3_url: str = Field(
        ...,
        description="S3 url of the associated file.",
    )

    coordinates: list = Field(
        ...,
        description="coordinates of the rules in associated file",
    )

    page_numbers: list = Field(
        ...,
        description="List of all the page numbers used for this COA, from the associated file.",
    )

    application_id: str = Field(..., description="Application this COA is related to.")

    accepting_user_id: str = Field(
        ...,
        description="User who accepted the rules for this COA",
    )

    chat_id: Optional[str] = Field(
        None,
        description="Chat this COA is related to",
    )

    # Validator to ensure 'due_date' is not in the past
    @validator("due_date", pre=True, always=True)
    def due_date_cannot_be_past(cls, v):
        if v is not None:
            if v < datetime.combine(date.today(), datetime.max.time()):
                raise ValueError("due_date cannot be in the past")
        return v

    # Update 'updated_at' before saving the model
    def save(self):
        self.updated_at = datetime.utcnow()
        # Here you would include code to save the model to the database
        # For example:
        # database.save(self)
        return self

    class Config:
        # Enable ORM mode if interacting with ORM libraries
        from_attributes = True
        # Optionally, you can add schema extra or other configurations here
