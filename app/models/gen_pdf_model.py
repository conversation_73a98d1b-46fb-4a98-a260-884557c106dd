from pydantic import BaseModel
from typing import List, Optional


# BCC          - Approved by Board of County Commissioners
# DRC          - Approved by District Rezoning Commission
# PC           - Approved by Planning Commission
# Rev          - Revised Date
# Petition No. - Rezoning Petition No.

class Item(BaseModel):
    content: str


class Section(BaseModel):
    heading: str
    items: List[Item]


class GenPdf(BaseModel):
    title: Optional[str] = None
    petition_no: Optional[str] = None
    bcc_date: Optional[str] = None
    drc_date: Optional[str] = None
    revision_date: Optional[str] = None
    # sections: List[Section]

    class Config:
        json_schema_extra = {
            'example': {
                'title': "AVALON PARK WEST",
                'petition_no': "7235",
                'bcc_date': "03/28/2025",
                'drc_date': "01/12/2025",
                'revision_date': "06/06/2024",
                'sections': [
                    {
                        'heading': "Master Development Plans",
                        'items': [
                            {
                                'content': "Development shall be in accordance with the application, plans, and information submitted for the MPUD (the \"Project\") unless otherwise stipulated or modified herein. ...",
                            },
                        ],
                    },
                    {
                        'heading': "General",
                        'items': [
                            {
                                'content': "Any existing and unexpired permits, special exceptions, variances, orders, conditions, development plans, conceptual plans, site plans, or other approvals for the parcels that are labeled on the approved Master Plan as \"Existing Residential\" or \"Existing Daycare\" ...",
                            },
                        ],
                    },
                ],
            },
        }
