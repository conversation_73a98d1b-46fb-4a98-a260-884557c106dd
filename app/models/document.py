from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field


class UploadStatus(str, Enum):
    UPLOADING = "uploading"
    OCR = "processing_ocr"
    KG = "building_knowledge_graph"
    COMPLETED = "completed"
    ERROR = "error"


class Document(BaseModel):
    file_name: str = Field(..., description="Name of the uploaded file")
    s3_path: str = Field(..., description="Uploaded file's S3 path")
    uploaded_at: datetime = Field(..., description="Date time of upload")
    county_name: str = Field(..., description="Name of the County it is uploaded for")
    user_id: str = Field(..., description="User uploaded by")
    user_name: str = Field(..., description="User uploaded by")
    summary: str = Field(..., description="A brief summary of the document")
    category: str = Field(..., description="Category of the document")
    zoning: str = Field(..., description="Zoning of the document")
    keywords: list = Field(..., description="Keywords of the document")
    upload_id: str = Field(..., description="Unique identifier for the upload")
    upload_status: UploadStatus = Field(..., description="Status of the upload")
