from pydantic import BaseModel, <PERSON>, validator
from datetime import datetime, date
from uuid import uuid4
from enum import Enum
from typing import Optional
from .basic_model import DatabaseModel
from dateutil.parser import parse


class StatusEnum(str, Enum):
    """Enumeration for the status of the application."""

    TODO = "todo"
    IN_PROGRESS = "in-progress"
    COMPLETED = "completed"


class PriorityEnum(str, Enum):
    """Enumeration for the priority level of the application."""

    HIGH = "high"
    MID = "mid"
    LOW = "low"


class MPUDCategoryEnum(str, Enum):
    NON_SUBSTANTIAL_MODIFICATION = "non-substantial-modification"
    SUBSTANTIAL_MODIFICATION = "substantial-modification"
    ZONING_AMENDMENT = "zoning-amendment"
    LAND_USE_EQUIVALENCY_REQUEST = "land-use-equivalency-request"


class ApplicationBaseModel(DatabaseModel):
    """A MPUD components capturing the comprehensive requirements and details of a system or component to be developed for conditions of approval."""

    status: StatusEnum = Field(
        StatusEnum.TODO,
        description="The status of the appliction",
        example=None,
    )
    chat_id: str = Field(
        "",
        description="The status of the appliction",
        example=None,
    )


class ApplicationInsert(ApplicationBaseModel):
    title: str = Field(
        ...,
        description="The official name of the MPUD. Should be concise and descriptive.",
        min_length=1,
    )
    request: str = Field(
        ...,
        description="A request in this context refers to a formal proposal made by a developer to change the zoning of a piece of property.",
        min_length=1,
    )
    mpud_category: MPUDCategoryEnum = Field(
        MPUDCategoryEnum.ZONING_AMENDMENT,
        description="The category of the MPUD, categorizing the nature of the development request.",
    )
    commision: str = Field(
        default="",
        description="The commission refers to the local governing body or authority that oversees land use and development in a specific area.",

    )
    developer: str = Field(
        default="",
        description="The developer is the individual or company responsible for initiating, planning, and managing a development project.",

    )
    reference: str = Field(
        ...,
        description="The reference provides the legal or regulatory basis for the MPUD request. In this case, the reference points to specific sections of the Land Development Code (LDC) that govern the zoning amendment process for Master Planned Unit Developments (MPUDs). The value refers to **LDC Section 402.2** (Zoning Amendment – MPUD) and **Section 522** (MPUD Zoning District), which outline the guidelines and requirements for approving an MPUD zoning district.",
        min_length=1,
    )
    additional_details: str = Field(
        None,
        description="A flexible object to store additional information necessary for determining the conditions of approval for an MPUD project. This includes gathering details on related code of development, condition in MPUD, and other condition of approval information.",
        example=None,
    )

    class Config:
        schema_extra = {
            "example": {
                "title": "New Development Project",
                "description": "A detailed explanation of the MPUD project.",
                "mpud_category": "zoning-amendment",
                "commision": "Local Planning Commission",
                "developer": "ABC Developers",
                "request": "Request for zoning amendment.",
                "reference": ["LDC Section 402.2", "Section 522"],
            }
        }


class ApplicationView(BaseModel):
    title: str = Field(
        ...,
        description="The official name of the MPUD. Should be concise and descriptive.",
    )
    request: str = Field(
        ...,
        description="A request in this context refers to a formal proposal made by a developer to change the zoning of a piece of property.",
    )
    id: str = Field(
        ...,
        description="The id of the application",
    )
    chat_id: str = Field(
        ...,
        description="The id of the chat",
    )
    status: StatusEnum = Field(
        description="The status of the appliction",
        example=None,
    )
    created_at: datetime = Field(
        description="The timestamp when the application was created. Defaults to the current timestamp.",
    )
    last_modified: datetime = Field(
        description="The timestamp when the application was last modified. Defaults to the current timestamp.",
    )
    user_name: str = Field(
        description="Name of the user the applicaiton belongs to",
    )

    @validator("created_at", "last_modified", pre=True, always=True)
    def validate_datetime(cls, value):
        """
        Validates that the value is a valid ISO 8601 datetime string.
        """
        if isinstance(value, str):
            try:
                value = parse(value)
                return value
            except ValueError as e:
                raise ValueError(f"Invalid ISO 8601 datetime string: {value}") from e
        return datetime(
            value.year,
            value.month,
            value.day,
            value.hour,
            value.minute,
            int(value.second),
            int(value.second * 1000000 % 1000000),
            tzinfo=value.tzinfo,
        )


class Application(BaseModel):
    # Unique ID for this application. Auto-generated UUID at the time of creation.
    mpud_id: str = Field(
        default_factory=lambda: str(uuid4()),
        description="Unique identifier for the application, auto-generated UUID.",
    )

    # Summary of this application.
    summary: str = Field(
        ...,
        description="A brief summary of the application.",  # Required field
    )

    # Current status of this application. Must be one of 'todo', 'in-progress', 'completed'.
    status: StatusEnum = Field(
        StatusEnum.TODO,  # Default status
        description="Current status of the application. One of 'todo', 'in-progress', 'completed'.",
    )

    # Priority of this application. Must be one of 'high', 'mid', 'low'.
    priority: PriorityEnum = Field(
        PriorityEnum.LOW,  # Default priority
        description="Priority level of the application. One of 'high', 'mid', 'low'.",
    )

    # When the application was created or applicant applied. Auto-generated at creation.
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Timestamp when the application was created or the applicant applied.",
    )

    # When the application's status or any field was updated. Auto-updated on modification.
    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        description="Timestamp when the application was last updated.",
    )

    # When the application is due. Selected by applicant at the time of creation.
    due_date: Optional[datetime] = Field(
        None,
        description="Due date of the application, selected by the applicant at creation.",
    )

    # ID of the user from the system whom it is assigned to. Selected by applicant at creation.
    assignee: str = Field(
        default_factory="",
        description="Identifier of the user to whom the application is assigned.",
    )

    county: str = Field(..., description="County this application is related to.")

    # S3 location of the Application COAs PDF
    s3_location: Optional[str] = Field(
        None,
        description="S3 location of the application COAs PDF",
    )

    # Validator to ensure 'due_date' is not in the past
    @validator("due_date", pre=True, always=True)
    def due_date_cannot_be_past(cls, v):
        if v is not None:
            if v < datetime.combine(date.today(), datetime.max.time()):
                raise ValueError("due_date cannot be in the past")
        return v

    # Update 'updated_at' before saving the model
    def save(self):
        self.updated_at = datetime.utcnow()
        # Here you would include code to save the model to the database
        # For example:
        # database.save(self)
        return self

    class Config:
        # Enable ORM mode if interacting with ORM libraries
        from_attributes = True
        # Optionally, you can add schema extra or other configurations here
