from pydantic import BaseModel, Field
from .basic_model import DatabaseModel


class LayoutBaseModel(DatabaseModel):
    """A MPUD components capturing the comprehensive requirements and details of a system or component to be developed for conditions of approval."""

    layout_name: str = Field(
        ...,
        min_length=5,
        max_length=50,
        description="The Layout Name, must be between 5 and 50 characters.",
    )


class LayoutInsert(LayoutBaseModel):
    class Config:
        schema_extra = {
            "example": {
                "layout_name": "This is sample layout",
            }
        }


class LayoutView(LayoutBaseModel):
    id: str = Field(
        ...,
        description="The id of the layout",
    )


class LayoutQueryParams(BaseModel):
    application_id: str = Field(
        ...,
        min_length=5,
        max_length=36,
        description="The application ID, must be between 5 and 36 characters.",
    )
