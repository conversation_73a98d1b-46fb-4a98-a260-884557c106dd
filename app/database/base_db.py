from abc import ABC, abstractmethod


class BaseDB(ABC):
    def __init__(self, db):
        self.db = db

    @abstractmethod
    def get(self):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def connect(self):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def close(self):
        raise NotImplementedError("Subclasses must implement this method")

    def __exit__(self):
        self.close()
