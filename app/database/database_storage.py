import certifi
from typing import Any
import pymongo as pm
import pymilvus
from app.core.config import settings
from app.crud.milvus.milvus_collection_fields import milvus_fields


Database = pm.database.Database


mongo_db_client: Any = None
mongo_config_db_client: Any = None
milvus_db_client: Any = None
ca = certifi.where()


def get_mongo_db_client():
    """Return database client instance."""
    if mongo_db_client is None:
        connect_mongo_db()
    return mongo_db_client


def get_mongo_db() -> Database:
    """Return database instance."""
    if mongo_db_client is None:
        connect_mongo_db()
        # print("INFO:     MongoDB client connected")
    return mongo_db_client[settings.MONGO_DEFAULT_DATABASE]


def get_one_mongo_db(database) -> Database:
    """Return database instance."""
    if mongo_db_client is None:
        connect_mongo_db()
        # print("INFO:     MongoDB client connected")
    return mongo_db_client[database]


def connect_mongo_db():
    """Create database connection."""
    global mongo_db_client
    mongo_db_client = pm.MongoClient(settings.MONGO_URL)


def get_mongo_config_db() -> Database:
    """Return database instance."""
    if mongo_config_db_client is None:
        connect_mongo_config_db()
        # print("INFO:     MongoDB client connected")
    return mongo_config_db_client[settings.MONGO_CONFIG_DATABASE]


def connect_mongo_config_db():
    """Create config database connection."""
    global mongo_config_db_client
    mongo_config_db_client = pm.MongoClient(settings.MONGO_URL)


def close_mongo_db():
    """Close database connection."""
    if mongo_db_client:
        mongo_db_client.close()
    if mongo_config_db_client:
        mongo_config_db_client.close()


def connect_milvus_db(coll_name, description="collection for files data"):
    global milvus_db_client
    milvus_db_client = pymilvus.MilvusClient(
        uri=settings.MILVUS_URI, token=settings.MILVUS_TOKEN,
    )
    if not milvus_db_client.has_collection(collection_name=coll_name):
        fields = milvus_fields[coll_name]
        schema = pymilvus.CollectionSchema(
            fields=fields,
            description=description,
        )

        index_params = milvus_db_client.prepare_index_params()

        index_params.add_index(
            field_name="vector",
            index_type="IVF_FLAT",
            metric_type="IP",
            params={"nlist": 4048},
        )

        milvus_db_client.create_collection(
            collection_name=coll_name,
            schema=schema,
            index_params=index_params,
        )


def get_milvus_client(coll_name=settings.MILVUS_DEFAULT_DATABASE):
    if milvus_db_client is None:
        connect_milvus_db(coll_name)
        # print("INFO:     Milvus client connected")
    return milvus_db_client


def close_milvus_db():
    """Close database connection."""
    if milvus_db_client:
        milvus_db_client.close()
