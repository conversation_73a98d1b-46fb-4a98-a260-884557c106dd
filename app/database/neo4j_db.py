from .base_graph_db import BaseGraphDB
from neo4j import GraphDatabase
from app.core.config import settings
from contextlib import contextmanager
from typing import Dict, Any, Optional
import json
import logging


logging.getLogger("neo4j").setLevel(logging.CRITICAL)
logger = logging.getLogger(__name__)


class Neo4jDB(BaseGraphDB):
    def __init__(self):
        self._driver = None
        super().__init__(None)

    @property
    def driver(self):
        if self._driver is None:
            self._driver = GraphDatabase.driver(
                settings.NEO4J_URI,
                auth=(settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD)
            )
        return self._driver

    @contextmanager
    def get_session(self):
        """Get a Neo4j session with proper connection management."""
        session = self.driver.session()
        try:
            yield session
        finally:
            session.close()

    def to_graphdb_format(self, properties) -> dict:
        # TOdO handle other keys than properties
        """Convert dictionary fields to strings for compatibility with graph databases."""
        # Handle the case where properties is None
        if properties is None:
            return {"properties": {}}

        # Handle other keys than properties
        formatted_data = {}
        for key, value in properties.get("properties", {}).items():
            if isinstance(value, dict):
                formatted_data[key] = json.dumps(value)
            else:
                formatted_data[key] = value
        return {**properties, "properties": formatted_data}

    def build_where_clause(self, properties, char, label):
        return " AND ".join(
            [
                f"{char}.{key} = {properties[key]}"
                if isinstance(properties[key], int)
                else f"{char}.{key} = '{properties[key]}'"
                for key in properties.keys()
            ]
            + [f"'{label}' IN labels({char})"]
        )

    def get(self):
        """Get the Neo4j database connection."""
        try:
            if not self._driver:
                self.connect()
            yield self
        finally:
            pass

    def connect(self):
        """Initialize the Neo4j driver if not already initialized."""
        if self._driver is None:
            self._driver = GraphDatabase.driver(
                settings.NEO4J_URI,
                auth=(settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD)
            )

    def close(self):
        """Close the Neo4j driver connection."""
        if self._driver is not None:
            self._driver.close()
            self._driver = None

    def execute_query(self, cypher_query: str, properties: Dict[str, Any] = None):
        """Execute a Cypher query and return the result."""
        formatted_data = self.to_graphdb_format(properties)
        # this is to format json to neo4j standards
        with self.get_session() as session:
            result = session.run(cypher_query, formatted_data)
            records = result.data()
            return records

    def execute_dict_query(self, cypher_query: str, properties):
        """Execute a Cypher query and return the result."""
        try:
            print('execute_dict_query', cypher_query)
            with self.get_session() as session:
                result = session.run(cypher_query, properties)
                records = result.data()
                return records
        except Exception as e:
            print('error :', e)

    def create_node(self, label, properties={}):
        """Insert a node into the Neo4j database."""
        if not label:
            raise ValueError("'label' is required.")

        if "id" not in properties:
            raise ValueError("'id' is required in properties.")
        cypher_query = f"""
        CREATE (n:{label} $properties)
        RETURN n, ID(n) AS node_id
        """
        result = self.execute_query(cypher_query, {"properties": properties})
        return {**result[0]["n"], "node_id": result[0]["node_id"]}

    def get_nodes_with_parent(
        self,
        node_label,
        parent_label,
        relation,
        parent_properties={},
        node_properties={},
        sort_by="last_updated",
        direction=-1,
    ):
        missing_params = [
            param_name
            for param_name, value in {
                "node_label": node_label,
                "parent_label": parent_label,
                "relation": relation,
            }.items()
            if not value
        ]

        if missing_params:
            raise ValueError(
                f"The following parameters are required: {', '.join(missing_params)}"
            )
        node_properties = self.build_where_clause(
            properties=node_properties, char="n", label=node_label
        )
        parent_properties = self.build_where_clause(
            properties=parent_properties, char="m", label=parent_label
        )
        sort_direction = "ASC" if direction == 1 else "DESC"

        cypher_query = f"""
        MATCH (n:{node_label})<-[{relation}]-(m:{parent_label})
        WHERE {node_properties} AND {parent_properties}
        RETURN n
        ORDER BY n.{sort_by} {sort_direction}
        """
        print(cypher_query)
        result = self.execute_query(cypher_query, {})
        return [i["n"] for i in result]

    def get_child_node(self, label, child_label, properties={}, child_properties={}):
        node_properties = self.build_where_clause(
            properties=properties, char="n", label=label
        )
        child_properties = self.build_where_clause(
            properties=child_properties, char="m", label=child_label
        )
        cypher_query = f"""
        MATCH (n:{label}) -[]->(m: {child_label})
        WHERE {node_properties} AND {child_properties}
        RETURN m, ID(m) AS node_id
        """
        print(cypher_query)
        result = self.execute_query(cypher_query, properties)
        print(result)
        return {**result[0]["m"], "node_id": result[0]["node_id"]} if result else None

    def has_node(self, label, properties={}):
        """Check if a node exists in the Neo4j database."""
        cypher_query = f"""
        MATCH (n:{label} {{id: $id}})
        RETURN n, ID(n) AS node_id
        """
        result = self.execute_query(cypher_query, properties)
        return result[0] if result else None

    def get_one_node(self, label, properties={}):
        formatted_property = self.build_where_clause(properties, "n", label)
        cypher_query = f"""
        MATCH (n:{label})
        WHERE {formatted_property}
        RETURN n, ID(n) AS node_id
        """

        result = self.execute_query(cypher_query, properties)
        return (
            {**result[0].get("n"), "node_id": result[0].get("node_id")}
            if result
            else None
        )

    def get_one_by_id(self, properties={}):
        cypher_query = f"""
        MATCH (n)
        WHERE Id(n) = {properties["id"]}
        RETURN n, ID(n) AS node_id
        """

        result = self.execute_query(cypher_query, properties)
        return (
            {**result[0].get("n"), "node_id": result[0].get("node_id")}
            if result
            else None
        )

    def create_edge(self, start_node, end_node, relationship, properties={}):
        with self.driver.session() as session:
            try:
                result = session.run(
                    f"""
                    MATCH (p), (c)
                    WHERE id(p) = $start_node AND id(c) = $end_node
                    CREATE (p)-[r:{relationship}]->(c)
                    RETURN p, c, r
                    """,
                    {"start_node": int(start_node), "end_node": int(end_node)}
                )
                data = list(result.data())
                return data[0] if data else None
            except Exception as e:
                print(f"Session execution error: {str(e)}")
                return None

    def has_edge(self, start_node, end_node=None, relationship=None):
        """Check if an edge exists between two nodes in the Neo4j database."""
        cypher_query = f"""
        MATCH (a)-[r:{relationship}]->(b)
        WHERE id(a) = $start_node AND id(b) = $end_node
        RETURN r
        """
        if not end_node :
            cypher_query = f"""
            MATCH (a)-[r:{relationship}]->(b)
            WHERE id(a) = $start_node
            RETURN r
            """
            result = self.execute_query(
                cypher_query, {"start_node": int(start_node)}
            )
            return result[0] if result else None

        result = self.execute_query(
            cypher_query, {"start_node": int(start_node), "end_node": int(end_node)}
        )
        return result[0] if result else None

    async def update_node_by_id(
        self, node_id: int, properties: Dict, node_type: Optional[str] = None
    ) -> Optional[Dict]:
        set_clauses = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"""
        MATCH (n)
        WHERE ID(n) = {node_id}
        SET {set_clauses}
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """
        print(query, "=-=")

        self.execute_query(
            query, properties
        )

    def update_node_by_id_sync(
        self, node_id: int, properties: Dict, node_type: Optional[str] = None
    ) -> Optional[Dict]:
        set_clauses = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"""
        MATCH (n)
        WHERE ID(n) = {node_id}
        SET {set_clauses}
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """
        print(query, "=-=")

        self.execute_query(
            query, properties
        )

    def update_node_dict_property(self, node_id, property_name, new_value):
        try:
            print('update_node_dict_property--')
            cypher_query = """
            MATCH (n)
            WHERE Id(n) = $node_id
            SET n[$property_name] = $new_value
            RETURN n
            """
            params = {
                "node_id": node_id,
                "property_name": property_name,
                "new_value": new_value
            }
            self.execute_dict_query(cypher_query, params)
        except Exception as e:
            print('error :', e)

    def update_node_property(self, node_id, property_name, new_value):
        cypher_query = f"""
        MATCH (n)
        WHERE Id(n) = {node_id}
        SET n.{property_name} = {new_value}
        RETURN n
        """
        print('cypher_query--->', cypher_query)
        self.execute_query(cypher_query, {})
        return

    def upsert_node(self, data):
        """Upsert a node in the Neo4j database."""
        label = data.get("label")
        properties = data.get("properties", {})
        if not label:
            raise ValueError("'label' is required.")
        set_clause = ", ".join([f"n.{k} = $properties.{k}" for k in properties.keys()])
        cypher_query = f"""
        MERGE (n:{label} {{id: $properties.id}})
        ON CREATE SET {set_clause}
        ON MATCH SET {set_clause}
        RETURN n, ID(n) AS node_id
        """
        result = self.execute_query(cypher_query, {"properties": properties})
        return result[0]

    def upsert_edge(self, start_node, end_node, relationship, properties={}):
        """Upsert an edge between two nodes in the Neo4j database."""
        set_clause = ", ".join([f"r.{k} = $properties.{k}" for k in properties.keys()])
        cypher_query = f"""
        MATCH (a), (b)
        WHERE id(a) = $start_node AND id(b) = $end_node
        MERGE (a)-[r:{relationship}]->(b)
        ON CREATE SET {set_clause}
        ON MATCH SET {set_clause}
        RETURN r
        """
        result = self.execute_query(
            cypher_query,
            {"start_node": start_node, "end_node": end_node, "properties": properties},
        )
        return result[0]

    def match_create_merge(self, node_id, node_label, r_label, properties={}):
        """Upsert an edge between two nodes in the Neo4j database."""
        set_clause = ", ".join([f"app.{key} = ${key}" for key in properties.keys()])

        # Query with the dynamic SET clause
        cypher_query = f"""
        MATCH (n) WHERE ID(n) = $node_id
        CREATE (app:{node_label})
        SET {set_clause}
        MERGE (n)-[:{r_label}]->(app)
        RETURN app
        """

        properties.update({"node_id": node_id, "node_label": node_label})

        result = self.execute_query(
            cypher_query,
            properties,
        )
        return result[0]
