from .base_db import BaseDB
from abc import abstractmethod


class BaseGraphDB(BaseDB):
    def __init__(self, db):
        super().__init__(db)

    @abstractmethod
    def create_node(self, label, properties={}):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def has_node(self, label, properties={}):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def create_edge(self, start_node, end_node, relationship, properties={}):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def has_edge(self, start_node, end_node, relationship):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_nodes_with_parent(
        self,
        node_label,
        parent_label,
        relation,
        parent_properties={},
        node_properties={},
    ):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_child_node(self, label, child_label, properties={}, child_properties={}):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_one_node(self, label, properties={}):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_one_by_id(self, label, properties={}):
        raise NotImplementedError("Subclasses must implement this method")
