import aioboto3
from app.core.config import settings
from typing import List, Dict, Optional
import json
from io import BytesIO


class AssertS3:
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.bucket_name = f"pasco-ocr-files-{settings.STAGE}"
        self.base_path = f"assets/{project_id}"
        self.uploaded_path = f"{self.base_path}/uploaded/"
        self.extracted_path = f"{self.base_path}/extracted/"

    async def _get_async_client(self):
        """Get async S3 client"""
        session = aioboto3.Session()
        return session.client(
            "s3",
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        )

    async def initialize_folders(self) -> bool:
        """Create folder structure if it doesn't exist"""
        try:
            folder_paths = [self.uploaded_path, self.extracted_path]
            async with await self._get_async_client() as s3:
                for folder_path in folder_paths:
                    await s3.put_object(Bucket=self.bucket_name, Key=folder_path)
                    print(f"Created folder: {folder_path}")
            return True
        except Exception as e:
            print(f"Failed to initialize S3 folders: {str(e)}")
            raise Exception(f"Failed to initialize S3 folders: {str(e)}")

    async def upload_file(
        self, file_path: str, file_content, content_type: str = None, folder: str = None
    ) -> bool:
        """Upload a file to the uploaded folder"""
        try:
            MAX_FILE_SIZE = 30 * 1024 * 1024  # 30MB in bytes

            extra_args = {}
            if content_type:
                extra_args["ContentType"] = content_type

            key = f"{self.uploaded_path}{file_path}"
            if folder:
                if folder == "extracted":
                    key = f"{self.extracted_path}{file_path}"

            # Read file content
            if hasattr(file_content, "read"):
                # For file-like objects that support async read
                if hasattr(file_content.read, "__await__"):
                    content = await file_content.read()
                else:
                    # For sync file-like objects
                    content = file_content.read()
            else:
                # If it's already bytes
                content = file_content

            # Check file size
            if len(content) > MAX_FILE_SIZE:
                raise Exception(
                    f"File size ({len(content) / (1024 * 1024):.2f}MB) exceeds maximum allowed size (30MB)"
                )

            file_obj = BytesIO(content)

            # Upload to S3
            async with await self._get_async_client() as s3:
                await s3.upload_fileobj(
                    file_obj, self.bucket_name, key, ExtraArgs=extra_args
                )

            # Reset file pointer if possible
            if hasattr(file_content, "seek"):
                if hasattr(file_content.seek, "__await__"):
                    await file_content.seek(0)
                else:
                    file_content.seek(0)

            return True
        except Exception as e:
            print(f"Upload error: {str(e)}")  # Debug print
            raise Exception(f"Failed to upload file: {str(e)}")

    async def list_all_files(self) -> Dict[str, List[Dict]]:
        """List all files in both uploaded and extracted folders"""
        uploaded = await self.list_uploaded()
        extracted = await self.list_extracted()
        return {"uploaded": uploaded, "extracted": extracted}

    async def _list_files(self, prefix: str) -> List[Dict]:
        """Helper method to list files with a given prefix"""
        try:
            async with await self._get_async_client() as s3:
                response = await s3.list_objects_v2(
                    Bucket=self.bucket_name, Prefix=prefix
                )

            files = []
            if "Contents" in response:
                for obj in response["Contents"]:
                    if not obj["Key"].endswith("/"):
                        files.append(
                            {
                                "key": obj["Key"],
                                "size": obj["Size"],
                                "last_modified": obj["LastModified"].isoformat(),
                                "filename": obj["Key"].split("/")[-1],
                            }
                        )
            return files
        except Exception as e:
            raise Exception(f"Failed to list files: {str(e)}")

    async def list_uploaded(self) -> List[Dict]:
        """List files in the uploaded folder"""
        return await self._list_files(self.uploaded_path)

    async def list_extracted(self) -> List[Dict]:
        """List files in the extracted folder"""
        return await self._list_files(self.extracted_path)

    async def delete_assert(self, file_path: str) -> bool:
        """Delete a file from both uploaded and extracted folders"""
        try:
            async with await self._get_async_client() as s3:
                # Delete from uploaded
                uploaded_key = f"{self.uploaded_path}{file_path}"
                await s3.delete_object(Bucket=self.bucket_name, Key=uploaded_key)

                # Delete from extracted
                extracted_key = f"{self.extracted_path}{file_path}"
                await s3.delete_object(Bucket=self.bucket_name, Key=extracted_key)
            return True
        except Exception as e:
            raise Exception(f"Failed to delete file: {str(e)}")

    async def get_extracted_assert(self, file_path: str) -> Optional[Dict]:
        """Get the extracted data for a file"""
        try:
            key = f"{self.extracted_path}{file_path}"
            async with await self._get_async_client() as s3:
                response = await s3.get_object(Bucket=self.bucket_name, Key=key)
                content = await response["Body"].read()
                return json.loads(content.decode("utf-8"))
        except Exception as e:
            if "NoSuchKey" in str(e):
                return None
            raise Exception(f"Failed to get extracted assert: {str(e)}")
