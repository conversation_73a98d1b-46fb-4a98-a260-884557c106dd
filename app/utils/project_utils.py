from typing import Dict
import tiktok<PERSON>


def calculate_token(input_text: str) -> Dict:
    """
    Calculate token count, word count and letter count for input text

    Args:
        input_text (str): Input text to analyze

    Returns:
        Dict containing:
            - tokens: Number of tokens using GPT tokenizer
            - words: Number of words
            - letters: Number of letters/characters
    """
    # Initialize tokenizer
    enc = tiktoken.get_encoding("cl100k_base")

    # Calculate tokens
    tokens = len(enc.encode(input_text))

    # Calculate words by splitting on whitespace
    words = len(input_text.split())

    # Calculate letters (excluding whitespace)
    letters = len("".join(input_text.split()))

    return {"tokens": tokens, "words": words, "letters": letters}
