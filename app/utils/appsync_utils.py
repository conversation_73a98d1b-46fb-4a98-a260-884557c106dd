# app/utils/appsync_utils.py

import aiohttp
import functools
import json
from typing import Callable, Any
from app.core.config import Settings

settings = Settings()


def format_size(size_bytes: int) -> str:
    """Convert bytes to human readable format"""
    for unit in ["B", "KB", "MB", "GB"]:
        if size_bytes < 1024.0:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.2f} GB"


async def send_progress_event(
    upload_id: str,
    progress: float,
    status: str,
    message: str,
):
    """
    Send progress event to AppSync Events API

    Args:
        upload_id (str): Unique identifier for the upload
        progress (float): Progress percentage (0-100)
        status (str): Current status (e.g., 'started', 'uploading', 'processing', 'completed', 'error')
        message (str): Human-readable status message
    """
    event_data = {
        "uploadId": upload_id,
        "progress": progress,
        "status": status,
        "message": message,
    }

    payload = {
        "channel": f"default/upload/{upload_id}",
        "events": [json.dumps(event_data)],
    }

    headers = {
        "x-api-key": settings.APP_SYNC_API_KEY,
        "Content-Type": "application/json",
    }

    async with aiohttp.ClientSession() as session:
        try:
            async with session.post(
                f"https://{settings.APP_SYNC_HTTP_DOMAIN}/event",
                headers=headers,
                json=payload,
            ) as response:
                if response.status != 200:
                    print(f"Failed to send event: {await response.text()}")
        except Exception as e:
            print(f"Error sending event: {str(e)}")


def track_progress(description: str = None):
    """
    Decorator to track progress of async functions using AppSync Events

    Args:
        description (str, optional): Custom description for the operation

    Usage:
        @track_progress("Processing file")
        async def process_file(upload_id: str, ...):
            # Function implementation
    """

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            # Extract upload_id from args or kwargs
            upload_id = None
            if args and isinstance(args[0], str):
                upload_id = args[0]
            elif "upload_id" in kwargs:
                upload_id = kwargs["upload_id"]

            if not upload_id:
                raise ValueError(
                    "upload_id must be provided as first argument or keyword argument",
                )

            operation_name = description or func.__name__

            try:
                # Send start event
                await send_progress_event(
                    upload_id=upload_id,
                    progress=0,
                    status="started",
                    message=f"Starting {operation_name}",
                )

                # Execute the function
                result = await func(*args, **kwargs)

                # Send completion event
                await send_progress_event(
                    upload_id=upload_id,
                    progress=100,
                    status="completed",
                    message=f"Completed {operation_name}",
                )

                return result

            except Exception as e:
                # Send error event
                await send_progress_event(
                    upload_id=upload_id,
                    progress=0,
                    status="error",
                    message=f"Error in {operation_name}: {str(e)}",
                )
                raise

        return wrapper

    return decorator
