import json
from fastapi.responses import StreamingResponse
from functools import wraps


def stream_generator(function):
    def streamer(generator, chat_id, callback):
        response = ""
        for chunk in generator:
            if hasattr(chunk, "content"):
                response += chunk.content
            else:
                response += chunk
            yield f"data: {json.dumps({'content': response, 'chat_id': chat_id})}\n\n"
        if callback is not None:
            callback(response)

    @wraps(function)
    def stream_wrapper(*args, **kwargs):
        generator, chat_id, callback = function(*args, **kwargs)
        return StreamingResponse(
            streamer(generator, chat_id, callback), media_type="text/event-stream",
        )

    return stream_wrapper
