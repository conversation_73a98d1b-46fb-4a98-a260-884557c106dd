from langchain_openai import OpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_milvus import Mi<PERSON>vu<PERSON>
from app.core.decorators import traced
from app.crud.milvus.base_repository import BaseRepository as Mi<PERSON><PERSON>sRepo
from app.database.database_storage import get_milvus_client
from app.core.config import Settings
import os
from dotenv import load_dotenv
import uuid
from app.crud.mongo.base_repository import BaseRepository as MongoRepo
from app.models.chat import Chat, ChatHistory, Message, ChatUser
from datetime import datetime
from app.database.database_storage import get_mongo_db
from functools import wraps
from fastapi import HTTPException
import traceback

_MONGO_REPO_CHAT = MongoRepo("chats")
_MONGO_REPO_CHAT_HISTORY = MongoRepo("chat_history")
_MONGO_REPO_CHAT_USERS = MongoRepo("chat_users")

settings = Settings()
load_dotenv()  # take environment variables


def application_error_handler(function):
    @wraps(function)
    def try_except_wrapper(*args, **kwargs):
        try:
            if "user" in kwargs:
                if not kwargs["user"].get("cognito:username"):
                    raise ValueError("User not found")

            return function(*args, **kwargs)
        except Exception as e:
            print(traceback.format_exc())
            raise HTTPException(
                status_code=500, detail=f"Internal server error: {str(e)}"
            )

    return try_except_wrapper


def get_embedding_function():
    return OpenAIEmbeddings(
        model="text-embedding-ada-002",
        api_key=settings.OPENAI_API_KEY,
    )


def post_to_milvus(data: list, milvus_collection: str):
    try:
        milvus_repo = MilvusRepo(milvus_collection)
        milvus_repo.create(data, get_milvus_client(milvus_collection))
        return {"status": "ok", "message": "Inserted Successfully !"}

    except Exception as e:
        return {"status": "non-ok", "message": str(e)}


def create_embedding_of_document(object_input: dict, mongo_document_id: str):
    try:
        fileName = ""
        chunk_size = 1024
        buffer_size = 256
        if "blocks" in object_input:
            del object_input["blocks"]

        if "fileName" in object_input:
            fileName = object_input["fileName"]
            del object_input["fileName"]
        if "_id" in object_input:
            del object_input["_id"]

        page_lis = list(object_input.keys())
        for p in range(0, len(page_lis)):
            page_lis[p] = int(page_lis[p])

        page_lis.sort()
        list_to_post_to_milvus = []

        for page in page_lis:
            page_data_to_post_milvus = {}

            text = object_input[str(page)]
            chunks = []

            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=chunk_size,
                chunk_overlap=buffer_size,
            )
            chunks = text_splitter.split_text(text)

            for chunk_id, chunk in enumerate(chunks):
                page_data_to_post_milvus = {}
                embeddings = get_embedding_function()
                vector_result = embeddings.embed_query(chunk)

                # page_data_to_post_milvus['id'] = int(page)
                page_data_to_post_milvus["vector"] = vector_result
                page_data_to_post_milvus["text"] = chunk
                page_data_to_post_milvus["fileName"] = fileName
                page_data_to_post_milvus["page"] = str(page)
                page_data_to_post_milvus["chunkId"] = str(chunk_id)
                page_data_to_post_milvus["documentId"] = str(mongo_document_id)

                list_to_post_to_milvus.append(page_data_to_post_milvus)

        if list_to_post_to_milvus:
            resp = post_to_milvus(
                list_to_post_to_milvus,
                os.environ.get("MILVUS_DEFAULT_DATABASE"),
            )
            if "status" in resp and resp["status"] == "ok":
                return {
                    "status": "ok",
                    "message": "created embeddings and inserted to milvus",
                }
            elif "message" in resp:
                return {"status": "ok", "message": resp["message"]}
        else:
            return {
                "status": "ok",
                "message": f"no data to post to milvus for file {fileName}",
            }

    except Exception as e:
        return {"status": "non-ok", "message": str(e)}


def create_embedding_of_metadata(metadata: dict, mongo_document_id: str):
    try:
        data = {}
        embeddings = get_embedding_function()
        vector_result = embeddings.embed_query(str(metadata))

        data["vector"] = vector_result
        data["documentId"] = str(mongo_document_id)

        resp = post_to_milvus([data], "document_metadata")
        # print("MILVUS METADATA RESPONSE: ", resp)
        if "status" in resp and resp["status"] == "ok":
            return {
                "status": "ok",
                "message": "created embeddings and inserted to milvus",
            }
        elif "message" in resp:
            return {"status": "ok", "message": resp["message"]}

    except Exception as e:
        return {"status": "non-ok", "message": str(e)}


def remove_vector(dict_data):
    if "vector" in dict_data["entity"]:
        del dict_data["entity"]["vector"]
    return dict_data


def milvus_search(query_embedding):
    docs = get_milvus_client().search(
        collection_name=os.environ.get("MILVUS_DEFAULT_DATABASE"),
        data=[query_embedding],  # Wrap the vector in a list
        limit=int(os.environ.get("MILVUS_SEARCH_RESULTS_LIMIT")),
        output_fields=["*"],  # Return all fields
    )
    if len(docs):
        return list(map(remove_vector, docs[0]))
    else:
        return "No results found"


def format_docs(docs):
    return "\n\n".join(
        f"file_name: {doc.metadata['fileName']}\n"
        f"page_no: {doc.metadata['page']}\n"
        f"Content:\n{doc.page_content}"
        for doc in docs
    )
    # return "\n\n".join(doc.page_content for doc in docs)


def get_langchain_milvus_client(
    embedding_model=get_embedding_function(),
    collection=settings.MILVUS_DEFAULT_DATABASE,
):
    vector_db = Milvus(
        collection_name=collection,
        embedding_function=embedding_model,
        connection_args={"uri": settings.MILVUS_URI, "token": settings.MILVUS_TOKEN},
    )
    return vector_db


def milvus_langchain_search(query):
    retriver = get_langchain_milvus_client().as_retriever(
        search_type="similarity",
        search_kwargs={"k": int(settings.MILVUS_SEARCH_RESULTS_LIMIT)},
    )
    docs = retriver.invoke(query)
    return format_docs(docs)


@traced(name="create_new_message")
def create_new_message(
    chat_id: str,
    ai_message: str,
    human_message: str,
    document_list: list,
    meta_data: list,
):
    for recd in document_list:
        recd["id"] = str(uuid.uuid4())
        recd["last_updated"] = datetime.now()
        recd["status"] = "open"

    insert_id = _MONGO_REPO_CHAT_HISTORY.create(
        ChatHistory(
            chat_id=chat_id,
            timestamp=datetime.now(),
            content={
                "human": Message(message=human_message),
                "ai": Message(message=ai_message),
            },
            document=document_list,
            meta_data=meta_data,
        ).model_dump(),
        get_mongo_db(),
    )
    # print("Chat saved at: ", insert_id)

    return insert_id


@traced(name="get_latest_n_messages_from_chat")
def get_latest_n_messages_from_chat(chat_id: str, user_id: str, application_id=""):
    chat_history = _MONGO_REPO_CHAT_HISTORY.get_all_by_projection(
        {"chat_id": chat_id},  # TODO: update query to include application id
        {"_id": 0},
        get_mongo_db(),
        limit=settings.HISTORY_FETCH_LIMIT,
        sort_key="timestamp",
        sort_direction=-1,
    )
    chat_history.reverse()

    chat_title = ""
    if not len(chat_history):
        chat_id, chat_title = generate_chat(user_id, application_id)
    # print("Working with chat_id: ", chat_id)

    return chat_history, chat_id, chat_title


def generate_chat(user_id: str, application_id: str):
    chat_id = str(uuid.uuid4())
    chat_title = generate_chat_title()
    _MONGO_REPO_CHAT.create(
        Chat(
            chat_id=chat_id,
            title=chat_title,
            user_id=user_id,
            created_at=datetime.now(),
            application_id=application_id,
        ).model_dump(),
        get_mongo_db(),
    )

    return chat_id, chat_title


def generate_chat_title():
    now = datetime.now()
    formatted_datetime = now.strftime("%d/%b %H:%M")
    return "Chat from " + formatted_datetime


def get_history_from_chat(chat_id: str, limit: int, user_id: str):
    chat = _MONGO_REPO_CHAT.get_one_by_projection(
        {"user_id": user_id, "chat_id": chat_id},
        {},
        get_mongo_db(),
    )
    if not chat:
        return []

    chat_history = _MONGO_REPO_CHAT_HISTORY.get_all_by_projection(
        {"chat_id": chat_id},
        {"_id": 0},
        get_mongo_db(),
        limit=limit,
    )
    return chat["title"], chat_history


def get_all_chats(user_id: str, application_id=None):
    query = {}
    if application_id is not None:
        query["application_id"] = application_id
    else:
        query["user_id"] = user_id

    chats = _MONGO_REPO_CHAT.get_all_by_projection(
        query,
        {"_id": 0},
        get_mongo_db(),
    )
    return chats


def toggle_chat_users(chat_id: str, chat_users: dict):
    for user_id in chat_users:
        # If selected
        if chat_users[user_id]:
            chat_user = ChatUser(chat_id=chat_id, user_id=user_id)
            _MONGO_REPO_CHAT_USERS.create(
                chat_user.model_dump(),
                get_mongo_db(),
            )
        else:
            _MONGO_REPO_CHAT_USERS.delete_many(
                {"chat_id": chat_id, "user_id": user_id},
                get_mongo_db(),
            )


def get_chat_users_by_chat_id(chat_id: str) -> dict:
    chatUsers = _MONGO_REPO_CHAT_USERS.get_all_by_projection(
        {"chat_id": chat_id},
        {"_id": 0, "user_id": 1},
        get_mongo_db(),
    )
    chatUsersDict = {}
    for chatUser in chatUsers:
        chatUsersDict[chatUser["user_id"]] = True
    return chatUsersDict


def parse_all_users_response(response: dict, chat_id: str):
    chatUsers = get_chat_users_by_chat_id(chat_id)
    users = []
    for user in response["Users"]:
        temp_user = {}
        temp_user["user_name"] = user["Username"]
        temp_user["selected"] = chatUsers.get(user["Username"], False)
        for attr in user["Attributes"]:
            temp_user[attr["Name"]] = attr["Value"]
        users.append(temp_user)
    return users
