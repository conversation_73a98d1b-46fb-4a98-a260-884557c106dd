from app.crud.mongo.base_repository import BaseRepository as MongoRepo
from app.database.database_storage import get_mongo_db
from app.models.application import Application
from bson.objectid import ObjectId
from datetime import datetime

# Repositories for applications and users
_MONGO_REPO_APPLICATION = MongoRepo("applications")


def create_application(
    summary: str,
    due_date: datetime,
    assignee: str,
    priority: str,
    status: str,
    county: str,
):
    """Creates a new application."""
    if due_date:
        due_date = due_date.replace(minute=59, hour=23, second=59, microsecond=999999)

    application = Application(
        summary=summary,
        status=status,
        priority=priority,
        due_date=due_date,
        assignee=assignee,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        county=county,
    )

    # Insert application into the database
    result = _MONGO_REPO_APPLICATION.create(
        application.model_dump(),
        get_mongo_db(),
    )
    return result.inserted_id, application.model_dump()


def object_id_to_str(record):
    """Converts the MongoDB _id field to string format for the application object."""
    record["id"] = str(record["_id"])
    del record["_id"]
    return record


def get_all_applications(county: str):
    """Fetches all applications assigned to a specific user."""
    query = {"county": county}
    applications = _MONGO_REPO_APPLICATION.get_all_by_projection(
        query,
        {},
        get_mongo_db(),
    )
    return list(map(object_id_to_str, applications))


def update_application_fields(application_id: str, updated_fields: dict):
    """Updates a specific field of an application."""
    if "due_date" in updated_fields:
        due_date = datetime.fromisoformat(updated_fields["due_date"])
        due_date = due_date.replace(minute=0, hour=0, second=0, microsecond=0)
        updated_fields["due_date"] = due_date

    updated_fields["updated_at"] = datetime.utcnow()
    try:
        return _MONGO_REPO_APPLICATION.update_one_by_query_upsert(
            {"_id": ObjectId(application_id)},
            updated_fields,
            get_mongo_db(),
        )
    except Exception as e:
        print(e)
        return {"message": e}


def get_application_by_id(application_id: str):
    """Fetches a specific application by its ID."""
    application = _MONGO_REPO_APPLICATION.get_one_by_projection(
        {"_id": ObjectId(application_id)},
        {},
        get_mongo_db(),
    )
    if application:
        return object_id_to_str(application)
    return None


def delete_application(application_id: str):
    """Deletes a specific application by its ID."""
    return _MONGO_REPO_APPLICATION.delete_many(
        {"_id": ObjectId(application_id)},
        get_mongo_db(),
    )


def update_application_pdf_s3_location(application_id: str, s3_url: str):
    """Updates a specific application's PDF S3 URL."""
    return _MONGO_REPO_APPLICATION.update_one_by_query_upsert(
        {"_id": ObjectId(application_id)},
        {"s3_location": s3_url, "updated_at": datetime.utcnow()},
        get_mongo_db(),
    )
