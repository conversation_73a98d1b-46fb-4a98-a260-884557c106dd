from app.crud.mongo.base_repository import BaseRepository as MongoRepo
from app.database.database_storage import get_mongo_db
from app.models.notification import Notification
from bson.objectid import ObjectId
from datetime import datetime
import traceback

_MONGO_REPO_NOTIFICATION = MongoRepo("notifications")
_MONGO_REPO_CHAT_USERS = MongoRepo("chat_users")


def create_notification(user_id: str, user_name: str, title: str, description: str, url=""):
    notification = Notification(
        user_id=user_id,
        user_name=user_name,
        title=title,
        description=description,
        resource_url=url,
        created_at=datetime.utcnow(),
    )

    result = _MONGO_REPO_NOTIFICATION.create(
        notification.model_dump(),
        get_mongo_db(),
    )
    return result.inserted_id, notification.model_dump()


def object_id_to_str(record):
    record["id"] = str(record["_id"])
    del record["_id"]
    return record


def get_this_users_chats(user_id: str):
    chats = _MONGO_REPO_CHAT_USERS.get_all_by_projection(
        {"user_id": user_id}, {"chat_id": 1}, get_mongo_db(),
    )
    return list(map(lambda chat: chat["chat_id"], chats))


def get_all_notifications(user_id: str):
    this_users_chats = get_this_users_chats(user_id)
    query = {
        "$or": [{"user_id": user_id}, {"chat_id": {"$in": this_users_chats or []}}],
    }
    notifications = _MONGO_REPO_NOTIFICATION.get_all_by_projection(
        query,
        {},
        get_mongo_db(),
    )
    return list(map(object_id_to_str, notifications))


def update_notification_read_status(user_id: str, notification_id: str, read: bool):
    try:
        if notification_id:
            return _MONGO_REPO_NOTIFICATION.update_one_by_query_upsert(
                {"user_id": user_id, "_id": ObjectId(notification_id)},
                {"read": read},
                get_mongo_db(),
            )
        else:
            return _MONGO_REPO_NOTIFICATION.update_many_by_query_upsert(
                {"user_id": user_id}, {"read": read}, get_mongo_db(),
            )
    except Exception:
        error_trace = "".join(traceback.format_exc())
        print(error_trace)
        return error_trace
