import httpx
import time
from typing import Dict, Any, Optional
from app.core.config import settings

# Global token store (for development only; use a database for production)
TOKEN_STORE = {}


def get_accela_config() -> Dict[str, str]:
    """Get Accela configuration from environment variables"""
    return {
        "app_id": settings.ACCELA_APP_ID,
        "app_secret": settings.ACCELA_APP_SECRET,
        "environment": settings.ACCELA_ENVIRONMENT,
        "agency": settings.ACCELA_AGENCY,
        "base_url": settings.ACCELA_BASE_URL,
        "authorization_url": settings.ACCELA_AUTHORIZATION_URL,
        "token_url": settings.ACCELA_TOKEN_URL,
        "redirect_uri": settings.ACCELA_REDIRECT_URI,
        "scope": settings.ACCELA_SCOPE
    }


async def get_accela_headers(authorization_token: str = None) -> Dict[str, str]:
    """Generate headers for Accela API requests"""
    config = get_accela_config()
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "x-accela-appid": config["app_id"],
        "x-accela-appsecret": config["app_secret"],
        "x-accela-environment": config["environment"],
        "x-accela-agency": config["agency"]
    }

    if authorization_token:
        # Send token without Bearer prefix (matching Flask implementation)
        headers["Authorization"] = authorization_token

    return headers


async def refresh_access_token(refresh_token: str) -> Optional[Dict[str, Any]]:
    """Refresh the access token using the refresh token."""
    try:
        payload = {
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
            "client_id": get_accela_config()["app_id"],
            "client_secret": get_accela_config()["app_secret"]
        }
        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        async with httpx.AsyncClient() as client:
            response = await client.post(
                get_accela_config()["token_url"],
                data=payload,
                headers=headers
            )

            if response.status_code == 200:
                token_data = response.json()
                token_data['expires_at'] = time.time() + token_data['expires_in']
                return token_data
            return None

    except Exception as e:
        print(f"Error refreshing token: {str(e)}")
        return None


async def get_valid_token() -> Optional[str]:
    """Retrieve a valid access token, refreshing it if it is near expiration."""
    token_data = TOKEN_STORE.get('token_data')
    if not token_data:
        return None

    # Refresh token 60 seconds before expiration
    if time.time() >= token_data['expires_at'] - 60:
        new_token_data = await refresh_access_token(token_data['refresh_token'])
        if new_token_data:
            TOKEN_STORE['token_data'] = new_token_data
            return new_token_data['access_token']
        else:
            return None
    return token_data['access_token']


async def exchange_code_for_token(code: str) -> Optional[Dict[str, Any]]:
    """Exchange authorization code for access token."""
    try:
        payload = {
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": get_accela_config()["redirect_uri"],
            "client_id": get_accela_config()["app_id"],
            "client_secret": get_accela_config()["app_secret"]
        }
        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        async with httpx.AsyncClient() as client:
            response = await client.post(
                get_accela_config()["token_url"],
                data=payload,
                headers=headers
            )

            if response.status_code == 200:
                token_data = response.json()
                token_data['expires_at'] = time.time() + token_data['expires_in']
                return token_data
            return None

    except Exception as e:
        print(f"Error exchanging code for token: {str(e)}")
        return None


async def make_accela_request(
    method: str,
    endpoint: str,
    authorization_token: str = None,
    data: Optional[Dict[str, Any]] = None,
    params: Optional[Dict[str, Any]] = None,
    timeout: float = 30.0
) -> Dict[str, Any]:
    """
    Make a request to the Accela API

    Args:
        method: HTTP method (GET, POST, PUT, DELETE)
        endpoint: API endpoint (e.g., '/records/{record_id}/conditions')
        authorization_token: Accela authorization token (optional if using stored token)
        data: Request body data (for POST, PUT requests)
        params: Query parameters (for GET requests)
        timeout: Request timeout in seconds

    Returns:
        Dictionary containing response data and status
    """
    try:
        # If no authorization token provided, try to get from store
        if not authorization_token:
            authorization_token = await get_valid_token()
            if not authorization_token:
                return {
                    "status_code": 401,
                    "data": None,
                    "error": "Authentication required"
                }

        headers = await get_accela_headers(authorization_token)
        url = f"{get_accela_config()['base_url']}{endpoint}"

        # Debug logging (remove in production)
        print(f"Making {method} request to: {url}")
        print(f"Headers: {headers}")
        print(f"Params: {params}")
        print(f"Data: {data}")

        async with httpx.AsyncClient() as client:
            if method.upper() == "GET":
                response = await client.get(
                    url,
                    headers=headers,
                    params=params,
                    timeout=timeout
                )
            elif method.upper() == "POST":
                response = await client.post(
                    url,
                    headers=headers,
                    json=data,
                    timeout=timeout
                )
            elif method.upper() == "PUT":
                response = await client.put(
                    url,
                    headers=headers,
                    json=data,
                    timeout=timeout
                )
            elif method.upper() == "DELETE":
                response = await client.delete(
                    url,
                    headers=headers,
                    timeout=timeout
                )
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            # Check if token is invalid and try to refresh
            if response.status_code == 401:
                token_data = TOKEN_STORE.get('token_data')
                if token_data and token_data.get('refresh_token'):
                    new_token_data = await refresh_access_token(token_data['refresh_token'])
                    if new_token_data:
                        TOKEN_STORE['token_data'] = new_token_data
                        # Retry the request with new token
                        headers["Authorization"] = f"Bearer {new_token_data['access_token']}"

                        if method.upper() == "GET":
                            response = await client.get(
                                url,
                                headers=headers,
                                params=params,
                                timeout=timeout
                            )
                        elif method.upper() == "POST":
                            response = await client.post(
                                url,
                                headers=headers,
                                json=data,
                                timeout=timeout
                            )
                        elif method.upper() == "PUT":
                            response = await client.put(
                                url,
                                headers=headers,
                                json=data,
                                timeout=timeout
                            )
                        elif method.upper() == "DELETE":
                            response = await client.delete(
                                url,
                                headers=headers,
                                timeout=timeout
                            )
                    else:
                        # Refresh failed, clear tokens
                        TOKEN_STORE.clear()
                        return {
                            "status_code": 401,
                            "data": None,
                            "error": "Token refresh failed, please re-authenticate"
                        }
                else:
                    # No refresh token available
                    TOKEN_STORE.clear()
                    return {
                        "status_code": 401,
                        "data": None,
                        "error": "Token expired, please re-authenticate"
                    }

            return {
                "status_code": response.status_code,
                "data": response.json() if response.status_code == 200 else None,
                "error": response.text if response.status_code != 200 else None
            }

    except httpx.TimeoutException:
        return {
            "status_code": 408,
            "data": None,
            "error": "Request timeout"
        }
    except httpx.RequestError as e:
        return {
            "status_code": 500,
            "data": None,
            "error": f"Request error: {str(e)}"
        }
    except Exception as e:
        return {
            "status_code": 500,
            "data": None,
            "error": f"Unexpected error: {str(e)}"
        }


def format_condition_data(condition_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format condition data according to Accela API requirements

    Args:
        condition_data: Raw condition data

    Returns:
        Formatted condition data
    """
    # Ensure all required fields are present with proper structure
    formatted_condition = {
        "name": condition_data.get("name", ""),
        "statusType": condition_data.get("statusType", "Applied"),
        "appliedDate": condition_data.get("appliedDate", ""),
        "longComments": condition_data.get("longComments", ""),
        "shortComments": condition_data.get("shortComments", ""),
        "statusDate": condition_data.get("statusDate", ""),
        "type": condition_data.get("type", {"value": "", "text": ""}),
        "group": condition_data.get("group", {"value": "", "text": ""}),
        "status": condition_data.get("status", {"value": "", "text": ""}),
        "appliedbyUser": condition_data.get("appliedbyUser", {"value": ""}),
        "severity": condition_data.get("severity", {"value": "", "text": ""}),
        "activeStatus": condition_data.get("activeStatus", {"value": "A", "text": "Active"}),
        "inheritable": condition_data.get("inheritable", {"value": "N", "text": "No"}),
        "displayNoticeInAgency": condition_data.get("displayNoticeInAgency", True),
        "isIncludeNameInNotice": condition_data.get("isIncludeNameInNotice", True),
        "isIncludeShortCommentsInNotice": condition_data.get("isIncludeShortCommentsInNotice", True),
        "displayNoticeInCitizens": condition_data.get("displayNoticeInCitizens", True),
        "displayNoticeInCitizensFee": condition_data.get("displayNoticeInCitizensFee", False)
    }

    return formatted_condition


def validate_record_id(record_id: str) -> bool:
    """
    Validate Accela record ID format

    Args:
        record_id: Record ID to validate

    Returns:
        True if valid, False otherwise
    """
    # Basic validation - Accela record IDs typically follow a specific format
    # This is a basic check, you may need to adjust based on your specific requirements
    if not record_id or len(record_id.strip()) == 0:
        return False

    # Add more specific validation rules as needed
    # For example, check for specific patterns, length, etc.

    return True


def extract_error_message(response_data: Dict[str, Any]) -> str:
    """
    Extract error message from Accela API response

    Args:
        response_data: Response data from Accela API

    Returns:
        Formatted error message
    """
    if isinstance(response_data, dict):
        # Check for common error fields in Accela responses
        error_fields = ["error", "message", "detail", "description"]
        for field in error_fields:
            if field in response_data:
                return str(response_data[field])

        # If no specific error field, return the whole response as string
        return str(response_data)

    return str(response_data)


def get_auth_url() -> str:
    """Generate OAuth2 authorization URL for Accela login"""
    config = get_accela_config()
    params = {
        "response_type": "code",
        "client_id": config["app_id"],
        "redirect_uri": config["redirect_uri"],
        "scope": config["scope"],
        "agency_name": config["agency"],
        "environment": config["environment"]
    }
    query_string = "&".join(f"{k}={v}" for k, v in params.items())
    return f"{config['authorization_url']}?{query_string}"


def get_auth_status() -> Dict[str, Any]:
    """Get current authentication status"""
    token_data = TOKEN_STORE.get('token_data')
    if token_data:
        current_time = time.time()
        expires_at = token_data.get('expires_at', 0)

        if current_time < expires_at:
            return {
                "authenticated": True,
                "expires_at": expires_at,
                "expires_in": int(expires_at - current_time),
                "token_type": token_data.get('token_type', 'Bearer')
            }
        else:
            # Token expired, try to refresh
            if token_data.get('refresh_token'):
                # Note: This would need to be async in practice
                return {
                    "authenticated": False,
                    "message": "Token expired, refresh available"
                }

            # Refresh failed or no refresh token
            TOKEN_STORE.clear()

    return {"authenticated": False}


def logout() -> Dict[str, str]:
    """Logout and clear tokens"""
    TOKEN_STORE.clear()
    return {"message": "Logged out successfully"}
