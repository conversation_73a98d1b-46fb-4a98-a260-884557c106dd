import glob
import os

from pdf2image import convert_from_path


def pdf_to_images(pdf_path, output_dir=None, dpi=300, fmt='jpg'):
    """
    Converts each page of the PDF at pdf_path to an image and saves them in a directory named after the PDF (without extension).
    Filenames are page1.jpg, page2.jpg, etc. Returns a list of file paths to the saved images.
    """
    pdf_basename = os.path.splitext(os.path.basename(pdf_path))[0]
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(pdf_path), pdf_basename)
    os.makedirs(output_dir, exist_ok=True)
    images = convert_from_path(pdf_path, dpi=dpi)
    image_paths = []
    # Map 'jpg' to 'JPEG' for Pillow
    save_fmt = 'JPEG' if fmt.lower() == 'jpg' else fmt.upper()
    for i, img in enumerate(images, start=1):
        out_path = os.path.join(output_dir, f'page{i}.{fmt}')
        img.save(out_path, save_fmt)
        image_paths.append(out_path)
    return image_paths


def convert_all_pdfs_in_dir(pdf_dir):
    pdf_files = glob.glob(os.path.join(pdf_dir, "*.pdf"))
    for pdf_file in pdf_files:
        print(f"Converting: {pdf_file}")
        pdf_to_images(pdf_file)

# Example usage:
# convert_all_pdfs_in_dir(r"C:\Users\<USER>\Downloads\plan-2\pavillion")
