import asyncio
import base64
import json
import logging
import os
import re
from datetime import datetime
from typing import Dict, List, Optional

import cv2
import litellm
from dotenv import load_dotenv
from PIL import Image
from pydantic import BaseModel

# Load environment variables
load_dotenv()

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Configure logger
logger = logging.getLogger('metadata_extractor')
logger.setLevel(logging.INFO)

# Remove any existing handlers to prevent duplicates
if logger.hasHandlers():
    logger.handlers.clear()

# Console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)

# File handler with daily files
current_date = datetime.now().strftime('%Y-%m-%d')
log_file = os.path.join('logs', f'metadata_extractor_{current_date}.log')
file_handler = logging.FileHandler(
    log_file,
    encoding='utf-8'
)
file_handler.setLevel(logging.INFO)
file_formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - [%(name)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
file_handler.setFormatter(file_formatter)

# Add both handlers to logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)

# Define supported models with their configurations
SUPPORTED_MODELS = {
    "claude-sonnet-4-20250514": {
        "provider": "anthropic",
        "max_tokens": 200000,
        "default_output_tokens": 15000
    },
    "claude-3-7-sonnet-latest": {
        "provider": "anthropic",
        "max_tokens": 200000,
        "default_output_tokens": 15000
    },
    "claude-3-5-sonnet-20240620": {
        "provider": "anthropic",
        "max_tokens": 200000,
        "default_output_tokens": 8192
    },
    "gpt-4.1": {
        "provider": "openai",
        "max_tokens": 128000,
        "default_output_tokens": 15000
    },
    "gpt-4-turbo": {
        "provider": "openai",
        "max_tokens": 4096,
        "max_input_tokens": 128000,
        "max_output_tokens": 4096,
        "default_output_tokens": 4096,
        "supports_function_calling": True,
        "supports_parallel_function_calling": True,
        "supports_vision": True,
        "supports_prompt_caching": True,
        "supports_system_messages": True,
        "supports_tool_choice": True,
        "supports_pdf_input": True
    },
    "openai/gpt-4-vision-preview": {
        "provider": "openai",
        "max_tokens": 128000,
        "default_output_tokens": 15000
    },
    "gpt-3.5-turbo": {
        "provider": "openai",
        "max_tokens": 4097,
        "max_input_tokens": 16385,
        "max_output_tokens": 4096,
        "default_output_tokens": 4096,
        "input_cost_per_token": 0.0000015,
        "output_cost_per_token": 0.000002,
        "supports_function_calling": True,
        "supports_prompt_caching": True,
        "supports_system_messages": True,
        "supports_tool_choice": True
    },
    "gpt-4o": {
        "provider": "openai",
        "max_tokens": 16384,
        "max_input_tokens": 128000,
        "max_output_tokens": 16384,
        "default_output_tokens": 16384,
        "supports_function_calling": True,
        "supports_parallel_function_calling": True,
        "supports_response_schema": True,
        "supports_vision": True,
        "supports_prompt_caching": True,
        "supports_system_messages": True,
        "supports_tool_choice": True,
        "supports_web_search": True,
        "supports_pdf_input": True
    },
    "gpt-4o-mini": {
        "provider": "openai",
        "max_tokens": 16384,
        "max_input_tokens": 128000,
        "max_output_tokens": 16384,
        "default_output_tokens": 16384,
        "input_cost_per_token": 0.00000015,
        "output_cost_per_token": 0.00000060,
        "supports_function_calling": True,
        "supports_parallel_function_calling": True,
        "supports_response_schema": True,
        "supports_vision": True,
        "supports_prompt_caching": True,
        "supports_system_messages": True,
        "supports_tool_choice": True,
        "supports_web_search": True,
        "supports_pdf_input": True
    }
}

# Load API keys
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

if not ANTHROPIC_API_KEY:
    logger.error("Anthropic API key not found in environment variables.")
    raise ValueError("Anthropic API key not found in environment variables")

if not OPENAI_API_KEY:
    logger.error("OpenAI API key not found in environment variables.")
    raise ValueError("OpenAI API key not found in environment variables")

# Configure litellm
litellm.api_keys = {
    "anthropic": ANTHROPIC_API_KEY,
    "openai": OPENAI_API_KEY
}

# Set default completion model configs
litellm.model_configs = {
    "claude-sonnet-4-20250514": {
        "api_key": ANTHROPIC_API_KEY,
        "api_base": "https://api.anthropic.com/v1"
    },
    "claude-3-5-sonnet-20240620": {
        "api_key": ANTHROPIC_API_KEY,
        "api_base": "https://api.anthropic.com/v1"
    },
    "gpt-4.1": {
        "api_key": OPENAI_API_KEY,
        "api_base": "https://api.openai.com/v1"
    },
    "openai/gpt-4-vision-preview": {
        "api_key": OPENAI_API_KEY,
        "api_base": "https://api.openai.com/v1"
    },
    "gpt-3.5-turbo": {
        "api_key": OPENAI_API_KEY,
        "api_base": "https://api.openai.com/v1"
    },
    "gpt-4o-mini": {
        "api_key": OPENAI_API_KEY,
        "api_base": "https://api.openai.com/v1"
    },
    "gpt-4-turbo": {
        "api_key": OPENAI_API_KEY,
        "api_base": "https://api.openai.com/v1"
    }
}


class MetadataResponse(BaseModel):
    request_id: str
    model_used: str
    metadata: Dict
    status: str
    processing_time: float
    token_usage: Optional[dict] = None


def reduce_image_for_api(image, max_size_bytes=5 * 1024 * 1024):
    """Reduce image size until its base64 representation is under max_size_bytes"""
    quality = 95
    scale = 1.0

    while True:
        # Apply current scale if needed
        if scale < 1.0:
            new_width = int(image.shape[1] * scale)
            new_height = int(image.shape[0] * scale)
            current_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        else:
            current_image = image

        # Encode with current quality
        _, buffer = cv2.imencode('.jpg', current_image, [cv2.IMWRITE_JPEG_QUALITY, quality])
        # Calculate the actual base64 string size that will be sent to the API
        base64_str = f"data:image/jpeg;base64,{base64.b64encode(buffer).decode('utf-8')}"
        base64_size = len(base64_str.encode('utf-8'))

        # Check if size is acceptable (leave some margin for safety)
        if base64_size <= max_size_bytes - 100:  # Leave 100 bytes margin
            return base64_str, current_image

        # Adjust parameters
        if quality > 30:
            # First try reducing quality
            quality -= 5
        else:
            # If quality is already low, reduce size
            scale *= 0.8  # More aggressive reduction (20% each time)

        # Prevent infinite loop
        if scale < 0.1:
            raise ValueError("Could not reduce image to required size while maintaining usable quality")


class MetadataExtractor:
    def __init__(self, model: str = "claude-sonnet-4-20250514"):
        self.model = model
        self.metadata = {
            "accessory_structure": "",
            "required_documents": [],
            "pages": {},
            "project_action": ""
        }
        self.request_id = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
        self.cost_tracking = {
            "total_cost": 0.0,
            "per_page_costs": {},
            "token_usage": {
                "total_prompt_tokens": 0,
                "total_completion_tokens": 0,
                "total_tokens": 0
            }
        }

        if model not in SUPPORTED_MODELS:
            raise ValueError(f"Unsupported model. Please use one of: {', '.join(SUPPORTED_MODELS.keys())}")

        self.model_config = SUPPORTED_MODELS[model]
        logger.info(f"Initialized MetadataExtractor with model: {model}")

    def _calculate_cost(self, token_usage: dict) -> float:
        """
        Calculate the cost based on token usage and model.
        All rates are per 1K tokens unless specified otherwise.
        """
        # Cost rates per 1K tokens
        COST_RATES = {
            "claude-sonnet-4-20250514": {
                "input": 0.003,  # $0.003 per 1K input tokens (3e-6 per token)
                "output": 0.015,  # $0.015 per 1K output tokens (15e-6 per token)
                "image": 0.0
            },
            "claude-3-7-sonnet-latest": {
                "input": 0.015,
                "output": 0.075,
                "image": 0.0
            },
            "claude-3-5-sonnet-20240620": {
                "input": 0.015,
                "output": 0.075,
                "image": 0.0
            },
            "gpt-4.1": {
                "input": 0.002,  # $0.002 per 1K input tokens
                "output": 0.008,  # $0.008 per 1K output tokens
                "image": 0.0
            },
            "gpt-4-turbo": {
                "input": 0.01,   # $0.01 per 1K input tokens (0.00001 per token)
                "output": 0.03,  # $0.03 per 1K output tokens (0.00003 per token)
                "image": 0.0
            },
            "gpt-4o": {
                "input": 0.005,  # $5.00 per 1M input tokens (0.000005 per token)
                "output": 0.02,  # $20.00 per 1M output tokens (0.00002 per token)
                "image": 0.0
            },
            "openai/gpt-4-vision-preview": {
                "input": 0.03,
                "output": 0.06,
                "image": 0.00765  # $0.00765 per image
            },
            "gpt-3.5-turbo": {
                "input": 0.0015,  # Converted to per 1K tokens
                "output": 0.002,  # Converted to per 1K tokens
                "image": 0.0
            },
            "gpt-4o-mini": {
                "input": 0.00015,  # Converted to per 1K tokens
                "output": 0.00060,  # Converted to per 1K tokens
                "image": 0.0
            }
        }

        if self.model not in COST_RATES:
            logger.warning(f"No cost rate defined for model {self.model}, using conservative default rates")
            rates = {"input": 0.0015, "output": 0.002, "image": 0.0}  # Conservative default
        else:
            rates = COST_RATES[self.model]

        # Calculate text token costs (per 1K tokens)
        input_cost = (token_usage.get('prompt_tokens', 0) / 1000) * rates['input']
        output_cost = (token_usage.get('completion_tokens', 0) / 1000) * rates['output']

        # Calculate image costs if applicable
        image_cost = 0.0
        if 'image_tokens' in token_usage and rates['image'] > 0:
            image_cost = token_usage['image_tokens'] * rates['image']

        total_cost = input_cost + output_cost + image_cost

        # Log detailed cost breakdown
        logger.debug(f"Cost breakdown for {self.model}:")
        logger.debug(f"  Input tokens: {token_usage.get('prompt_tokens', 0)}")
        logger.debug(f"  Output tokens: {token_usage.get('completion_tokens', 0)}")
        logger.debug(f"  Image tokens: {token_usage.get('image_tokens', 0)}")
        logger.debug(f"  Input cost: ${input_cost:.6f}")
        logger.debug(f"  Output cost: ${output_cost:.6f}")
        logger.debug(f"  Image cost: ${image_cost:.6f}")
        logger.debug(f"  Total cost: ${total_cost:.6f}")

        return total_cost

    def _update_cost_tracking(self, page_path: str, token_usage: dict):
        """
        Update cost tracking information.
        """
        cost = self._calculate_cost(token_usage)

        # Update total cost
        self.cost_tracking["total_cost"] += cost

        # Update per-page cost
        self.cost_tracking["per_page_costs"][page_path] = {
            "cost": cost,
            "token_usage": token_usage,
            "timestamp": datetime.now().isoformat()
        }

        # Update total token usage
        self.cost_tracking["token_usage"]["total_prompt_tokens"] += token_usage.get('prompt_tokens', 0)
        self.cost_tracking["token_usage"]["total_completion_tokens"] += token_usage.get('completion_tokens', 0)
        self.cost_tracking["token_usage"]["total_tokens"] += token_usage.get('total_tokens', 0)

        logger.info(f"Cost for {page_path}: ${cost:.4f}")
        logger.info(f"Total cost so far: ${self.cost_tracking['total_cost']:.4f}")

    def _save_metadata(self, base_dir: str, page_path: str):
        """
        Save the current metadata to JSON file.
        """
        datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save metadata - use a single file per directory with array of entries
        metadata_file = os.path.join(base_dir, "metadata.json")

        # Create a new metadata entry with timestamp
        new_entry = {
            "page_path": page_path,
            "metadata": self.metadata
        }

        # If file exists, append new entry to existing array
        if os.path.exists(metadata_file):
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    existing_metadata = json.load(f)

                # Ensure existing_metadata is a list
                if not isinstance(existing_metadata, list):
                    existing_metadata = [existing_metadata]

                # Simply append the new entry
                existing_metadata.append(new_entry)

                # Save the updated array
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_metadata, f, indent=2, ensure_ascii=False)
            except Exception as e:
                logger.error(f"Error appending to metadata: {str(e)}")
                # If append fails, create new array with current entry
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump([new_entry], f, indent=2, ensure_ascii=False)
        else:
            # If file doesn't exist, create new array with first entry
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump([new_entry], f, indent=2, ensure_ascii=False)

        # Format cost tracking data for better readability
        formatted_cost_tracking = {
            "total_cost": f"${self.cost_tracking['total_cost']:.4f}",
            "per_page_costs": {
                page_path: {
                    "cost": f"${cost_data['cost']:.4f}",
                    "token_usage": cost_data['token_usage'],
                    "timestamp": cost_data['timestamp']
                }
                for page_path, cost_data in self.cost_tracking['per_page_costs'].items()
            },
            "token_usage": self.cost_tracking['token_usage']
        }

        # Save cost tracking
        cost_file = os.path.join(base_dir, "metadata_cost_tracking.json")
        with open(cost_file, 'w', encoding='utf-8') as f:
            json.dump(formatted_cost_tracking, f, indent=2, ensure_ascii=False)

        logger.info(f"Metadata saved to: {metadata_file}")
        logger.info(f"Cost tracking saved to: {cost_file}")

    def ensure_json_response(self, response_content):
        try:
            # Try to parse the entire response as JSON
            return json.loads(response_content)
        except json.JSONDecodeError:
            # If that fails, try to extract JSON from the response
            try:
                # Look for the first '{' and last '}'
                start = response_content.index('{')
                end = response_content.rindex('}') + 1
                json_str = response_content[start:end]
                return json.loads(json_str)
            except (ValueError, json.JSONDecodeError):
                # If all else fails, return an error JSON object
                return {"error": "Failed to parse response as JSON", "original_response": response_content}

    async def find_accessory_structure(self, image_paths: List[str]) -> str:
        """
        Find accessory structure by analyzing pages one by one until found.

        Args:
            image_paths: List of paths to page image files

        Returns:
            String containing the accessory structure type or empty string if not found
        """
        for i, image_path in enumerate(image_paths):
            page_num = i + 1
            logger.info(f"Analyzing page {page_num} for accessory structure...")

            try:
                # Read and process the image
                image = cv2.imread(image_path)
                if image is None:
                    logger.warning(f"Could not read image file: {image_path}")
                    continue

                # Reduce image size for API if needed
                try:
                    img_base64, _ = reduce_image_for_api(image)
                except ValueError as e:
                    logger.warning(f"Error processing image {image_path}: {str(e)}")
                    continue

                # Prepare the prompt for accessory structure detection
                prompt = """
                Analyze this construction plan page image and identify if it shows plans for an accessory structure.

                CRITICAL REQUIREMENTS:
                1. EXACT VISUAL REPRODUCTION: Extract every single character, symbol, number, and text element exactly as it visually appears
                2. COMPLETE VISUAL EXTRACTION: Capture ALL visible text including headers, footers, page numbers, watermarks, marginalia
                3. STRUCTURAL PRESERVATION: Maintain original document structure including section numbering, page layout, and organization

                Please analyze this page and provide your response in the following JSON structure:
                {
                    "accessory_structure": "shed/garage/pool house/workshop/carport/deck/patio/fence/retaining wall/steel building etc or 'none' if not applicable",
                    "project_action": "building/demolishing/alteration/other (describe)",
                    "confidence": 0-100,
                    "evidence": "Brief explanation of why this is or isn't an accessory structure and the project action",
                }

                IMPORTANT GUIDELINES:
                - Look for keywords like "shed", "garage", "pool house", "workshop", "carport", "deck", "patio", "fence", "retaining wall", etc.
                - Check for building codes, permit types, or project descriptions that indicate accessory structures
                - For project_action, look for words like "new construction", "demolition", "alteration", "addition", "remodel", etc. and extract the most relevant action
                - If the page shows main building plans, return "none" for accessory_structure
                - Provide high confidence only if you're certain about the accessory structure type and project action
                - Include all relevant text that supports your identification

                Ensure all text content is preserved exactly as it appears in the image, with no modifications or interpretations.
                """

                # Set up messages based on model provider
                if self.model_config["provider"] == "anthropic":
                    messages = [{
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": img_base64}
                        ]
                    }]
                else:  # OpenAI
                    messages = [{
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": img_base64}
                        ]
                    }]

                # Use LLM to analyze the image
                completion = await litellm.acompletion(
                    model=self.model,
                    messages=messages,
                    response_format={"type": "json_object"},
                    max_tokens=2000,
                    temperature=0.1
                )

                # Extract the response content
                content = completion.choices[0].message.content.strip()
                logger.info(f"Page {page_num} accessory structure analysis: {content}")

                try:
                    page_analysis = self.ensure_json_response(content)
                    accessory_structure = page_analysis.get('accessory_structure', '').strip()
                    project_action = page_analysis.get('project_action', '').strip()
                    confidence = page_analysis.get('confidence', 0)

                    # If we found an accessory structure with reasonable confidence, return it
                    if accessory_structure and accessory_structure.lower() != 'none' and confidence >= 70:
                        logger.info(f"Found accessory structure '{accessory_structure}' on page {page_num} with confidence {confidence}% and project action '{project_action}'")
                        # Store project_action in metadata
                        self.metadata['project_action'] = project_action
                        return accessory_structure
                    elif accessory_structure and accessory_structure.lower() != 'none' and confidence >= 50:
                        logger.info(f"Found potential accessory structure '{accessory_structure}' on page {page_num} with confidence {confidence}% and project action '{project_action}'")
                        self.metadata['project_action'] = project_action
                        return accessory_structure
                    # If not found, still store project_action if present
                    if project_action:
                        self.metadata['project_action'] = project_action
                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing JSON response for page {page_num}: {str(e)}")
                    continue

                # Update cost tracking
                if hasattr(completion, 'usage'):
                    token_usage = {
                        "completion_tokens": getattr(completion.usage, 'completion_tokens', 0),
                        "prompt_tokens": getattr(completion.usage, 'prompt_tokens', 0),
                        "total_tokens": getattr(completion.usage, 'total_tokens', 0)
                    }
                    self._update_cost_tracking(image_path, token_usage)

            except Exception as e:
                logger.error(f"Error analyzing page {page_num} for accessory structure: {str(e)}")
                continue

        logger.info("No accessory structure found in any page")
        return ""

    async def find_required_documents(self, accessory_structure: str) -> List[str]:
        """
        Find required documents based on the accessory structure using completion.

        Args:
            accessory_structure: The type of accessory structure found

        Returns:
            List of required document types
        """
        try:
            prompt = f"""
            Based on the accessory structure type "{accessory_structure}", determine what documents are typically required for building permits.

            Please provide your response in the following JSON structure:
            {{
                "required_documents": [],
                "reasoning": "Explanation of why these documents are required for this type of accessory structure"
            }}

            IMPORTANT GUIDELINES:
            - Consider the specific requirements for {accessory_structure}
            - Include standard documents like site plan, construction drawings etc if needed
            - Add specialized documents based on the structure type (e.g., electrical for workshops, plumbing for pool houses) if needed
            - Consider local building codes and permit requirements if needed
            - Include structural details if the structure has significant size or complexity if needed
            - Include specifications for materials and construction methods if needed
            - Consider energy calculations for larger structures if needed
            - Include accessibility compliance if the structure is accessible to the public if needed
            - Include fire safety plans for structures with electrical or heating systems if needed
            - Include landscape/drainage plans if the structure affects site drainage if needed
            - Include utility plans if the structure requires utilities if needed
            - Include demolition plans if this involves removing existing structures if needed
            - Include existing conditions if this is an addition or modification if needed
            - Include as-built survey if this is a significant modification if needed

            Only include documents that are actually required for this specific type of accessory structure.
            """

            # Set up messages
            messages = [{
                "role": "user",
                "content": prompt
            }]

            # Use LLM to determine required documents
            completion = await litellm.acompletion(
                model=self.model,
                messages=messages,
                response_format={"type": "json_object"},
                max_tokens=2000,
                temperature=0.1
            )

            # Extract the response content
            content = completion.choices[0].message.content.strip()
            logger.info(f"Required documents analysis: {content}")

            try:
                analysis = self.ensure_json_response(content)
                required_docs = analysis.get('required_documents', [])
                reasoning = analysis.get('reasoning', '')

                logger.info(f"Determined required documents: {required_docs}")
                logger.info(f"Reasoning: {reasoning}")

                return required_docs

            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON response for required documents: {str(e)}")
                # Return default documents if parsing fails
                return ["site plan", "elevation view", "floor plan"]

            # Update cost tracking
            if hasattr(completion, 'usage'):
                token_usage = {
                    "completion_tokens": getattr(completion.usage, 'completion_tokens', 0),
                    "prompt_tokens": getattr(completion.usage, 'prompt_tokens', 0),
                    "total_tokens": getattr(completion.usage, 'total_tokens', 0)
                }
                self._update_cost_tracking("required_documents_analysis", token_usage)

        except Exception as e:
            logger.error(f"Error finding required documents: {str(e)}")
            # Return default documents if analysis fails
            return ["site plan", "elevation view", "floor plan"]

    async def analyze_page_detailed(self, image_path: str, page_num: int, accessory_structure: str, required_documents: List[str]) -> Dict:
        """
        Perform detailed page analysis with context from accessory structure and required documents.

        Args:
            image_path: Path to the page image file
            page_num: Page number (1-based)
            accessory_structure: The identified accessory structure type
            required_documents: List of required document types

        Returns:
            Dictionary containing detailed page analysis
        """
        try:
            print("image_path", image_path)
            # Read and process the image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not read image file: {image_path}")

            # Reduce image size for API if needed
            try:
                img_base64, _ = reduce_image_for_api(image)
            except ValueError as e:
                raise ValueError(f"Error processing image {image_path}: {str(e)}")

            # Prepare the prompt for detailed page analysis
            base_prompt = f"""
            Analyze this construction plan page image with the following context:
            - Accessory Structure: {accessory_structure}
            - Required Documents: {', '.join(required_documents)}

            CRITICAL REQUIREMENTS:
            1. EXACT VISUAL REPRODUCTION: Extract every single character, symbol, number, and text element exactly as it visually appears
            2. COMPLETE VISUAL EXTRACTION: Capture ALL visible text including headers, footers, page numbers, watermarks, marginalia
            3. STRUCTURAL PRESERVATION: Maintain original document structure including section numbering, page layout, and organization

            Please analyze this page and provide your response in the following JSON structure:
            {{
                "page_analysis": {{
                    "plan_type": "site plan/elevation view/floor plan/foundation plan/roof plan/electrical plan/plumbing plan/mechanical plan/structural details/specifications/energy calculations/accessibility compliance/fire safety plan/landscape plan/drainage plan/grading plan/utility plan/demolition plan/existing conditions/as-built survey/other",
                    "is_this_plan_need": true/false,
                    "has_plan_drawings": true/false,
                    "num_plan_drawings": 1-10,
                    "has_plan_images": true/false,
                    "num_plan_images": 1-10,
                    "has_text": true/false,
                    "percentage_of_text": 0-100,
                    "percentage_of_plan_drawings": 0-100,
                    "percentage_of_image": 0-100,
                    "confidence": 0-100,
                    "notes": "why this confidence level and what is lack of confidence"
                }}
            }}

            IMPORTANT GUIDELINES:
            - For plan_type: Identify the specific type of plan shown on this page
            - For is_this_plan_need: Determine if this type of plan is Strictly required for the {accessory_structure} project
            - For has_plan_drawings: Check if there is construction drawings in the black and white (not just text)
            - For num_plan_drawings: Estimate the number of distinct visible construction drawings on the page (1-10)
            - For has_plan_images: Check if the page contains actual color images representing the {accessory_structure} (not just text)
            - For num_plan_images: Estimate the number of distinct color images visible representing the {accessory_structure} on the page (1-10)
            - For has_text: True if there is any text that is critical for understanding the plan or project
            - For percentage_of_text: Estimate the percentage (0-100) of the page area or content that is important text
            - For percentage_of_plan_drawings: Estimate the percentage (0-100) of the page area or content that is construction drawings in black and white
            - For percentage_of_image: Estimate the percentage (0-100) of the page area or content that is color images representing the {accessory_structure}
            - For confidence: Provide a confidence level (0-100) for your analysis
            - For notes: why this confidence level and what is lack of confidence
            - Consider the required documents list when determining if this plan is needed

            Ensure all text content is preserved exactly as it appears in the image, with no modifications or interpretations.
            """

            # Set up messages based on model provider
            if self.model_config["provider"] == "anthropic":
                messages = [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": base_prompt},
                        {"type": "image_url", "image_url": img_base64}
                    ]
                }]
            else:  # OpenAI
                messages = [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": base_prompt},
                        {"type": "image_url", "image_url": img_base64}
                    ]
                }]

            # Use LLM to analyze the image
            completion = await litellm.acompletion(
                model=self.model,
                messages=messages,
                response_format={"type": "json_object"},
                max_tokens=self.model_config["default_output_tokens"],
                temperature=0.1
            )

            # Extract the response content
            content = completion.choices[0].message.content.strip()
            logger.info(f"Page {page_num} detailed analysis: {content}")

            try:
                page_metadata = self.ensure_json_response(content)

                # Update cost tracking
                if hasattr(completion, 'usage'):
                    token_usage = {
                        "completion_tokens": getattr(completion.usage, 'completion_tokens', 0),
                        "prompt_tokens": getattr(completion.usage, 'prompt_tokens', 0),
                        "total_tokens": getattr(completion.usage, 'total_tokens', 0)
                    }
                    self._update_cost_tracking(image_path, token_usage)

                return page_metadata

            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON response: {str(e)}")
                logger.error(f"Raw content: {content}")
                raise ValueError("Failed to parse LLM response into JSON format")

        except Exception as e:
            logger.error(f"Error in detailed page analysis for page {page_num}: {str(e)}")
            raise

    async def process_pages_sequential(self, image_paths: List[str], status_queue=None) -> MetadataResponse:
        """
        Process pages sequentially: find accessory structure, then required documents, then detailed analysis.
        Args:
            image_paths: List of paths to page image files
            status_queue: Optional asyncio.Queue for streaming progress events
        Returns:
            MetadataResponse object containing the complete metadata
        """
        start_time = datetime.now()
        try:
            if status_queue:
                await status_queue.put({"event_type": "sequential_start", "message": "Sequential processing started", "total_pages": len(image_paths)})
            # Step 1: Find accessory structure by analyzing pages one by one
            logger.info("Step 1: Finding accessory structure...")
            if status_queue:
                await status_queue.put({"event_type": "accessory_structure_start", "message": "Finding accessory structure..."})
            accessory_structure = await self.find_accessory_structure(image_paths)
            if accessory_structure:
                self.metadata['accessory_structure'] = accessory_structure
                logger.info(f"Found accessory structure: {accessory_structure}")
                if status_queue:
                    await status_queue.put({"event_type": "accessory_structure_found", "message": f"Found accessory structure: {accessory_structure}"})
            else:
                self.metadata['accessory_structure'] = "none"
                logger.info("No accessory structure found, setting to 'none'")
                if status_queue:
                    await status_queue.put({"event_type": "accessory_structure_none", "message": "No accessory structure found"})
            # Step 2: Find required documents using completion
            logger.info("Step 2: Finding required documents...")
            if status_queue:
                await status_queue.put({"event_type": "required_documents_start", "message": "Finding required documents..."})
            required_documents = await self.find_required_documents(accessory_structure)
            self.metadata['required_documents'] = required_documents
            logger.info(f"Determined required documents: {required_documents}")
            if status_queue:
                await status_queue.put({"event_type": "required_documents_found", "message": f"Required documents: {required_documents}", "required_documents": required_documents})
            # Step 3: Perform detailed page analysis concurrently
            logger.info("Step 3: Performing detailed page analysis...")
            if status_queue:
                await status_queue.put({"event_type": "detailed_analysis_start", "message": "Starting detailed page analysis", "total_pages": len(image_paths)})
            analysis_tasks = []
            for i, image_path in enumerate(image_paths):
                page_num = i + 1
                logger.info(f"Creating analysis task for page {page_num}...")
                if status_queue:
                    await status_queue.put({"event_type": "page_analysis_start", "message": f"Analyzing page {page_num}", "page": page_num, "page_path": image_path})
                task = self.analyze_page_detailed(image_path, page_num, accessory_structure, required_documents)
                analysis_tasks.append((page_num, image_path, task))
            if analysis_tasks:
                try:
                    tasks = [task for _, _, task in analysis_tasks]
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    for i, (page_num, image_path, _) in enumerate(analysis_tasks):
                        result = results[i]
                        if isinstance(result, Exception):
                            logger.error(f"Error in detailed analysis for page {page_num}: {str(result)}")
                            if status_queue:
                                await status_queue.put({"event_type": "page_analysis_error", "message": f"Error analyzing page {page_num}", "page": page_num, "error": str(result)})
                            continue
                        try:
                            page_metadata = result
                            page_filename = os.path.basename(image_path)
                            if 'page_analysis' in page_metadata:
                                self.metadata['pages'][page_filename] = {
                                    "plan_type": page_metadata['page_analysis'].get('plan_type', 'unknown'),
                                    "is_this_plan_need": page_metadata['page_analysis'].get('is_this_plan_need', False),
                                    "has_plan_drawings": page_metadata['page_analysis'].get('has_plan_drawings', False),
                                    "num_plan_drawings": page_metadata['page_analysis'].get('num_plan_drawings', 0),
                                    "has_plan_images": page_metadata['page_analysis'].get('has_plan_images', False),
                                    "num_plan_images": page_metadata['page_analysis'].get('num_plan_images', 0),
                                    "has_text": page_metadata['page_analysis'].get('has_text', False),
                                    "percentage_of_text": page_metadata['page_analysis'].get('percentage_of_text', 0),
                                    "percentage_of_plan_drawings": page_metadata['page_analysis'].get('percentage_of_plan_drawings', 0),
                                    "percentage_of_image": page_metadata['page_analysis'].get('percentage_of_image', 0),
                                    "confidence": page_metadata['page_analysis'].get('confidence', 0),
                                    "notes": page_metadata['page_analysis'].get('notes', ''),
                                }
                            logger.info(f"Completed detailed analysis for page {page_num}")
                            if status_queue:
                                await status_queue.put({"event_type": "page_analysis_complete", "message": f"Completed analysis for page {page_num}", "page": page_num, "page_path": image_path, "page_metadata": page_metadata})
                        except Exception as e:
                            logger.error(f"Error processing result for page {page_num}: {str(e)}")
                            if status_queue:
                                await status_queue.put({"event_type": "page_analysis_error", "message": f"Error processing result for page {page_num}", "page": page_num, "error": str(e)})
                            continue
                except Exception as e:
                    logger.error(f"Error in concurrent page analysis: {str(e)}")
                    if status_queue:
                        await status_queue.put({"event_type": "detailed_analysis_error", "message": f"Error in concurrent page analysis: {str(e)}"})
                    for page_num, image_path, task in analysis_tasks:
                        try:
                            page_metadata = await task
                            page_filename = os.path.basename(image_path)
                            if 'page_analysis' in page_metadata:
                                self.metadata['pages'][page_filename] = {
                                    "plan_type": page_metadata['page_analysis'].get('plan_type', 'unknown'),
                                    "is_this_plan_need": page_metadata['page_analysis'].get('is_this_plan_need', False),
                                    "has_plan_drawings": page_metadata['page_analysis'].get('has_plan_drawings', False),
                                    "num_plan_drawings": page_metadata['page_analysis'].get('num_plan_drawings', 0),
                                    "has_plan_images": page_metadata['page_analysis'].get('has_plan_images', False),
                                    "num_plan_images": page_metadata['page_analysis'].get('num_plan_images', 0),
                                    "has_text": page_metadata['page_analysis'].get('has_text', False),
                                    "percentage_of_text": page_metadata['page_analysis'].get('percentage_of_text', 0),
                                    "percentage_of_plan_drawings": page_metadata['page_analysis'].get('percentage_of_plan_drawings', 0),
                                    "percentage_of_image": page_metadata['page_analysis'].get('percentage_of_image', 0),
                                    "confidence": page_metadata['page_analysis'].get('confidence', 0),
                                    "notes": page_metadata['page_analysis'].get('notes', ''),
                                }
                            logger.info(f"Completed detailed analysis for page {page_num}")
                            if status_queue:
                                await status_queue.put({"event_type": "page_analysis_complete", "message": f"Completed analysis for page {page_num}", "page": page_num, "page_path": image_path, "page_metadata": page_metadata})
                        except Exception as e:
                            logger.error(f"Error in detailed analysis for page {page_num}: {str(e)}")
                            if status_queue:
                                await status_queue.put({"event_type": "page_analysis_error", "message": f"Error in detailed analysis for page {page_num}", "page": page_num, "error": str(e)})
                            continue
            processing_time = (datetime.now() - start_time).total_seconds()
            response = MetadataResponse(
                request_id=self.request_id,
                model_used=self.model,
                metadata=self.metadata,
                status="success",
                processing_time=processing_time,
                token_usage=self.cost_tracking["token_usage"]
            )
            if status_queue:
                await status_queue.put({"event_type": "sequential_complete", "message": "Sequential processing complete", "processing_time": processing_time, "metadata": self.metadata})
            return response
        except Exception as e:
            logger.error(f"Error in sequential processing: {str(e)}", exc_info=True)
            if status_queue:
                await status_queue.put({"event_type": "sequential_error", "message": str(e)})
            raise

    async def wait_for_response(self, image_path: str, page_num: int, max_retries: int = 1, retry_delay: float = 2.0) -> Optional[MetadataResponse]:
        """
        Wait for a valid response from page analysis with retries.

        Args:
            image_path: Path to the page image file
            page_num: Page number (1-based)
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds

        Returns:
            MetadataResponse if successful, None if all retries failed
        """
        for retry in range(max_retries):
            try:
                response = await self.process_pages_sequential([image_path])
                if response and response.status == "success":
                    if response.metadata and isinstance(response.metadata, dict):
                        return response
                    else:
                        logger.warning(f"Invalid metadata in response, retry {retry + 1}/{max_retries}")
                else:
                    logger.warning(f"Response not complete, retry {retry + 1}/{max_retries}")
            except Exception as e:
                logger.error(f"Error processing page {page_num} on retry {retry + 1}: {str(e)}")

            if retry < max_retries - 1:
                await asyncio.sleep(retry_delay)

        logger.error(f"Failed to get valid response for page {page_num} after {max_retries} retries")
        return None

    def get_metadata_context(self) -> str:
        """Get the current state of the metadata as context for next page analysis."""
        return json.dumps(self.metadata, indent=2)

    def get_metadata(self) -> Dict:
        """Get the current state of the metadata."""
        return self.metadata

    def save_root_metadata(self, pdf_dir: str, metadata_data: dict):
        """
        Save metadata to the root metadata file.

        Args:
            pdf_dir: Directory containing the PDF processing results
            metadata_data: Metadata data to save
        """
        root_metadata_file = os.path.join(pdf_dir, "root_metadata.json")

        try:
            # Save the metadata directly to root metadata
            with open(root_metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata_data, f, indent=2, ensure_ascii=False)

            logger.info("Updated root metadata file")

        except Exception as e:
            logger.error(f"Error saving root metadata: {str(e)}")
            raise

    def load_root_metadata(self, pdf_dir: str) -> dict:
        """
        Load the root metadata file.

        Args:
            pdf_dir: Directory containing the PDF processing results

        Returns:
            Dictionary containing the root metadata data
        """
        root_metadata_file = os.path.join(pdf_dir, "root_metadata.json")

        try:
            if os.path.exists(root_metadata_file):
                with open(root_metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading root metadata: {str(e)}")
            return {}

    def clear_metadata(self):
        """Clear the metadata."""
        self.metadata = {
            "accessory_structure": "",
            "required_documents": [],
            "pages": {},
            "project_action": ""
        }
        logger.info("Metadata cleared")


async def extract_horizontal_pairs_knowledge(page_key, split_n, split_output_dir, metadata, all_knowledge, status_queue=None, base_progress=0):
    pair_json_files = []
    if status_queue:
        await status_queue.put({"event_type": "knowledge_page_start", "message": f"Starting knowledge extraction for page {page_key}", "page_key": page_key, "split_n": split_n, "progress": base_progress})
    # Create tasks for all horizontal pairs
    tasks = []
    task_info = []  # Store info about each task for later processing
    total_pairs = 0
    for row in range(max(2, split_n)):
        for col in range(split_n - 1):
            total_pairs += 1

    for row in range(max(2, split_n)):
        for col in range(split_n - 1):
            section1 = os.path.join(split_output_dir, f"{row + 1}_{col + 1}.png")
            section2 = os.path.join(split_output_dir, f"{row + 1}_{col + 2}.png")
            joint = os.path.join(split_output_dir, f"joint_h_{row + 1}_{col + 1}_{row + 1}_{col + 2}.png")
            if status_queue:
                await status_queue.put({"event_type": "knowledge_pair_start", "message": f"Preparing pair {row + 1}_{col + 1} and {row + 1}_{col + 2}", "page_key": page_key, "row": row + 1, "col": col + 1, "progress": base_progress + 1})
            # Only process if all three images exist
            if all(os.path.exists(p) for p in [section1, section2, joint]):
                # analyst_context = "image" if (metadata["pages"][page_key]["has_plan_drawings"] or metadata["pages"][page_key]["has_plan_images"]) and (metadata["pages"][page_key]["percentage_of_plan_drawings"] + metadata["pages"][page_key]["percentage_of_image"]) > metadata["pages"][page_key]["percentage_of_text"] else "text"
                analyst_context = 'text'

                async def process_pair(s1, s2, j, context, r, c, status_queue=None):
                    from app.utils.knowledge import KnowledgeBuilder
                    kb = KnowledgeBuilder(model="gpt-4o")
                    try:
                        result = await kb.wait_for_response([s1, s2, j], metadata["accessory_structure"], metadata["pages"][page_key]["plan_type"], context)
                        if status_queue:
                            await status_queue.put({"event_type": "knowledge_pair_complete", "message": f"Completed pair {r + 1}_{c + 1} and {r + 1}_{c + 2}", "page_key": page_key, "row": r + 1, "col": c + 1, "progress": base_progress + 3})
                        return result
                    except Exception as e:
                        if status_queue:
                            await status_queue.put({"event_type": "knowledge_error", "message": f"Error processing pair {r + 1}_{c + 1} and {r + 1}_{c + 2}: {str(e)}", "page_key": page_key, "row": r + 1, "col": c + 1, "error": str(e), "progress": base_progress + 3})
                        return None
                task = process_pair(section1, section2, joint, analyst_context, row, col, status_queue)
                tasks.append(task)
                task_info.append({
                    "row": row,
                    "col": col,
                    "section1": section1,
                    "section2": section2,
                    "joint": joint,
                    "analyst_context": analyst_context
                })
            else:
                if status_queue:
                    await status_queue.put({"event_type": "knowledge_error", "message": f"Missing files for pair {row + 1}_{col + 1} and {row + 1}_{col + 2}", "page_key": page_key, "row": row + 1, "col": col + 1, "progress": base_progress + 1})
    # Process all pairs concurrently using asyncio.gather
    if tasks:
        try:
            responses = await asyncio.gather(*tasks, return_exceptions=True)
            for i, response in enumerate(responses):
                info = task_info[i]
                row, col = info["row"], info["col"]
                if isinstance(response, Exception):
                    if status_queue:
                        await status_queue.put({"event_type": "knowledge_error", "message": f"Error processing pair {row + 1}_{col + 1} and {row + 1}_{col + 2}: {str(response)}", "page_key": page_key, "row": row + 1, "col": col + 1, "error": str(response), "progress": base_progress + 4})
                    continue
                if response and hasattr(response, 'knowledge_base') and response.knowledge_base:
                    pair_name = f"knowledge_{row + 1}_{col + 1}_{row + 1}_{col + 2}.json"
                    pair_path = os.path.join(split_output_dir, pair_name)
                    knowledge_to_save = response.knowledge_base
                    if not isinstance(knowledge_to_save, dict):
                        knowledge_to_save = {"error": "Invalid knowledge structure", "original": knowledge_to_save}
                    with open(pair_path, 'w', encoding='utf-8') as f:
                        json.dump(knowledge_to_save, f, indent=2, ensure_ascii=False)
                    pair_json_files.append({
                        "pair": [f"{row + 1}_{col + 1}", f"{row + 1}_{col + 2}"],
                        "file": pair_name
                    })
        except Exception as e:
            if status_queue:
                await status_queue.put({"event_type": "knowledge_error", "message": f"Error in concurrent processing: {str(e)}", "page_key": page_key, "error": str(e), "progress": base_progress + 4})
    if status_queue:
        await status_queue.put({"event_type": "knowledge_page_complete", "message": f"Completed knowledge extraction for page {page_key}", "page_key": page_key, "num_pairs": len(pair_json_files), "progress": base_progress + 5})
    return pair_json_files


async def process_3x3_sections_for_needed_plans(pdf_dir: str, knowledge_model: str = "gpt-4o", status_queue=None):
    """
    After root_metadata.json is created, this function:
    - Loads root_metadata.json
    - Filters pages where is_this_plan_need == True
    - For each such page, splits the full_page.jpg into 3x3 or 2x2 sections
    - For each horizontal pair in the first and second row, uses process_image from KnowledgeBuilder to extract knowledge for (section1, section2, their horizontal joint)
    - Saves the knowledge in the split_output_dir
    - Aggregates and saves a root_knowledge.json in the parent directory
    - Updates the root_metadata.json file with the extracted knowledge for each page as soon as it's processed, reading from the saved knowledge JSON files
    Args:
        pdf_dir: Directory containing the PDF processing results (where root_metadata.json is located)
        knowledge_model: Model to use for knowledge extraction
        status_queue: Optional asyncio.Queue for streaming progress events
    """
    import json
    import os

    from app.utils.knowledge import KnowledgeBuilder

    # Load root_metadata.json
    root_metadata_file = os.path.join(pdf_dir, "root_metadata.json")
    if not os.path.exists(root_metadata_file):
        if status_queue:
            await status_queue.put({"event_type": "knowledge_error", "message": "root_metadata.json not found", "progress": 65})
        return

    with open(root_metadata_file, 'r', encoding='utf-8') as f:
        metadata = json.load(f)

    # Filter pages where is_this_plan_need == True
    needed_pages = []
    if "pages" in metadata:
        for page_key, page_data in metadata["pages"].items():
            if page_data.get("is_this_plan_need", False):
                needed_pages.append(page_key)

    if not needed_pages:
        if status_queue:
            await status_queue.put({"event_type": "knowledge_no_pages", "message": "No pages marked as needed for knowledge extraction", "progress": 70})
        return

    if status_queue:
        await status_queue.put({"event_type": "knowledge_start", "message": f"Starting knowledge extraction for {len(needed_pages)} pages", "total_pages": len(needed_pages), "progress": 70})

    all_knowledge = {}

    for idx, page_key in enumerate(needed_pages):
        if status_queue:
            # Progress from 70 to 85 for page processing
            progress = 70 + (idx / len(needed_pages)) * 15
            await status_queue.put({"event_type": "knowledge_page_processing", "message": f"Processing page {page_key}", "page_index": idx + 1, "total_pages": len(needed_pages), "page_key": page_key, "progress": int(progress)})
        # Try to find the image in a subdirectory or in the main directory
        page_dir = None
        full_page_path = None
        for d in os.listdir(pdf_dir):
            subdir_path = os.path.join(pdf_dir, d)
            if os.path.isdir(subdir_path) and d.startswith('page'):
                if page_key in os.listdir(subdir_path):
                    page_dir = subdir_path
                    full_page_path = os.path.join(page_dir, page_key)
                    break
        if full_page_path is None:
            if page_key in os.listdir(pdf_dir):
                page_dir = pdf_dir
                full_page_path = os.path.join(pdf_dir, page_key)
        if full_page_path is None or not os.path.exists(full_page_path):
            if status_queue:
                await status_queue.put({"event_type": "knowledge_error", "message": f"Full page image not found: {page_key}", "page_key": page_key, "progress": int(progress)})
            print(f"Full page image not found: {page_key}")
            continue
        image_name_wo_ext = os.path.splitext(page_key)[0]
        split_output_dir = os.path.join(page_dir, image_name_wo_ext)
        os.makedirs(split_output_dir, exist_ok=True)
        split_n = split_image_nine_parts_for_exp_meta(full_page_path, split_output_dir)
        if status_queue:
            await status_queue.put({"event_type": "knowledge_page_split", "message": f"Split ({split_n}x{split_n}) completed for {full_page_path}", "page_key": page_key, "split_n": split_n, "progress": int(progress + 2)})
        pair_json_files = await extract_horizontal_pairs_knowledge(page_key, split_n, split_output_dir, metadata, all_knowledge, status_queue, base_progress=int(progress + 2))
        # Aggregate all keys from all pairs for this page
        aggregated_page_knowledge = {
            "visual_elements": {
                "objects": [],
                "relationships": [],
                "patterns": []
            },
            "text_content": {
                "extracted_text": [],
                "key_phrases": []
            }
        }
        for pair_info in pair_json_files:
            pair_file = os.path.join(split_output_dir, pair_info["file"])
            if os.path.exists(pair_file):
                try:
                    with open(pair_file, 'r', encoding='utf-8') as f:
                        knowledge_data = json.load(f)
                    # Merge visual_elements
                    if 'visual_elements' in knowledge_data:
                        for key in ['objects', 'relationships', 'patterns']:
                            if key in knowledge_data['visual_elements']:
                                if isinstance(knowledge_data['visual_elements'][key], list):
                                    aggregated_page_knowledge['visual_elements'][key].extend(knowledge_data['visual_elements'][key])
                    # Merge text_content
                    if 'text_content' in knowledge_data:
                        for key in ['extracted_text', 'key_phrases']:
                            if key in knowledge_data['text_content']:
                                if isinstance(knowledge_data['text_content'][key], list):
                                    aggregated_page_knowledge['text_content'][key].extend(knowledge_data['text_content'][key])
                except Exception as e:
                    if status_queue:
                        await status_queue.put({"event_type": "knowledge_error", "message": f"Error reading knowledge file {pair_info['file']}: {str(e)}", "page_key": page_key, "error": str(e), "progress": int(progress + 5)})
                    print(f"Error reading knowledge file {pair_info['file']}: {str(e)}")
                    continue
        # Remove duplicates from aggregated knowledge
        for category in ['visual_elements', 'text_content']:
            for key in aggregated_page_knowledge[category]:
                if isinstance(aggregated_page_knowledge[category][key], list):
                    seen = set()
                    unique_items = []
                    for item in aggregated_page_knowledge[category][key]:
                        if isinstance(item, dict):
                            item_str = json.dumps(item, sort_keys=True)
                        else:
                            item_str = str(item)
                        if item_str not in seen:
                            seen.add(item_str)
                            unique_items.append(item)
                    aggregated_page_knowledge[category][key] = unique_items
        all_knowledge[page_key] = aggregated_page_knowledge
        if status_queue:
            await status_queue.put({"event_type": "knowledge_page_aggregated", "message": f"Aggregated knowledge for page {page_key}", "page_key": page_key, "progress": int(progress + 8)})
        # Update root_metadata.json for this page
        if "pages" in metadata and page_key in metadata["pages"]:
            metadata["pages"][page_key].update(aggregated_page_knowledge)
            with open(root_metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            if status_queue:
                await status_queue.put({"event_type": "knowledge_root_metadata_updated", "message": f"Updated root_metadata.json with knowledge for page {page_key}", "page_key": page_key, "progress": int(progress + 10)})
    # Save root_knowledge.json in the parent directory
    root_knowledge_file = os.path.join(pdf_dir, "root_knowledge.json")
    with open(root_knowledge_file, 'w', encoding='utf-8') as f:
        json.dump(all_knowledge, f, indent=2, ensure_ascii=False)
    if status_queue:
        await status_queue.put({"event_type": "knowledge_extraction_complete", "message": f"Saved root_knowledge.json with knowledge for {len(all_knowledge)} pages", "total_pages": len(all_knowledge), "progress": 95})
    # After saving knowledge, generate the Excel report
    kb = KnowledgeBuilder(model="claude-sonnet-4-20250514")
    await kb.analyze_building_permit_with_excel_export(directory=pdf_dir, status_queue=status_queue)


def split_image_nine_parts_for_exp_meta(image_path, output_dir):
    """
    Split a single image into 3x3 or 2x2 sections and save them in output_dir.
    Also create horizontal and vertical joint images as in app.py.
    If the image is less than or equal to A4 size (4300x3600 at 300dpi), do a 2x2 split.
    Returns the split_n used (2 or 3).
    """
    os.makedirs(output_dir, exist_ok=True)
    img = Image.open(image_path)
    img_width, img_height = img.size
    # A4 size at 300dpi: 3600 x 4300 pixels (portrait, user override)
    A4_WIDTH = 3600
    A4_HEIGHT = 4300
    if img_width <= A4_WIDTH and img_height <= A4_HEIGHT:
        split_n = 2
    else:
        split_n = 3
    section_width = img_width // split_n
    section_height = img_height // split_n
    sections = [[None for _ in range(split_n)] for _ in range(split_n)]
    for row in range(split_n):
        for col in range(split_n):
            left = col * section_width
            top = row * section_height
            right = left + section_width if col < split_n - 1 else img_width
            bottom = top + section_height if row < split_n - 1 else img_height
            section = img.crop((left, top, right, bottom))
            sections[row][col] = section
            output_path = os.path.join(output_dir, f"{row + 1}_{col + 1}.png")
            section.save(output_path, "PNG", optimize=False)
    # Create joint images (horizontal and vertical)
    for row in range(split_n):
        for col in range(split_n - 1):
            section1 = sections[row][col]
            section2 = sections[row][col + 1]
            width = section1.width
            height = section1.height
            joint_image = Image.new('RGB', (width, height))
            overlap_width = width // 2
            joint_image.paste(section1.crop((width - overlap_width, 0, width, height)), (0, 0))
            joint_image.paste(section2.crop((0, 0, overlap_width, height)), (width - overlap_width, 0))
            joint_name = f"joint_h_{row + 1}_{col + 1}_{row + 1}_{col + 2}.png"
            joint_path = os.path.join(output_dir, joint_name)
            joint_image.save(joint_path, "PNG", optimize=False)
    for col in range(split_n):
        for row in range(split_n - 1):
            section1 = sections[row][col]
            section2 = sections[row + 1][col]
            width = section1.width
            height = section1.height
            joint_image = Image.new('RGB', (width, height))
            overlap_height = height // 2
            joint_image.paste(section1.crop((0, height - overlap_height, width, height)), (0, 0))
            joint_image.paste(section2.crop((0, 0, width, overlap_height)), (0, height - overlap_height))
            joint_name = f"joint_v_{row + 1}_{col + 1}_{row + 2}_{col + 1}.png"
            joint_path = os.path.join(output_dir, joint_name)
            joint_image.save(joint_path, "PNG", optimize=False)
    return split_n


def natural_key(string_):
    """Sort helper for human order: page2.jpg < page10.jpg"""
    return [int(s) if s.isdigit() else s.lower() for s in re.split(r'(\d+)', string_)]

# Add this at the end of exp_meta.py


async def process_directory_async(base_dir: str, status_queue=None):
    print("Processing directory asynchronously", base_dir)
    await process_directory(base_dir, status_queue)


async def process_directory(base_dir: str, status_queue=None):
    import glob
    me = MetadataExtractor(model="gpt-4o")
    try:
        image_patterns = [
            os.path.join(base_dir, "*.jpg"),
            os.path.join(base_dir, "*.jpeg"),
            os.path.join(base_dir, "*.png"),
            os.path.join(base_dir, "*.gif")
        ]
        image_files = []
        seen = set()
        for pattern in image_patterns:
            files = glob.glob(pattern)
            for f in files:
                fname = os.path.basename(f)
                if fname not in seen:
                    image_files.append(f)
                    seen.add(fname)
        image_files = sorted(image_files, key=lambda x: natural_key(os.path.basename(x)))
        if not image_files:
            if status_queue:
                await status_queue.put({"event_type": "metadata_no_images", "message": f"No images found in directory: {base_dir}", "progress": 50})
            return
        if status_queue:
            await status_queue.put({"event_type": "metadata_loading", "message": "Metadata extraction started", "total_images": len(image_files), "progress": 55})
        for i, image_file in enumerate(image_files):
            if status_queue:
                # Progress from 55 to 65 for metadata processing
                progress = 55 + (i / len(image_files)) * 10
                await status_queue.put({"event_type": "metadata_progress", "message": f"Processing {os.path.basename(image_file)} ({i + 1}/{len(image_files)})", "current": i + 1, "total": len(image_files), "progress": int(progress)})
            # Simulate per-image processing (replace with actual logic)
            await asyncio.sleep(0.1)
        # Call the real processing logic
        await me.process_pages_sequential(image_files, status_queue)
        me.save_root_metadata(base_dir, me.metadata)
        if status_queue:
            await status_queue.put({"event_type": "metadata_saved", "message": "Root metadata saved", "progress": 65})
        # Call process_3x3_sections_for_needed_plans after saving root_metadata.json
        await process_3x3_sections_for_needed_plans(base_dir, knowledge_model="gpt-4o", status_queue=status_queue)
        if status_queue:
            await status_queue.put({"event_type": "metadata_complete", "message": "Metadata extraction complete", "metadata": me.get_metadata(), "progress": 100})
    except Exception as e:
        if status_queue:
            await status_queue.put({"event_type": "metadata_error", "message": str(e), "progress": 0})
