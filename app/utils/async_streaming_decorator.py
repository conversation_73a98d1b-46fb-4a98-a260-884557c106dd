import json
from fastapi.responses import StreamingResponse
from functools import wraps


def async_stream_generator(function):
    async def async_streamer(generator):
        async for chunk in generator:
            # Yield the response in SSE format
            yield f"data: {json.dumps(chunk)}\n\n"

    @wraps(function)
    async def async_stream_wrapper(*args, **kwargs):
        # Ensure that we are dealing with an async generator
        generator = await function(*args, **kwargs)
        # Wrap the generator in StreamingResponse (awaitable)
        return StreamingResponse(
            async_streamer(generator),
            media_type="text/event-stream",
        )

    return async_stream_wrapper
