from app.crud.mongo.base_repository import BaseRepository as MongoRepo
from app.database.database_storage import get_mongo_db
from app.models.coa import COA
from bson.objectid import ObjectId
from datetime import datetime
from fastapi.responses import JSONResponse

_MONGO_REPO_COA = MongoRepo("coas")  # Repository for COAs


def create_coa(data: dict):
    """
    Create a new COA document.
    """
    coa = COA(**data)
    coa.created_at = datetime.utcnow()
    coa.updated_at = datetime.utcnow()

    result = _MONGO_REPO_COA.create(coa.model_dump(), get_mongo_db())
    return result.inserted_id, coa.model_dump()


def create_coa_from_message(documents_list: list, application_id: str):
    """
    Create a new COA document from inspector response.
    """
    if not len(documents_list):
        return None

    insert_ids = []
    for record in documents_list:
        for condition in record["conditions"]:
            s3_url = condition["meta_data"]["file_name"]
            file_name = s3_url.split("/")[-1]

            data = {
                "title": record["title"],
                "summary": condition["text"],
                "file_name": file_name,
                "s3_url": s3_url,
                "coordinates": condition["meta_data"]["coordinates"].values(),
                "page_numbers": condition["meta_data"]["page_no"],
                "application_id": application_id,
            }

            coa = COA(**data)
            coa.created_at = datetime.utcnow()
            coa.updated_at = datetime.utcnow()

            result = _MONGO_REPO_COA.create(coa.model_dump(), get_mongo_db())
            insert_ids.append(result.inserted_id)
    return insert_ids


def object_id_to_str(record):
    """
    Convert ObjectId to string for easier handling in JSON responses.
    """
    record["id"] = str(record["_id"])
    del record["_id"]
    return record


def get_all_coas(application_id: str):
    """
    Fetch all COAs.
    """
    query = {"application_id": application_id}
    coas = _MONGO_REPO_COA.get_all_by_projection(query, {}, get_mongo_db())
    return list(map(object_id_to_str, coas))


def update_coa(coa_id: str, updated_fields: dict):
    """
    Update multiple fields of a COA document.
    """
    if "due_date" in updated_fields:
        due_date = datetime.fromisoformat(updated_fields["due_date"])
        due_date = due_date.replace(minute=0, hour=0, second=0, microsecond=0)
        updated_fields["due_date"] = due_date

    updated_fields["updated_at"] = datetime.utcnow()  # Set updated_at to current time
    result = _MONGO_REPO_COA.update_one_by_query_upsert(
        {"_id": ObjectId(coa_id)},
        updated_fields,
        get_mongo_db(),
    )
    if result.acknowledged:
        return JSONResponse(status_code=200, content={"message": "Successfully updated"})
    else :
        return JSONResponse(status_code=500, content={"message": "An error occurred with updating field"})


def delete_coa(coa_id: str):
    """
    Delete a COA document.
    """
    result = _MONGO_REPO_COA.delete_many({"_id": ObjectId(coa_id)}, get_mongo_db())
    return result.deleted_count
