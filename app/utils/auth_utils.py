import jwt
from functools import lru_cache
import time
import boto3
import requests
import json
import base64
from fastapi import HTTPException, status, Request

from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.asymmetric import rsa
from app.core.config import Settings

settings = Settings()
region = "us-east-1"  # Replace with your actual AWS region
userpool_id = settings.AWS_USER_POOL_ID  # Replace with your Cognito user pool ID
app_client_id = settings.AWS_COGNITO_APP_CLIENT_ID  # Replace with your app client ID

cognito_idp = boto3.client(
    "cognito-idp",
    region_name=settings.AWS_REGION,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
)


@lru_cache(maxsize=1)
def get_jwks():
    keys_url = f"https://cognito-idp.{region}.amazonaws.com/{userpool_id}/.well-known/jwks.json"
    response = requests.get(keys_url)
    return json.loads(response.text)["keys"]


def get_current_user(request: Request):
    try:
        user = request.state.user
        if not user:
            return None
        return user
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated"
        )


def check_token_and_get_user_id(token, secret_key=None):
    try:
        if token.startswith("Bearer "):
            token = token[7:]  # Strip the "Bearer " prefix if present
        decoded = jwt.decode(token, options={"verify_signature": False})

        if time.time() > decoded["exp"]:
            return "Token has expired"

        user_id = decoded["cognito:username"]  # Assuming 'sub' is the user ID
        return user_id

    except jwt.ExpiredSignatureError:
        return "Token has expired"
    except jwt.InvalidTokenError as e:
        return f"Invalid Token: {str(e)}"
    except Exception as e:
        return f"Error: {str(e)}"


def verify_token_and_get_user_details(token: str) -> dict:
    try:
        if token.startswith("Bearer "):
            token = token[7:]
        keys = get_jwks()
        kid = jwt.get_unverified_header(token)["kid"]
        key = next(k for k in keys if k["kid"] == kid)

        # Construct the public key from the modulus and exponent in the JWKS
        public_numbers = rsa.RSAPublicNumbers(
            n=int.from_bytes(
                base64.urlsafe_b64decode(key["n"] + "=="), byteorder="big"
            ),
            e=int.from_bytes(
                base64.urlsafe_b64decode(key["e"] + "=="), byteorder="big"
            ),
        )

        public_key = public_numbers.public_key(backend=default_backend())
        public_key_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )

        # Verify and decode the token using the constructed PEM public key
        claims = jwt.decode(
            token, public_key_pem, algorithms=["RS256"], audience=app_client_id, leeway=10
        )
        return claims

    except (KeyError, IndexError, requests.exceptions.RequestException) as e:
        if isinstance(e, jwt.PyJWTError):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while processing the token",
            )
