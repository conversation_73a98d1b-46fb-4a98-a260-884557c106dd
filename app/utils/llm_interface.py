import os
import logging
from pathlib import Path
import json
from langchain_openai import ChatOpenAI
from app.tools.chat_tools import chat_tools, tool_default_args
from app.utils.base_utils import (
    milvus_langchain_search,
    get_latest_n_messages_from_chat,
)
from langchain_core.prompts import (
    ChatPromptTemplate,
    MessagesPlaceholder,
)
from langchain_core.runnables import RunnablePassthrough
from langchain_core.runnables import Runnable
from app.utils.base_utils import create_new_message


class LLMInterface:
    def __init__(self, api_key, model_name, instance_name, token_limit=None):
        self.api_key: str = api_key
        self.instance_name = instance_name
        logs_dir = Path(__file__).parent / "../logs"
        os.makedirs(logs_dir, exist_ok=True)
        log_file = f"llm_logger_{instance_name}"
        self.llm_logger = self.__setup_logger(
            log_file,
            os.path.join(logs_dir, f"{log_file}.log"),
        )
        self.token_limit = token_limit
        self.prompt_tokens = 0
        self.completion_tokens = 0
        self.total_tokens = 0
        self.total_cost = 0.0
        self.model_name = model_name
        self.client = self.__get_chat_model(self.model_name)

    def __get_chat_model(self, model_name: str) -> ChatOpenAI:
        llm = ChatOpenAI(
            model=model_name,
            temperature=0,
            max_tokens=self.token_limit,
            timeout=None,
            max_retries=2,
            api_key=self.api_key,
            streaming=True,
        )
        return llm

    def __setup_logger(self, name, log_file, level=logging.INFO):
        """Function to set up a logger for logging to a file."""
        logger = logging.getLogger(name)

        if not logger.handlers:
            handler = logging.FileHandler(log_file)
            formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        logger.setLevel(level)
        logger.propagate = False
        return logger

    def __search(self, query: str):
        resp = milvus_langchain_search(query)
        return resp

    def __get_callback_to_save_chat(self, chat_id, query):
        return lambda response: create_new_message(chat_id, response, query, [], [])

    def __get_prompt(self, prompt_path):
        path = Path(__file__).parent / f"../prompts/{prompt_path}"
        base_prompt = open(path, mode="r").read()

        return ChatPromptTemplate.from_messages(
            [
                ("system", base_prompt),
                (MessagesPlaceholder(variable_name="history")),
                ("human", "{input}"),
            ],
        )

    def __format_history(self, history):
        formatted_history = []
        for each_his in history:
            formatted_history.append(("human", each_his["content"]["human"]["message"]))
            formatted_history.append(("ai", each_his["content"]["ai"]["message"]))
        return formatted_history

    def __get_chain_with_prompt(
        self,
        prompt_path: str,
        history: str,
        llm: ChatOpenAI,
        prefix_chain: Runnable = None,
        suffix_chain: Runnable = None,
    ) -> Runnable:
        system_prompt = self.__get_prompt(prompt_path)
        chain = (
            RunnablePassthrough.assign(history=lambda x: history)
            | system_prompt
            | llm
        )
        # self.llm_logger.info("prompt: ", system_prompt)
        if isinstance(prefix_chain, Runnable):
            chain = prefix_chain | chain

        if isinstance(suffix_chain, Runnable):
            chain = chain | suffix_chain
        return chain

    def __handle_function_calling(
        self, tools_picked_by_llm, history, llm, chat_id, user, callback_to_save_chat,
    ):
        for each_tool_call in tools_picked_by_llm:
            available_tools = {tool.name: tool for tool in chat_tools}
            tool_in_current_iteration = available_tools[each_tool_call["name"].lower()]
            if (
                each_tool_call["name"].lower()
                == "get_relevant_data_for_mpuid_from_brain"
            ):
                self.llm_logger.info("Function Called ==> get_relevant_data_for_mpuid_from_brain")
                self.llm_logger.info(f"LLM arguments {json.dumps(each_tool_call['args'])}")
                self.llm_logger.info("Tool prompt:")
                complete__args = {
                    **each_tool_call["args"],
                    **{
                        "base_chain": self.__get_chain_with_prompt(
                            tool_default_args[each_tool_call["name"].lower()]["prompt"],
                            history,
                            llm,
                            prefix_chain=RunnablePassthrough.assign(
                                context=lambda x: self.__search(x["input"]),
                            ),
                        ),
                    },
                }
            elif each_tool_call["name"].lower() == "add_data_to_mpud":
                self.llm_logger.info("Function Called ==> add_data_to_mpud")
                self.llm_logger.info(f"LLM arguments {each_tool_call['args']}")
                complete__args = {
                    **each_tool_call["args"],
                    **{"chat_id": chat_id, "user": user},
                }
            elif each_tool_call["name"].lower() == "delete_data_from_mpud":
                self.llm_logger.info("Function Called ==> delete_data_from_mpud")
                self.llm_logger.info(f"LLM arguments {each_tool_call['args']}")
                self.llm_logger.info("Tool prompt:")
                complete__args = {
                    **each_tool_call["args"],
                    **{"chat_id": chat_id, "user": user},
                    **{
                        "base_chain": self.__get_chain_with_prompt(
                            tool_default_args[each_tool_call["name"].lower()]["prompt"],
                            history,
                            llm,
                            prefix_chain=RunnablePassthrough.assign(
                                context=lambda x: self.__search(x["input"]),
                            ),
                        ),
                    },
                }
            tool_stream = tool_in_current_iteration.invoke(complete__args)
            return (tool_stream, chat_id, callback_to_save_chat)

    def chat_interaction_wrapper(self, query, user, chat_id):
        self.llm_logger.info("+++++++++++++++++ BEGIN LLM SESSION ++++++++")

        self.llm_logger.info(f"""
                             Model: {self.model_name}
                             Input: {query}
                             User name: {user['cognito:username']}
                             Chat Id: {chat_id}
                             """)
        self.llm_logger.info(
            f"""Available Tools: {json.dumps(
                {
                    tool.name: {
                        'Description': tool.description, 'Arguments': tool.args,
                    } for tool in chat_tools
                }, indent=2)}""",
        )
        if self.model_name not in ["gpt-4o-mini", "gpt-4o"]:

            def error_generator():
                msg = "Please use gpt-4o-mini or gpt-4o model"
                yield f"data: {json.dumps({'content': msg})}\n\n"

            return error_generator()
        llm_with_tools = self.client.bind_tools(chat_tools)
        history, chat_id, chat_title = get_latest_n_messages_from_chat(
            chat_id, user["cognito:username"],
        )
        history = self.__format_history(history)

        base_chain = RunnablePassthrough.assign(
            context=lambda x: self.__search(x["input"]),
        )

        chain = self.__get_chain_with_prompt(
            "chat/llm_processing.prompt",
            history,
            llm_with_tools,
            prefix_chain=base_chain,
        )

        tools_picked_by_llm = chain.invoke({"input": query}).tool_calls
        callback_to_save_chat = self.__get_callback_to_save_chat(
            chat_id=chat_id, query=query,
        )

        if not tools_picked_by_llm:
            streams = (
                chain.stream({"input": query}), chat_id, callback_to_save_chat,
            )
            return streams
        else:
            return self.__handle_function_calling(
                tools_picked_by_llm,
                history,
                self.client,
                chat_id,
                user,
                callback_to_save_chat,
            )
