import asyncio
import base64
import json
import logging
import os
import re
from datetime import datetime
from typing import Dict, List, Optional

import cv2
import litellm
from dotenv import load_dotenv
from pydantic import BaseModel

# Load environment variables
load_dotenv()

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Configure logger
logger = logging.getLogger('knowledge_builder')
logger.setLevel(logging.INFO)

# Remove any existing handlers to prevent duplicates
if logger.hasHandlers():
    logger.handlers.clear()

# Console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)

# File handler with daily files
current_date = datetime.now().strftime('%Y-%m-%d')
log_file = os.path.join('logs', f'knowledge_builder_{current_date}.log')
file_handler = logging.FileHandler(
    log_file,
    encoding='utf-8'
)
file_handler.setLevel(logging.INFO)
file_formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - [%(name)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
file_handler.setFormatter(file_formatter)

# Add both handlers to logger
logger.addHandler(console_handler)
logger.addHandler(file_handler)

# Define supported models with their configurations
SUPPORTED_MODELS = {
    "claude-sonnet-4-20250514": {
        "provider": "anthropic",
        "max_tokens": 200000,
        "default_output_tokens": 15000
    },
    "claude-3-7-sonnet-latest": {
        "provider": "anthropic",
        "max_tokens": 200000,
        "default_output_tokens": 15000
    },
    "claude-3-5-sonnet-20240620": {
        "provider": "anthropic",
        "max_tokens": 200000,
        "default_output_tokens": 8192
    },
    "gpt-4.1": {
        "provider": "openai",
        "max_tokens": 128000,
        "default_output_tokens": 15000
    },
    "gpt-4-turbo": {
        "provider": "openai",
        "max_tokens": 4096,
        "max_input_tokens": 128000,
        "max_output_tokens": 4096,
        "default_output_tokens": 4096,
        "supports_function_calling": True,
        "supports_parallel_function_calling": True,
        "supports_vision": True,
        "supports_prompt_caching": True,
        "supports_system_messages": True,
        "supports_tool_choice": True,
        "supports_pdf_input": True
    },
    "openai/gpt-4-vision-preview": {
        "provider": "openai",
        "max_tokens": 128000,
        "default_output_tokens": 15000
    },
    "gpt-3.5-turbo": {
        "provider": "openai",
        "max_tokens": 4097,
        "max_input_tokens": 16385,
        "max_output_tokens": 4096,
        "default_output_tokens": 4096,
        "input_cost_per_token": 0.0000015,
        "output_cost_per_token": 0.000002,
        "supports_function_calling": True,
        "supports_prompt_caching": True,
        "supports_system_messages": True,
        "supports_tool_choice": True
    },
    "gpt-4o": {
        "provider": "openai",
        "max_tokens": 16384,
        "max_input_tokens": 128000,
        "max_output_tokens": 16384,
        "default_output_tokens": 16384,
        "supports_function_calling": True,
        "supports_parallel_function_calling": True,
        "supports_response_schema": True,
        "supports_vision": True,
        "supports_prompt_caching": True,
        "supports_system_messages": True,
        "supports_tool_choice": True,
        "supports_web_search": True,
        "supports_pdf_input": True
    },
    "gpt-4o-mini": {
        "provider": "openai",
        "max_tokens": 16384,
        "max_input_tokens": 128000,
        "max_output_tokens": 16384,
        "default_output_tokens": 16384,
        "input_cost_per_token": 0.00000015,
        "output_cost_per_token": 0.00000060,
        "supports_function_calling": True,
        "supports_parallel_function_calling": True,
        "supports_response_schema": True,
        "supports_vision": True,
        "supports_prompt_caching": True,
        "supports_system_messages": True,
        "supports_tool_choice": True,
        "supports_web_search": True,
        "supports_pdf_input": True
    }
}

# Load API keys
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")

if not ANTHROPIC_API_KEY:
    logger.error("Anthropic API key not found in environment variables.")
    raise ValueError("Anthropic API key not found in environment variables")

if not OPENAI_API_KEY:
    logger.error("OpenAI API key not found in environment variables.")
    raise ValueError("OpenAI API key not found in environment variables")

# Configure litellm
litellm.api_keys = {
    "anthropic": ANTHROPIC_API_KEY,
    "openai": OPENAI_API_KEY
}

# Set default completion model configs
litellm.model_configs = {
    "claude-sonnet-4-20250514": {
        "api_key": ANTHROPIC_API_KEY,
        "api_base": "https://api.anthropic.com/v1"
    },
    "claude-3-5-sonnet-20240620": {
        "api_key": ANTHROPIC_API_KEY,
        "api_base": "https://api.anthropic.com/v1"
    },
    "gpt-4.1": {
        "api_key": OPENAI_API_KEY,
        "api_base": "https://api.openai.com/v1"
    },
    "openai/gpt-4-vision-preview": {
        "api_key": OPENAI_API_KEY,
        "api_base": "https://api.openai.com/v1"
    },
    "gpt-3.5-turbo": {
        "api_key": OPENAI_API_KEY,
        "api_base": "https://api.openai.com/v1"
    },
    "gpt-4o-mini": {
        "api_key": OPENAI_API_KEY,
        "api_base": "https://api.openai.com/v1"
    },
    "gpt-4-turbo": {
        "api_key": OPENAI_API_KEY,
        "api_base": "https://api.openai.com/v1"
    }
}


class KnowledgeResponse(BaseModel):
    request_id: str
    model_used: str
    knowledge_base: Dict
    status: str
    processing_time: float
    token_usage: Optional[dict] = None


def reduce_image_for_api(image, max_size_bytes=5 * 1024 * 1024):
    """Reduce image size until its base64 representation is under max_size_bytes"""
    quality = 95
    scale = 1.0

    while True:
        # Apply current scale if needed
        if scale < 1.0:
            new_width = int(image.shape[1] * scale)
            new_height = int(image.shape[0] * scale)
            current_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
        else:
            current_image = image

        # Encode with current quality
        _, buffer = cv2.imencode('.jpg', current_image, [cv2.IMWRITE_JPEG_QUALITY, quality])
        # Calculate the actual base64 string size that will be sent to the API
        base64_str = f"data:image/jpeg;base64,{base64.b64encode(buffer).decode('utf-8')}"
        base64_size = len(base64_str.encode('utf-8'))

        # Check if size is acceptable (leave some margin for safety)
        if base64_size <= max_size_bytes - 100:  # Leave 100 bytes margin
            return base64_str, current_image

        # Adjust parameters
        if quality > 30:
            # First try reducing quality
            quality -= 5
        else:
            # If quality is already low, reduce size
            scale *= 0.8  # More aggressive reduction (20% each time)

        # Prevent infinite loop
        if scale < 0.1:
            raise ValueError("Could not reduce image to required size while maintaining usable quality")


class KnowledgeBuilder:
    def __init__(self, model: str = "claude-sonnet-4-20250514"):
        self.model = model
        self.knowledge_base = {}
        self.request_id = datetime.now().strftime('%Y%m%d_%H%M%S_%f')
        self.cost_tracking = {
            "total_cost": 0.0,
            "per_image_costs": {},
            "token_usage": {
                "total_prompt_tokens": 0,
                "total_completion_tokens": 0,
                "total_tokens": 0
            }
        }

        if model not in SUPPORTED_MODELS:
            raise ValueError(f"Unsupported model. Please use one of: {', '.join(SUPPORTED_MODELS.keys())}")

        self.model_config = SUPPORTED_MODELS[model]
        logger.info(f"Initialized KnowledgeBuilder with model: {model}")

    # Define the building permit analysis prompt as a class attribute
    BUILDING_PERMIT_ANALYSIS_PROMPT = """
    Analyze the submitted building permit application documents and check for all required elements according to the 2023 Florida Building Code, Building, Eighth Edition.

    I have provided you with a knowledge base extracted from the PDF containing key information and metadata from different pages.

    The knowledge base contains the following extracted information:
    {knowledge_base}

    Please use this knowledge base to perform a comprehensive compliance check and provide a structured analysis that includes:

    1. **Project Summary Information**
    2. **Submittal Type Identification**
    3. **Basic Document Information**
    4. **Dimensional Requirements**
    5. **Code Compliance**
    6. **Document Completeness**

    IMPORTANT: For each compliance item, specify:
    - The exact value found (or describe what's missing)
    - Pass/Fail status using "✓ Pass" or "✗ Fail" format
    - Whether the item is "Yes" (required), "Optional", or not applicable
    - Which specific page(s) the information was found on (e.g., "page1.jpg", "page2.jpg") or "NOT FOUND"
    - please provide the value from the knowledge base that you used to make the determination on the code compliance section.
    - Document type may vary from plan to plan based on the Accessory Structure type, so please be sure to check the document type and provide the correct one in the document completeness section.
    Please provide your analysis in the following JSON format:

    {{
        "project_summary": {{
            "accessory_structure": "",
            "project_action": "",
            "required_documents": "",
            "total_pages_analyzed": 0,
            "pages_with_critical_info": "",
            "overall_assessment": "APPROVED/REJECTED",
            "overall_confidence": 0
        }},
        "submittal_type_identification": {{
            "submittal_type": {{
                "field": "Submittal Type",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "fbc_code_edition": {{
                "field": "FBC Code Edition",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }}
        }},
        "basic_document_information": {{
            "square_footage": {{
                "field": "Square Footage",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "property_address": {{
                "field": "Property Address",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "building_elevations": {{
                "field": "Building Elevations",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "product_approval_list": {{
                "field": "Product Approval List",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "engineer_stamp": {{
                "field": "Engineer Stamp",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "engineer_signature": {{
                "field": "Engineer Signature",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "construction_type": {{
                "field": "Construction Type",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "scope_of_work": {{
                "field": "Scope of Work",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "alteration_level": {{
                "field": "Alteration Level",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Optional",
                "found_on": ""
            }}
        }},
        "dimensional_requirements": {{
            "requirements_met": {{
                "field": "Requirements Met",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "measurements_labeled": {{
                "field": "Measurements Labeled",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "scaling_consistent": {{
                "field": "Scaling Consistent",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }}
        }},
        "code_compliance": {{
            "sections_referenced": {{
                "field": "Sections Referenced",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }},
            "correct_edition": {{
                "field": "Correct Edition",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }}
        }},
        "document_completeness": {{
            "For example, site_plan": {{
                "field": "Site Plan/Plot Plan",
                "value": "",
                "status": "✓ Pass/✗ Fail",
                "required": "Yes",
                "found_on": ""
            }}
        }},
        "deficiencies": [],
        "recommendations": []
    }}
    """

    def _calculate_cost(self, token_usage: dict) -> float:
        """
        Calculate the cost based on token usage and model.
        All rates are per 1K tokens unless specified otherwise.
        """
        # Cost rates per 1K tokens
        COST_RATES = {
            "claude-sonnet-4-20250514": {
                "input": 0.003,  # $0.003 per 1K input tokens (3e-6 per token)
                "output": 0.015,  # $0.015 per 1K output tokens (15e-6 per token)
                "image": 0.0,
                "search_context": {
                    "low": 0.01,    # $0.01 per low context search
                    "medium": 0.01,  # $0.01 per medium context search
                    "high": 0.01    # $0.01 per high context search
                },
                "cache": {
                    "creation": 0.00375,  # $0.00375 per 1K tokens for cache creation
                    "read": 0.0003        # $0.0003 per 1K tokens for cache read
                }
            },
            "claude-3-7-sonnet-latest": {
                "input": 0.015,
                "output": 0.075,
                "image": 0.0
            },
            "claude-3-5-sonnet-20240620": {
                "input": 0.015,
                "output": 0.075,
                "image": 0.0
            },
            "gpt-4.1": {
                "input": 0.002,  # $0.002 per 1K input tokens
                "output": 0.008,  # $0.008 per 1K output tokens
                "image": 0.0,
                "search_context": {
                    "low": 0.03,    # $0.03 per low context search
                    "medium": 0.035,  # $0.035 per medium context search
                    "high": 0.05    # $0.05 per high context search
                }
            },
            "gpt-4-turbo": {
                "input": 0.01,   # $0.01 per 1K input tokens (0.00001 per token)
                "output": 0.03,  # $0.03 per 1K output tokens (0.00003 per token)
                "image": 0.0
            },
            "gpt-4o": {
                "input": 0.005,  # $5.00 per 1M input tokens (0.000005 per token)
                "output": 0.02,  # $20.00 per 1M output tokens (0.00002 per token)
                "image": 0.0,
                "search_context": {
                    "low": 0.03,    # $0.03 per low context search
                    "medium": 0.035,  # $0.035 per medium context search
                    "high": 0.05    # $0.05 per high context search
                },
                "cache": {
                    "read": 0.0025  # $2.50 per 1M cached input tokens (0.0000025 per token)
                },
                "batch": {
                    "input": 0.00125,  # $0.00125 per 1K input tokens (0.00000125 per token)
                    "output": 0.005    # $0.005 per 1K output tokens (0.00000500 per token)
                }
            },
            "openai/gpt-4-vision-preview": {
                "input": 0.03,
                "output": 0.06,
                "image": 0.00765  # $0.00765 per image
            },
            "gpt-3.5-turbo": {
                "input": 0.0015,  # Converted to per 1K tokens
                "output": 0.002,  # Converted to per 1K tokens
                "image": 0.0
            },
            "gpt-4o-mini": {
                "input": 0.00015,  # Converted to per 1K tokens
                "output": 0.00060,  # Converted to per 1K tokens
                "image": 0.0
            }
        }

        if self.model not in COST_RATES:
            logger.warning(f"No cost rate defined for model {self.model}, using conservative default rates")
            rates = {"input": 0.0015, "output": 0.002, "image": 0.0}  # Conservative default
        else:
            rates = COST_RATES[self.model]

        # Calculate text token costs (per 1K tokens)
        input_cost = (token_usage.get('prompt_tokens', 0) / 1000) * rates['input']
        output_cost = (token_usage.get('completion_tokens', 0) / 1000) * rates['output']

        # Calculate image costs if applicable
        image_cost = 0.0
        if 'image_tokens' in token_usage and rates['image'] > 0:
            image_cost = token_usage['image_tokens'] * rates['image']

        total_cost = input_cost + output_cost + image_cost

        # Log detailed cost breakdown
        logger.debug(f"Cost breakdown for {self.model}:")
        logger.debug(f"  Input tokens: {token_usage.get('prompt_tokens', 0)}")
        logger.debug(f"  Output tokens: {token_usage.get('completion_tokens', 0)}")
        logger.debug(f"  Image tokens: {token_usage.get('image_tokens', 0)}")
        logger.debug(f"  Input cost: ${input_cost:.6f}")
        logger.debug(f"  Output cost: ${output_cost:.6f}")
        logger.debug(f"  Image cost: ${image_cost:.6f}")
        logger.debug(f"  Total cost: ${total_cost:.6f}")

        return total_cost

    def _update_cost_tracking(self, image_path: str, token_usage: dict):
        """
        Update cost tracking information.
        """
        cost = self._calculate_cost(token_usage)

        # Update total cost
        self.cost_tracking["total_cost"] += cost

        # Update per-image cost
        self.cost_tracking["per_image_costs"][image_path] = {
            "cost": cost,
            "token_usage": token_usage,
            "timestamp": datetime.now().isoformat()
        }

        # Update total token usage
        self.cost_tracking["token_usage"]["total_prompt_tokens"] += token_usage.get('prompt_tokens', 0)
        self.cost_tracking["token_usage"]["total_completion_tokens"] += token_usage.get('completion_tokens', 0)
        self.cost_tracking["token_usage"]["total_tokens"] += token_usage.get('total_tokens', 0)

        logger.info(f"Cost for {image_path}: ${cost:.4f}")
        logger.info(f"Total cost so far: ${self.cost_tracking['total_cost']:.4f}")

    def _save_knowledge_base(self, base_dir: str, image_path: str):
        """
        Save the current knowledge base and cost tracking to JSON files.
        Simply appends new knowledge data to the existing JSON array.
        """
        datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save knowledge base - use a single file per directory with array of entries
        kb_file = os.path.join(base_dir, "knowledge_base.json")

        # Create a new knowledge entry with timestamp
        new_entry = {
            "image_path": image_path,
            "knowledge": self.knowledge_base
        }

        # If file exists, append new entry to existing array
        if os.path.exists(kb_file):
            try:
                with open(kb_file, 'r', encoding='utf-8') as f:
                    existing_kb = json.load(f)

                # Ensure existing_kb is a list
                if not isinstance(existing_kb, list):
                    existing_kb = [existing_kb]

                # Simply append the new entry
                existing_kb.append(new_entry)

                # Save the updated array
                with open(kb_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_kb, f, indent=2, ensure_ascii=False)
            except Exception as e:
                logger.error(f"Error appending to knowledge base: {str(e)}")
                # If append fails, create new array with current entry
                with open(kb_file, 'w', encoding='utf-8') as f:
                    json.dump([new_entry], f, indent=2, ensure_ascii=False)
        else:
            # If file doesn't exist, create new array with first entry
            with open(kb_file, 'w', encoding='utf-8') as f:
                json.dump([new_entry], f, indent=2, ensure_ascii=False)

        # Format cost tracking data for better readability
        formatted_cost_tracking = {
            "total_cost": f"${self.cost_tracking['total_cost']:.4f}",
            "per_image_costs": {
                image_path: {
                    "cost": f"${cost_data['cost']:.4f}",
                    "token_usage": cost_data['token_usage'],
                    "timestamp": cost_data['timestamp']
                }
                for image_path, cost_data in self.cost_tracking['per_image_costs'].items()
            },
            "token_usage": self.cost_tracking['token_usage']
        }

        # Save cost tracking
        cost_file = os.path.join(base_dir, "cost_tracking.json")
        with open(cost_file, 'w', encoding='utf-8') as f:
            json.dump(formatted_cost_tracking, f, indent=2, ensure_ascii=False)

        logger.info(f"Knowledge base saved to: {kb_file}")
        logger.info(f"Cost tracking saved to: {cost_file}")

    def ensure_json_response(self, response_content):
        try:
            # Try to parse the entire response as JSON
            return json.loads(response_content)
        except json.JSONDecodeError:
            # If that fails, try to extract JSON from the response
            try:
                # Look for the first '{' and last '}'
                start = response_content.index('{')
                end = response_content.rindex('}') + 1
                json_str = response_content[start:end]
                return json.loads(json_str)
            except (ValueError, json.JSONDecodeError):
                # If all else fails, return an error JSON object
                return {"error": "Failed to parse response as JSON", "original_response": response_content}

    async def process_image(self, image_paths: List[str], accessory_structure: str, plan_type: str, analyst_context: Optional[str] = None, context: Optional[str] = None) -> KnowledgeResponse:
        """
        Process multiple images and update the knowledge base iteratively.

        Args:
            image_paths: List of paths to image files
            context: Optional context or previous knowledge to consider

        Returns:
            KnowledgeResponse object containing the updated knowledge base
        """
        start_time = datetime.now()

        try:
            # Read and process all images
            image_contents = []
            for image_path in image_paths:
                image = cv2.imread(image_path)
                if image is None:
                    raise ValueError(f"Could not read image file: {image_path}")

                # Reduce image size for API if needed
                try:
                    img_base64, _ = reduce_image_for_api(image)
                    image_contents.append({
                        "path": image_path,
                        "base64": img_base64
                    })
                except ValueError as e:
                    raise ValueError(f"Error processing image {image_path}: {str(e)}")
            print("accessory_structure: ", accessory_structure)
            print("plan_type: ", plan_type)
            print("analyst_context: ", analyst_context)
            if analyst_context == "image":
                base_prompt = """
                You are a certified construction expert specializing in visual plan analysis for Florida City building permits and FBC (Florida Building Code) compliance.

                You are analyzing a partial construction plan extracted from a PDF. This page belongs to a {accessory_structure} structure and is part of a {plan_type} plan.

                Your task is to extract and describe visual elements, spatial relationships, and textual information exactly as they appear in the image. The output will contribute to a knowledge base used for automated FBC compliance checks.

                Your analysis must consider the following:

                VISUAL PLAN ANALYSIS GUIDELINES:

                1. VISUAL ELEMENT IDENTIFICATION:
                * Detect and list all architectural or structural components: walls, doors, windows, beams, columns, footings, trusses, dimensions, elevation markers, section cuts, labels, etc.
                * Include graphical notations (e.g., North arrows, scales, gridlines, symbols, legend references).
                * Include structural tags or component callouts (e.g., "T1", "W1", "B2", or "Detail A").

                2. SPATIAL RELATIONSHIPS:
                * Describe how visual elements relate in space (e.g., aligned, adjacent, overlapping, connected, dimensioned, or grouped).
                * Highlight any evident patterns or groupings (e.g., repetitive layout, framing structure, symmetry, modular segments).

                3. TEXT CONTENT CAPTURE:
                * Extract every visible word, symbol, number, and notation **exactly** as they appear visually.
                * Preserve all original formatting, capitalization, punctuation, and spacing — no corrections or interpretations.
                * Include visible annotations, dimensions, footnotes, and table content.
                * Retain original document layout, indentation, and alignment.

                4. DOCUMENT STRUCTURE INTEGRITY:
                * Maintain the reading order from top-left to bottom-right unless otherwise visually indicated.
                * Capture table structures, bullet lists, and aligned data maintaining visual fidelity.
                * Combine multi-image fragments logically, assuming this image may be part of a larger sequence.

                5. ACCURACY & FIDELITY:
                * No summarization or paraphrasing.
                * Do not infer meanings or label elements unless labels are explicitly shown.
                * Preserve all graphical spacing and layout fidelity.
                Previous knowledge (if any):
                {context}
                Use the following structured JSON format for your response:
                  {{
                    "visual_elements": {{
                        "objects": [],
                        "relationships": [],
                        "patterns": []
                    }},
                    "text_content": {{
                        "extracted_text": [],
                        "key_phrases": []
                    }}
                }}

                Include all visible data from the image. Ensure formatting, spacing, and element order matches exactly what appears in the source image.
                Use empty arrays (`[]`) for any section where no content is detected.

                """

            else:
                # Prepare the prompt
                base_prompt = """
                    You are a construction expert in the field of building permits belonging to the state of Florida City.
                    you are extracting information from a split image from construction plans pdf page to build a knowledge base for a construction permit FBC compliance check.
                    This is page belongs to a {accessory_structure} structure.
                    and this is {plan_type} plan.
                    Analyze this image and extract relevant information to build a knowledge base.
                    Consider the following aspects:
                    1. Visual elements and their relationships
                    2. Text content if present
                    CRITICAL REQUIREMENTS (FOR DOCUMENT EXTRACTION):
                    1. EXACT VISUAL REPRODUCTION:
                * Extract every single character, symbol, number, and text element exactly as it visually appears
                * Preserve ALL original spacing, gaps, and character positioning without any interpretation or correction
                * Maintain ALL formatting: uppercase, lowercase, mixed case, bold, italics, underlines
                * Keep ALL punctuation marks, special characters, symbols, and spacing precisely as shown
                * DO NOT alter, interpret, compress, or "fix" any visual elements — reproduce everything verbatim

                2. COMPLETE VISUAL EXTRACTION:
                * Capture ALL visible text including headers, footers, page numbers, watermarks, marginalia
                * Extract ALL numerical values, codes, references, measurements with exact visual spacing
                * Include ALL bullet points, lists, tables, and structural elements maintaining exact formatting
                * Preserve ALL line breaks, paragraph spacing, indentation, and alignment as visually presented

                3. STRUCTURAL PRESERVATION:
                * Maintain original document structure including section numbering, page layout, and organization
                * Preserve cross-references, citations, and internal document connections exactly as shown
                * Keep table structures, cell alignment, and grid layouts precisely as they appear visually

                4. MULTI-IMAGE DOCUMENT RECONSTRUCTION:
                * Combine all image sections into one continuous document following natural reading flow
                * Ensure seamless content connection across image boundaries without duplication or omission
                * Maintain logical document progression while preserving exact visual formatting from each section

                5. QUALITY ASSURANCE:
                * Verify completeness — ensure all visible content from all images is captured
                * Confirm accuracy — all text, numbers, spacing, and formatting match the original exactly
                * Check continuity — document flows naturally while maintaining visual fidelity to source

                Previous knowledge (if any):
                {context}

                Please provide your response in the following JSON structure:
                {{
                    "visual_elements": {{
                        "objects": [],
                        "relationships": [],
                        "patterns": []
                    }},
                    "text_content": {{
                        "extracted_text": [],
                        "key_phrases": []
                    }}
                }}

                Ensure all text content is preserved exactly as it appears in the images, with no modifications or interpretations.
                """

            # Format prompt with context if provided
            prompt = base_prompt.format(context=context if context else "No previous knowledge available.", accessory_structure=accessory_structure, plan_type=plan_type)

            # Set up messages based on model provider
            if self.model_config["provider"] == "anthropic":
                messages = [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        *[{"type": "image_url", "image_url": img["base64"]} for img in image_contents]
                    ]
                }]
            else:  # OpenAI
                messages = [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        *[{"type": "image_url", "image_url": img["base64"]} for img in image_contents]
                    ]
                }, ]

            # Use LLM to analyze the images
            completion = await litellm.acompletion(
                model=self.model,
                messages=messages,
                response_format={"type": "json_object"},
                max_tokens=self.model_config["default_output_tokens"],
                temperature=0.1
            )

            # Extract the response content
            content = completion.choices[0].message.content.strip()
            print(content)

            try:
                knowledge_update = self.ensure_json_response(content)
                logger.info(f"Knowledge update: {knowledge_update}")
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON response: {str(e)}")
                logger.error(f"Raw content: {content}")
                raise ValueError("Failed to parse LLM response into JSON format")

            # Update knowledge base
            self._update_knowledge_base(knowledge_update)
            logger.info("Knowledge base updated successfully", knowledge_update)
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()

            # Get token usage
            token_usage = None
            if hasattr(completion, 'usage'):
                token_usage = {
                    "completion_tokens": getattr(completion.usage, 'completion_tokens', 0),
                    "prompt_tokens": getattr(completion.usage, 'prompt_tokens', 0),
                    "total_tokens": getattr(completion.usage, 'total_tokens', 0)
                }

            # After getting token usage, update cost tracking
            if token_usage:
                self._update_cost_tracking(image_paths[0], token_usage)

            # Save knowledge base after processing all images
            self._save_knowledge_base(os.path.dirname(image_paths[0]), image_paths[0])

            # Create response
            response = KnowledgeResponse(
                request_id=self.request_id,
                model_used=self.model,
                knowledge_base=self.knowledge_base,
                status="success",
                processing_time=processing_time,
                token_usage=token_usage
            )

            logger.info(f"Successfully processed {len(image_paths)} images")

            return response

        except Exception as e:
            logger.error(f"Error processing images: {str(e)}", exc_info=True)
            raise

    async def wait_for_response(self, image_paths: List[str], accessory_structure: str = "unknown", plan_type: str = "unknown", analyst_context: str = "text", max_retries: int = 1, retry_delay: float = 2.0) -> Optional[KnowledgeResponse]:
        """
        Wait for a valid response from image processing with retries.

        Args:
            image_paths: List of paths to image files
            accessory_structure: The type of accessory structure
            plan_type: The type of plan being analyzed
            analyst_context: Context for analysis ("image" or "text")
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds

        Returns:
            KnowledgeResponse if successful, None if all retries failed
        """
        for retry in range(max_retries):
            try:
                context = self.get_knowledge_base()
                response = await self.process_image(image_paths, accessory_structure, plan_type, analyst_context, context)
                print(response)
                if response and response.status == "success":
                    if response.knowledge_base and isinstance(response.knowledge_base, dict):
                        return response
                    else:
                        logger.warning(f"Invalid knowledge base in response, retry {retry + 1}/{max_retries}")
                else:
                    logger.warning(f"Response not complete, retry {retry + 1}/{max_retries}")
            except Exception as e:
                logger.error(f"Error processing images on retry {retry + 1}: {str(e)}")

            if retry < max_retries - 1:
                await asyncio.sleep(retry_delay)

        logger.error(f"Failed to get valid response after {max_retries} retries")
        return None

    def _update_knowledge_base(self, knowledge_update: Dict):
        """
        Update the knowledge base with new information.

        Args:
            knowledge_update: Dictionary containing new knowledge to be integrated
        """
        try:
            # Update visual elements
            if 'visual_elements' in knowledge_update:
                if 'visual_elements' not in self.knowledge_base:
                    self.knowledge_base['visual_elements'] = {}

                for key, value in knowledge_update['visual_elements'].items():
                    if key not in self.knowledge_base['visual_elements']:
                        self.knowledge_base['visual_elements'][key] = []
                    self.knowledge_base['visual_elements'][key] = value

            # Update text content
            if 'text_content' in knowledge_update:
                if 'text_content' not in self.knowledge_base:
                    self.knowledge_base['text_content'] = {}

                for key, value in knowledge_update['text_content'].items():
                    if key not in self.knowledge_base['text_content']:
                        self.knowledge_base['text_content'][key] = []
                    self.knowledge_base['text_content'][key] = value

            # Update contextual information
            if 'contextual_info' in knowledge_update:
                if 'contextual_info' not in self.knowledge_base:
                    self.knowledge_base['contextual_info'] = {}

                self.knowledge_base['contextual_info'].update(knowledge_update['contextual_info'])

            # Update knowledge updates
            if 'knowledge_updates' in knowledge_update:
                if 'knowledge_updates' not in self.knowledge_base:
                    self.knowledge_base['knowledge_updates'] = {
                        'new_facts': [],
                        'modified_facts': [],
                        'confidence_scores': {}
                    }

                updates = knowledge_update['knowledge_updates']
                self.knowledge_base['knowledge_updates']['new_facts'].extend(updates.get('new_facts', []))
                self.knowledge_base['knowledge_updates']['modified_facts'].extend(updates.get('modified_facts', []))
                self.knowledge_base['knowledge_updates']['confidence_scores'].update(updates.get('confidence_scores', {}))

            logger.info("Knowledge base updated successfully")

        except Exception as e:
            logger.error(f"Error updating knowledge base: {str(e)}", exc_info=True)
            raise

    def get_knowledge_base(self) -> Dict:
        """Get the current state of the knowledge base."""
        return self.knowledge_base

    def save_root_knowledge(self, pdf_dir: str, page_num: int, knowledge_data: dict):
        """
        Save knowledge to the root knowledge file by reading existing knowledge_base.json files.

        Args:
            pdf_dir: Directory containing the PDF processing results
            page_num: Page number (1-based)
            knowledge_data: Knowledge data to save
        """
        root_kb_file = os.path.join(pdf_dir, "root_knowledge.json")
        page_dir = os.path.join(pdf_dir, f"page_{page_num:04d}")
        kb_file = os.path.join(page_dir, "knowledge", "knowledge_base.json")

        try:
            # Load existing root knowledge if it exists
            if os.path.exists(root_kb_file):
                with open(root_kb_file, 'r', encoding='utf-8') as f:
                    root_kb = json.load(f)
            else:
                root_kb = {}

            # Read the knowledge_base.json file for this page
            if os.path.exists(kb_file):
                with open(kb_file, 'r', encoding='utf-8') as f:
                    page_knowledge = json.load(f)

                # Save the page knowledge directly to root knowledge
                page_key = f"page{page_num}"
                root_kb[page_key] = page_knowledge

                # Save updated root knowledge
                with open(root_kb_file, 'w', encoding='utf-8') as f:
                    json.dump(root_kb, f, indent=2, ensure_ascii=False)

                logger.info(f"Updated root knowledge file for page {page_num}")
            else:
                logger.warning(f"No knowledge_base.json found for page {page_num}")

        except Exception as e:
            logger.error(f"Error saving root knowledge: {str(e)}")
            raise

    def load_root_knowledge(self, pdf_dir: str) -> dict:
        """
        Load the root knowledge file.

        Args:
            pdf_dir: Directory containing the PDF processing results

        Returns:
            Dictionary containing the root knowledge data
        """
        root_kb_file = os.path.join(pdf_dir, "root_knowledge.json")

        try:
            if os.path.exists(root_kb_file):
                with open(root_kb_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading root knowledge: {str(e)}")
            return {}

    def load_root_metadata(self, pdf_dir: str) -> dict:
        """
        Load the root knowledge file.

        Args:
            pdf_dir: Directory containing the PDF processing results

        Returns:
            Dictionary containing the root knowledge data
        """
        root_kb_file = os.path.join(pdf_dir, "root_metadata.json")

        try:
            if os.path.exists(root_kb_file):
                with open(root_kb_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading root knowledge: {str(e)}")
            return {}

    def clear_knowledge_base(self):
        """Clear the knowledge base."""
        self.knowledge_base = {}
        logger.info("Knowledge base cleared")

    def download_build_permit_report(self, directory: str, model: str = "claude-sonnet-4-20250514", output_filename: str = None) -> str:
        """
        Download building permit analysis as Excel report from knowledge.json.

        Args:
            directory: Directory containing the root knowledge
            model: Model to use for analysis
            output_filename: Custom output filename (optional)

        Returns:
            Path to the generated Excel file
        """
        try:
            # Generate analysis
            analysis_result = self.analyze_building_permit(model=model, directory=directory)

            # Generate output filename if not provided
            if not output_filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                project_name = "building_permit"
                if directory:
                    project_name = os.path.basename(directory.rstrip('/\\'))
                output_filename = f"{project_name}_compliance_report_{timestamp}.xlsx"

            # Ensure output is in the same directory
            output_path = os.path.join(directory, output_filename)

            # Generate Excel report
            self.generate_building_permit_excel_report(analysis_result, output_path)

            # Also save the raw analysis as JSON for reference
            json_output = output_path.replace('.xlsx', '_analysis.json')
            with open(json_output, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, indent=2, ensure_ascii=False)

            logger.info(f"Building permit report downloaded: {output_path}")
            logger.info(f"Raw analysis saved: {json_output}")

            return output_path

        except Exception as e:
            logger.error(f"Error downloading build permit report: {str(e)}")
            raise

    def generate_building_permit_excel_report(self, analysis_result: dict, output_file: str = "building_permit_report.xlsx") -> None:
        """
        Generate Excel report from building permit analysis results with multiple sheets.

        Args:
            analysis_result: Analysis result dictionary from analyze_building_permit
            output_file: Output Excel file path
        """
        try:
            from openpyxl import Workbook
            from openpyxl.styles import (Alignment, Border, Font, PatternFill,
                                         Side)
            from openpyxl.utils import get_column_letter

            # Create workbook
            wb = Workbook()

            # Define styles
            header_font = Font(bold=True, color='FFFFFF', size=12)
            header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
            pass_fill = PatternFill(start_color='90EE90', end_color='90EE90', fill_type='solid')
            fail_fill = PatternFill(start_color='FFB6C1', end_color='FFB6C1', fill_type='solid')

            # Confidence level fills
            very_high_confidence = PatternFill(start_color='006400', end_color='006400', fill_type='solid')
            high_confidence = PatternFill(start_color='008000', end_color='008000', fill_type='solid')
            moderate_confidence = PatternFill(start_color='FFD700', end_color='FFD700', fill_type='solid')
            low_confidence = PatternFill(start_color='FFA500', end_color='FFA500', fill_type='solid')
            very_low_confidence = PatternFill(start_color='FF0000', end_color='FF0000', fill_type='solid')

            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            def create_sheet(name, headers=None):
                """Helper function to create a new sheet with standard formatting"""
                if name in wb.sheetnames:
                    sheet = wb[name]
                else:
                    sheet = wb.create_sheet(name)
                if headers:
                    for col, header in enumerate(headers, 1):
                        cell = sheet.cell(row=1, column=col, value=header)
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.border = thin_border
                return sheet

            def format_sheet(sheet):
                """Apply standard formatting to a sheet"""
                for row in sheet.iter_rows():
                    for cell in row:
                        cell.border = thin_border
                        cell.alignment = Alignment(vertical='center', wrap_text=True)

                        # Apply confidence coloring
                        if cell.value and isinstance(cell.value, str) and '%' in cell.value:
                            try:
                                confidence = safe_confidence(cell.value)
                                if confidence >= 90:
                                    cell.fill = very_high_confidence
                                elif confidence >= 70:
                                    cell.fill = high_confidence
                                elif confidence >= 50:
                                    cell.fill = moderate_confidence
                                elif confidence >= 30:
                                    cell.fill = low_confidence
                                else:
                                    cell.fill = very_low_confidence
                            except (ValueError, TypeError):
                                pass

                        # Apply status coloring
                        if cell.value == '✓':
                            cell.fill = pass_fill
                        elif cell.value == '✗':
                            cell.fill = fail_fill

                # Auto-adjust column widths
                for column in sheet.columns:
                    max_length = 0
                    column_letter = get_column_letter(column[0].column)
                    for cell in column:
                        try:
                            cell_length = len(safe_str(cell.value)) if cell.value else 0
                            max_length = max(max_length, cell_length)
                        except BaseException:
                            pass
                    adjusted_width = min(max(max_length + 2, 15), 60)
                    sheet.column_dimensions[column_letter].width = adjusted_width

            def safe_str(value):
                """Safely convert any value to string, handling None and special cases. Also, prefix with a single quote if it starts with '=' or '+'."""
                if value is None:
                    return ""
                if isinstance(value, (int, float)):
                    s = str(value)
                elif isinstance(value, str):
                    s = value
                else:
                    s = str(value)
                # Prevent Excel formula injection
                if s and (s.startswith('=') or s.startswith('+')):
                    return "'" + s
                return s

            def safe_confidence(value):
                """Safely extract confidence value from string or number."""
                if isinstance(value, (int, float)):
                    return value
                if isinstance(value, str):
                    try:
                        return int(value.strip('%'))
                    except ValueError:
                        return 0
                return 0

            # Get analysis data with proper nesting handling
            if isinstance(analysis_result, dict) and 'analysis_result' in analysis_result:
                if isinstance(analysis_result['analysis_result'], dict) and 'analysis_result' in analysis_result['analysis_result']:
                    analysis = analysis_result['analysis_result']['analysis_result']
                else:
                    analysis = analysis_result['analysis_result']
            else:
                analysis = analysis_result

            # 1. Summary Sheet
            summary_sheet = wb.active
            summary_sheet.title = "Summary"

            # Header
            summary_sheet.merge_cells('A1:G1')
            summary_sheet.cell(row=1, column=1, value="Building Permit Compliance Report").font = Font(bold=True, size=16)

            # Basic Information
            info_data = [
                ["Overall Assessment:", safe_str(analysis.get('overall_assessment', 'UNKNOWN'))],
                ["Overall Confidence:", f"{safe_confidence(analysis.get('overall_confidence', 0))}%"],
                ["Request ID:", safe_str(analysis_result.get('request_id', 'N/A'))],
                ["Model Used:", safe_str(analysis_result.get('model_used', 'N/A'))],
                ["Analysis Date:", datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
            ]

            for idx, (label, value) in enumerate(info_data, 3):
                summary_sheet.cell(row=idx, column=1, value=label).font = Font(bold=True)
                summary_sheet.cell(row=idx, column=2, value=value)

            # 2. Confidence Legend Sheet
            legend_sheet = create_sheet("Confidence Legend")
            legend_data = [
                ["Level", "Range", "Description"],
                ["Very High", "90-100%", "Extremely confident in the assessment"],
                ["High", "70-89%", "Strong confidence in the assessment"],
                ["Moderate", "50-69%", "Moderate confidence, may need review"],
                ["Low", "30-49%", "Low confidence, should be reviewed"],
                ["Very Low", "0-29%", "Very low confidence, requires immediate review"]
            ]

            for row_idx, row_data in enumerate(legend_data, 1):
                for col_idx, value in enumerate(row_data, 1):
                    cell = legend_sheet.cell(row=row_idx, column=col_idx, value=value)
                    if row_idx == 1:
                        cell.font = header_font
                        cell.fill = header_fill

            # 3. Submittal Info Sheet
            submittal_sheet = create_sheet("Submittal Info",
                                           ["Category", "Status", "Confidence", "Value/Details", "Page Found", "Requirements"])

            submittal_type = analysis.get('submittal_type', {})
            if submittal_type:
                row = 2
                submittal_sheet.cell(row=row, column=1, value="Submittal Type")
                submittal_sheet.cell(row=row, column=2, value=safe_str(submittal_type.get('type', 'N/A')))
                submittal_sheet.cell(row=row, column=3, value=f"{safe_confidence(submittal_type.get('confidence', 0))}%")
                submittal_sheet.cell(row=row, column=4, value=safe_str(submittal_type.get('code_edition_verified', 'N/A')))
                submittal_sheet.cell(row=row, column=5, value=safe_str(submittal_type.get('found_on_page', 'N/A')))
                submittal_sheet.cell(row=row, column=6, value="Must specify type")

            # 4. Document Info Sheet
            doc_info_sheet = create_sheet("Document Info",
                                          ["Category", "Status", "Confidence", "Value/Details", "Page Found", "Requirements", "Notes"])

            basic_info = analysis.get('basic_document_info', {})
            if basic_info:
                for row, (key, value) in enumerate(basic_info.items(), 2):
                    if isinstance(value, dict):
                        doc_info_sheet.cell(row=row, column=1, value=key.replace('_', ' ').title())
                        doc_info_sheet.cell(row=row, column=2, value=safe_str(value.get('status', 'N/A')))
                        doc_info_sheet.cell(row=row, column=3, value=f"{safe_confidence(value.get('confidence', 0))}%")
                        doc_info_sheet.cell(row=row, column=4, value=safe_str(value.get('value', value.get('details', '')))[:100])
                        doc_info_sheet.cell(row=row, column=5, value=safe_str(value.get('found_on_page', 'N/A')))
                        doc_info_sheet.cell(row=row, column=6, value="Required for compliance")
                        doc_info_sheet.cell(row=row, column=7, value=safe_str(value.get('details', ''))[:100] if 'details' in value else '')

            # 5. Requirements Sheet
            req_sheet = create_sheet("Requirements",
                                     ["Category", "Status", "Confidence", "Details", "Pages Found", "Requirements"])

            row = 2
            # Dimensional Requirements
            dim_req = analysis.get('dimensional_requirements', {})
            if dim_req:
                req_sheet.cell(row=row, column=1, value="Dimensional Requirements")
                req_sheet.cell(row=row, column=2, value="✓" if dim_req.get('requirements_met') else "✗")
                req_sheet.cell(row=row, column=3, value=f"{safe_confidence(dim_req.get('confidence', 0))}%")
                req_sheet.cell(row=row, column=4, value=safe_str(dim_req.get('details', '')))
                req_sheet.cell(row=row, column=5, value=', '.join(map(safe_str, dim_req.get('found_on_pages', []))))
                req_sheet.cell(row=row, column=6, value="All dimensions must be labeled")
                row += 1

            # Code Compliance
            code_comp = analysis.get('code_compliance', {})
            if code_comp:
                req_sheet.cell(row=row, column=1, value="Code Compliance")
                req_sheet.cell(row=row, column=2, value="✓" if code_comp.get('sections_referenced') else "✗")
                req_sheet.cell(row=row, column=3, value=f"{safe_confidence(code_comp.get('confidence', 0))}%")
                req_sheet.cell(row=row, column=4, value=safe_str(code_comp.get('details', '')))
                req_sheet.cell(row=row, column=5, value=', '.join(map(safe_str, code_comp.get('found_on_pages', []))))
                req_sheet.cell(row=row, column=6, value="Must reference applicable codes")

            # 6. Issues Sheet
            issues_sheet = create_sheet("Issues & Recommendations",
                                        ["Type", "Item", "Action Required"])

            row = 2
            # Deficiencies
            deficiencies = analysis.get('deficiencies', [])
            if deficiencies:
                for idx, deficiency in enumerate(deficiencies, row):
                    issues_sheet.cell(row=idx, column=1, value="Deficiency")
                    issues_sheet.cell(row=idx, column=2, value=safe_str(deficiency))
                    issues_sheet.cell(row=idx, column=3, value="Must be corrected before approval")
                row = idx + 1

            # Recommendations
            recommendations = analysis.get('recommendations', [])
            if recommendations:
                for idx, recommendation in enumerate(recommendations, row):
                    issues_sheet.cell(row=idx, column=1, value="Recommendation")
                    issues_sheet.cell(row=idx, column=2, value=safe_str(recommendation))
                    issues_sheet.cell(row=idx, column=3, value="Improves compliance/quality")

            # 7. Page Analysis Sheet
            page_sheet = create_sheet("Page Analysis",
                                      ["Metric", "Value", "Details"])

            page_summary = analysis.get('page_summary', {})
            if page_summary:
                page_sheet.cell(row=2, column=1, value="Total Pages Analyzed")
                page_sheet.cell(row=2, column=2, value=safe_str(page_summary.get('total_pages_analyzed', 0)))

                page_sheet.cell(row=3, column=1, value="Pages with Critical Info")
                page_sheet.cell(row=3, column=2, value=', '.join(map(safe_str, page_summary.get('pages_with_critical_info', []))))

                page_sheet.cell(row=4, column=1, value="Analysis Confidence")
                page_sheet.cell(row=4, column=2, value=f"{safe_confidence(page_summary.get('confidence', 0))}%")

            # Format all sheets
            for sheet in wb.sheetnames:
                format_sheet(wb[sheet])

            # Save workbook
            wb.save(output_file)
            print(f"Building permit Excel report generated: {output_file}")

        except Exception as e:
            print(f"Error generating Excel report: {e}")
            import traceback
            traceback.print_exc()
            raise

    def analyze_building_permit(self, model: str = "claude-sonnet-4-20250514", directory: str = None) -> dict:
        """
        Analyze building permit using the root knowledge with page location tracking.

        Args:
            model: Model to use for analysis
            directory: Directory containing the root knowledge

        Returns:
            Dictionary containing the analysis results with page references
        """
        try:
            # Load knowledge base from directory if provided
            if directory:
                kb = self.load_root_knowledge(directory)
            else:
                kb = self.knowledge_base

            # Validate model
            if model not in SUPPORTED_MODELS:
                raise ValueError(f"Unsupported model. Please use one of: {', '.join(SUPPORTED_MODELS.keys())}")

            # Get model configuration
            model_config = SUPPORTED_MODELS[model]

            # Prepare base prompt with knowledge base context
            base_prompt = f"""
            Analyze the submitted building permit application documents and check for all required elements according to the 2023 Florida Building Code, Building, Eighth Edition.

            I have provided you with a knowledge base extracted from the PDF containing key information and metadata from different pages.

            The knowledge base contains the following extracted information:
            {json.dumps(kb, indent=2)}

            Please use this knowledge base to perform a comprehensive compliance check for the following mandatory elements and provide a clear pass/fail result for each.

            IMPORTANT: For each compliance item, specify which page(s) the information was found on (e.g., "page1", "page2", etc.) or indicate "NOT FOUND" if missing.

            1. Submittal Type Identification:
            - Identify if this is a Residential, Building, or Accessibility submittal
            - Validate the correct Florida Building Code edition is referenced (must be 2023 Florida Building Code, Eighth Edition)

            2. Basic Document Information:
            - ✓/✗ Total Square Footage clearly stated
            - ✓/✗ Property Address complete and accurate
            - ✓/✗ Building Elevations included
            - ✓/✗ Product Approval List present and complete
            - ✓/✗ Engineer Stamp present, valid and clearly visible
            - ✓/✗ Engineer's wet signature included with date
            - ✓/✗ Construction Type clearly specified
            - ✓/✗ Scope of Work clearly defined
            - ✓/✗ Level of alteration details included (for existing building permits)

            3. Dimensional Requirements:
            - Check if all dimensional requirements specific to this type of submittal are met
            - Verify all measurements are properly labeled with units
            - Confirm scaling is consistent throughout plans

            4. Code Compliance:
            - Verify all applicable code sections are properly referenced
            - Confirm all referenced codes are the correct edition/date

            Generate a compliance report with page references for each finding.

            Please provide your analysis in the following JSON format:
            {{
                "overall_assessment": "APPROVED/REJECTED",
                "submittal_type": {{
                    "type": "Residential/Building/Accessibility",
                    "code_edition_verified": true/false,
                    "found_on_page": "page1/page2/etc or NOT FOUND"
                }},
                "basic_document_info": {{
                    "square_footage": {{
                        "status": "✓/✗",
                        "details": "",
                        "found_on_page": "page1/page2/etc or NOT FOUND",
                        "value": ""
                    }},
                    "property_address": {{
                        "status": "✓/✗",
                        "details": "",
                        "found_on_page": "page1/page2/etc or NOT FOUND",
                        "value": ""
                    }},
                    "building_elevations": {{
                        "status": "✓/✗",
                        "details": "",
                        "found_on_page": "page1/page2/etc or NOT FOUND"
                    }},
                    "product_approval_list": {{
                        "status": "✓/✗",
                        "details": "",
                        "found_on_page": "page1/page2/etc or NOT FOUND"
                    }},
                    "engineer_stamp": {{
                        "status": "✓/✗",
                        "details": "",
                        "found_on_page": "page1/page2/etc or NOT FOUND",
                        "engineer_name": "",
                        "pe_number": ""
                    }},
                    "engineer_signature": {{
                        "status": "✓/✗",
                        "details": "",
                        "found_on_page": "page1/page2/etc or NOT FOUND",
                        "signature_date": ""
                    }},
                    "construction_type": {{
                        "status": "✓/✗",
                        "details": "",
                        "found_on_page": "page1/page2/etc or NOT FOUND",
                        "value": ""
                    }},
                    "scope_of_work": {{
                        "status": "✓/✗",
                        "details": "",
                        "found_on_page": "page1/page2/etc or NOT FOUND",
                        "value": ""
                    }},
                    "alteration_details": {{
                        "status": "✓/✗",
                        "details": "",
                        "found_on_page": "page1/page2/etc or NOT FOUND"
                    }}
                }},
                "dimensional_requirements": {{
                    "requirements_met": true/false,
                    "measurements_labeled": true/false,
                    "scaling_consistent": true/false,
                    "details": "",
                    "found_on_pages": []
                }},
                "code_compliance": {{
                    "sections_referenced": true/false,
                    "correct_edition": true/false,
                    "details": "",
                    "found_on_pages": []
                }},
                "deficiencies": [],
                "recommendations": [],
                "page_summary": {{
                    "total_pages_analyzed": 0,
                    "pages_with_critical_info": []
                }}
            }}
            """

            # Set up messages based on model provider
            if model_config["provider"] == "anthropic":
                messages = [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": base_prompt},
                    ]
                }]
            else:  # OpenAI
                messages = [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": base_prompt},
                    ]
                }]

            # Use LLM to analyze the documents
            completion = litellm.completion(
                model=model,
                messages=messages,
                response_format={"type": "json_object"},
                max_tokens=model_config["default_output_tokens"],
                temperature=0.1
            )

            raw_content = completion.choices[0].message.content.strip()

            # Extract JSON content using regex
            json_match = re.search(r'\{[\s\S]*\}', raw_content)
            if json_match:
                json_content = json_match.group(0)
            else:
                raise ValueError("Could not find valid JSON in the response")

            try:
                analysis_result = json.loads(json_content)
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON response: {str(e)}")
                raise ValueError("Failed to parse LLM response into JSON format")

            # Get token usage
            token_usage = None
            if hasattr(completion, 'usage'):
                token_usage = {
                    "completion_tokens": getattr(completion.usage, 'completion_tokens', 0),
                    "prompt_tokens": getattr(completion.usage, 'prompt_tokens', 0),
                    "total_tokens": getattr(completion.usage, 'total_tokens', 0),
                }

                # Update cost tracking
                self._update_cost_tracking("building_permit_analysis", token_usage)

            # Save analysis result to JSON file
            if directory:
                analysis_file = os.path.join(directory, "building_permit_analysis.json")
                with open(analysis_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "request_id": self.request_id,
                        "model_used": model,
                        "analysis_result": analysis_result,
                        "status": "success",
                        "token_usage": token_usage,
                        "timestamp": datetime.now().isoformat()
                    }, f, indent=2, ensure_ascii=False)
                logger.info(f"Analysis result saved to: {analysis_file}")

            return {
                "request_id": self.request_id,
                "model_used": model,
                "analysis_result": analysis_result,
                "status": "success",
                "token_usage": token_usage
            }

        except Exception as e:
            logger.error(f"Error analyzing building permit: {str(e)}", exc_info=True)
            raise

    """
    Adjusted Building Permit Analysis - Prompt and JSON Structure for Excel Export
    """

    # Updated prompt for building permit analysis
    # Function to convert JSON response to Excel-compatible data structure
    @staticmethod
    def convert_analysis_to_excel_data(analysis_result):
        """
        Convert the JSON analysis result to the Excel data structure format.

        Args:
            analysis_result (dict): The JSON response from the building permit analysis

        Returns:
            list: List of rows for Excel export
        """
        excel_data = []
        print('project_summary' in analysis_result)
        # Project Summary Section (Header rows)
        if 'project_summary' in analysis_result:
            ps = analysis_result['project_summary']
            print("this is the ps", ps)
            excel_data.extend([
                ['Accessory Structure', ps.get('accessory_structure', '')],
                ['Project Action', ps.get('project_action', '')],
                ['Required Documents', ps.get('required_documents', '')],
                ['Total Pages Analyzed', ps.get('total_pages_analyzed', 0)],
                ['Pages with Critical Info', ps.get('pages_with_critical_info', '')],
                ['Overall Assessment', ps.get('overall_assessment', '')],
                ['Overall Confidence', ps.get('overall_confidence', 0)],
                [],  # Empty row
                []  # Empty row
            ])

        # Submittal Type Identification Section
        if 'submittal_type_identification' in analysis_result:
            excel_data.append(['Submittal Type Identification', 'Details', 'Pass/Fail', 'Required', 'Found'])

            sti = analysis_result['submittal_type_identification']
            for key, item in sti.items():
                excel_data.append([
                    item['field'],
                    item['value'],
                    item['status'],
                    item['required'],
                    item['found_on']
                ])
            excel_data.append([])  # Empty row

        # Basic Document Information Section
        if 'basic_document_information' in analysis_result:
            excel_data.append(['Basic Document Information'])

            bdi = analysis_result['basic_document_information']
            for key, item in bdi.items():
                excel_data.append([
                    item['field'],
                    item['value'],
                    item['status'],
                    item['required'],
                    item['found_on']
                ])
            excel_data.append([])  # Empty row

        # Dimensional Requirements Section
        if 'dimensional_requirements' in analysis_result:
            excel_data.append(['Dimensional Requirements'])

            dr = analysis_result['dimensional_requirements']
            for key, item in dr.items():
                excel_data.append([
                    item['field'],
                    item['value'],
                    item['status'],
                    item['required'],
                    item['found_on']
                ])
            excel_data.append([])  # Empty row

        # Code Compliance Section
        if 'code_compliance' in analysis_result:
            excel_data.append(['Code Compliance'])

            cc = analysis_result['code_compliance']
            for key, item in cc.items():
                excel_data.append([
                    item['field'],
                    item['value'],
                    item['status'],
                    item['required'],
                    item['found_on']
                ])
            excel_data.append([])  # Empty row

        # Document Completeness Section
        if 'document_completeness' in analysis_result:
            excel_data.append(['Document completeness'])

            dc = analysis_result['document_completeness']
            for key, item in dc.items():
                excel_data.append([
                    item['field'],
                    item['value'],
                    item['status'],
                    item['required'],
                    item['found_on']
                ])

        return excel_data

    # Function to create Excel file from analysis result
    def create_excel_report(self, analysis_result, output_file='building_permit_analysis.xlsx'):
        """
        Create an Excel file from the building permit analysis result.

        Args:
            analysis_result (dict): The JSON response from analysis
            output_file (str): Path to output Excel file

        Returns:
            str: Path to created Excel file
        """
        from openpyxl import Workbook

        # Convert analysis to Excel data
        excel_data = KnowledgeBuilder.convert_analysis_to_excel_data(analysis_result)
        print("this is the excel data", excel_data)
        # Create workbook and worksheet
        wb = Workbook()
        ws = wb.active
        ws.title = "Project Summary"

        # Add data to worksheet
        for row_data in excel_data:
            ws.append(row_data)

        # Save workbook
        wb.save(output_file)

        return output_file

    # Example usage in the analyze_building_permit function:
    async def analyze_building_permit_with_excel_export(self, model: str = "claude-sonnet-4-20250514", directory: str = None, export_excel: bool = True, status_queue=None) -> dict:
        """
        Enhanced version of analyze_building_permit that includes Excel export capability.
        """
        try:
            if status_queue:
                await status_queue.put({"event_type": "excel_export_start", "message": "Excel export started", "directory": directory})
            # Load knowledge base
            if directory:
                kb = self.load_root_metadata(directory)
            else:
                kb = self.knowledge_base
            # Validate model
            if model not in SUPPORTED_MODELS:
                raise ValueError(f"Unsupported model. Please use one of: {', '.join(SUPPORTED_MODELS.keys())}")
            model_config = SUPPORTED_MODELS[model]
            # Prepare prompt with knowledge base
            base_prompt = self.BUILDING_PERMIT_ANALYSIS_PROMPT.format(knowledge_base=json.dumps(kb, indent=2))
            # Set up messages
            messages = [{
                "role": "user",
                "content": [{"type": "text", "text": base_prompt}]
            }]
            # Get LLM analysis
            completion = await litellm.acompletion(
                model=model,
                messages=messages,
                response_format={"type": "json_object"},
                max_tokens=model_config["default_output_tokens"],
                temperature=0.1
            )
            if status_queue:
                await status_queue.put({"event_type": "excel_llm_complete", "message": "LLM analysis complete for Excel export", "directory": directory})
            raw_content = completion.choices[0].message.content.strip()
            # Parse JSON response
            json_match = re.search(r'\{[\s\S]*\}', raw_content)
            if json_match:
                json_content = json_match.group(0)
            else:
                raise ValueError("Could not find valid JSON in the response")
            try:
                analysis_result = json.loads(json_content)
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON response: {str(e)}")
                raise ValueError("Failed to parse LLM response into JSON format")
            # Get token usage
            token_usage = None
            if hasattr(completion, 'usage'):
                token_usage = {
                    "completion_tokens": getattr(completion.usage, 'completion_tokens', 0),
                    "prompt_tokens": getattr(completion.usage, 'prompt_tokens', 0),
                    "total_tokens": getattr(completion.usage, 'total_tokens', 0),
                }
                self._update_cost_tracking("building_permit_analysis", token_usage)
            # Save analysis result to JSON file
            if directory:
                if status_queue:
                    await status_queue.put({"event_type": "complete", "permit_analysis": analysis_result, "directory": directory})
                analysis_file = os.path.join(directory, "building_permit_analysis.json")
                with open(analysis_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        "request_id": self.request_id,
                        "model_used": model,
                        "analysis_result": analysis_result,
                        "status": "success",
                        "token_usage": token_usage,
                        "timestamp": datetime.now().isoformat()
                    }, f, indent=2, ensure_ascii=False)
                logger.info(f"Analysis result saved to: {analysis_file}")

                # Export to Excel if requested
                if export_excel:
                    excel_file = os.path.join(directory, "building_permit_analysis.xlsx")
                    self.create_excel_report(analysis_result, excel_file)
                    logger.info(f"Excel report saved to: {excel_file}")
            return {
                "request_id": self.request_id,
                "model_used": model,
                "analysis_result": analysis_result,
                "status": "success",
                "token_usage": token_usage
            }
        except Exception as e:
            logger.error(f"Error analyzing building permit: {str(e)}", exc_info=True)
            if status_queue:
                await status_queue.put({"event_type": "excel_export_error", "message": str(e), "directory": directory})
            raise


if __name__ == "__main__":
    import glob

    async def process_directory(base_dir: str):
        # Initialize the knowledge builder
        kb = KnowledgeBuilder(model="gpt-4.1")

        try:
            # Get all images in the directory
            image_patterns = [
                os.path.join(base_dir, "*.jpg"),           # Direct jpg files
                os.path.join(base_dir, "*.jpeg"),          # Direct jpeg files
                os.path.join(base_dir, "*.png"),           # Direct png files
                os.path.join(base_dir, "*.gif")            # Direct gif files
            ]

            # Collect all image files from different patterns
            image_files = []
            for pattern in image_patterns:
                files = glob.glob(pattern)
                if files:
                    logger.info(f"Found {len(files)} images with pattern: {pattern}")
                    image_files.extend(files)

            # Remove duplicates and sort
            image_files = sorted(list(set(image_files)))

            if not image_files:
                logger.warning(f"No images found in directory: {base_dir}")
                return

            logger.info(f"Total unique images found: {len(image_files)}")

            # Process all images in a single request
            response = await kb.wait_for_response(image_files, "unknown", "unknown", "text")

            if response:
                # Log the results
                logger.info(f"Successfully processed {len(image_files)} images")
                logger.info(f"Processing time: {response.processing_time:.2f} seconds")
                logger.info(f"Token usage: {response.token_usage}")

                # Log the number of new facts added
                new_facts = len(response.knowledge_base.get('knowledge_updates', {}).get('new_facts', []))
                logger.info(f"Added {new_facts} new facts to knowledge base")
            else:
                logger.error("Failed to process images")

            # Log final cost summary
            logger.info("=== Final Cost Summary ===")
            logger.info(f"Total cost: ${kb.cost_tracking['total_cost']:.4f}")
            logger.info(f"Total prompt tokens: {kb.cost_tracking['token_usage']['total_prompt_tokens']}")
            logger.info(f"Total completion tokens: {kb.cost_tracking['token_usage']['total_completion_tokens']}")
            logger.info(f"Total tokens: {kb.cost_tracking['token_usage']['total_tokens']}")

        except Exception as e:
            logger.error(f"Error in main process: {str(e)}", exc_info=True)
            raise

    dir_ = r"C:\Users\<USER>\Downloads\plan-2\pavillion\Approved-BR-NEW-25-00037"

    # dir_ = 'C:/Users/<USER>/Downloads/residensial_New_commericial/Alicia Herrin Site Plan'
    kb = KnowledgeBuilder(model="claude-sonnet-4-20250514")

    # Fix: Call the method on the kb instance
    kb.analyze_building_permit_with_excel_export(directory=dir_)
    # with open(os.path.join(dir_, 'building_permit_analysis.json'), 'rb') as file:
    #     analysis_result = json.load(file)
    # print(analysis_result)
    # kb.create_excel_report(analysis_result.get('analysis_result'), os.path.join(dir_, 'building_permit_analysis.xlsx'))
    # excel_report_path = kb.download_build_permit_report(
    #         directory=dir_,
    #         model="claude-sonnet-4-20250514",
    #         output_filename="my_building_permit_report.xlsx"
    #     )

    # print(f"Excel report generated successfully: {excel_report_path}")

    # # Run the async main function with the specified directory path
    # asyncio.run(process_directory(r"C:\Users\<USER>\Desktop\LLM SPLIT\split_images"))

