from typing import Dict, List
from app.crud.mongo.base_repository import BaseRepository as MongoRepo
from app.database.database_storage import get_mongo_db
from datetime import datetime, timezone
from app.models.permission_model import Permissions, RolePermission
from app.models.user_model import Roles

_MONGO_REPO_USER_CONFIG = MongoRepo("user_config")  # Repository for user configs


def remove_object_id(record):
    """
    Convert ObjectId to string for easier handling in JSON responses.
    """
    del record["_id"]
    return record


async def initialize_default_permissions():
    """Initialize default permissions for all roles if they don't exist."""
    default_permissions = {
        Roles.superadmin: {
            "viewUsers": True,
            "editUser": True,
            "manageRoles": True,
            "createCoa": True,
            "viewCoa": True,
            "createApplication": True,
            "viewApplication": True,
            "viewKnowledgeBase": True,
            "uploadKnowledgeBase": True,
        },
        Roles.admin: {
            "viewUsers": True,
            "editUser": True,
            "manageRoles": False,
            "createCoa": True,
            "viewCoa": True,
            "createApplication": True,
            "viewApplication": True,
            "viewKnowledgeBase": True,
            "uploadKnowledgeBase": True,
        },
        Roles.drafter: {
            "viewUsers": True,
            "editUser": False,
            "manageRoles": False,
            "createCoa": True,
            "viewCoa": True,
            "createApplication": True,
            "viewApplication": True,
            "viewKnowledgeBase": False,
            "uploadKnowledgeBase": False,
        },
        Roles.enforcer: {
            "viewUsers": False,
            "editUser": False,
            "manageRoles": False,
            "createCoa": False,
            "viewCoa": True,
            "createApplication": False,
            "viewApplication": True,
            "viewKnowledgeBase": False,
            "uploadKnowledgeBase": False,
        },
    }

    for role, permissions in default_permissions.items():
        existing = _MONGO_REPO_USER_CONFIG.get_one_by_filter(
            {"role": role},
            db=get_mongo_db())
        if not existing:
            role_permission = RolePermission(
                role=role,
                permissions=Permissions(**permissions),
            )
            _MONGO_REPO_USER_CONFIG.create(
                role_permission.model_dump(),
                db=get_mongo_db())


async def get_all_permissions() -> List[Dict]:
    """Get all role permissions."""
    return _MONGO_REPO_USER_CONFIG.get_all(db=get_mongo_db())


async def get_role_permissions(role: Roles) -> Dict:
    """Get permissions for a specific role."""
    permission = _MONGO_REPO_USER_CONFIG.get_one_by_filter(
        {"role": role},
        db=get_mongo_db())
    remove_object_id(permission)
    return permission


async def update_role_permissions(role: Roles, permissions: Permissions) -> bool:
    """Update permissions for a specific role."""
    if role == Roles.superadmin:
        return False

    result = _MONGO_REPO_USER_CONFIG.update_one_by_query_upsert(
        {"role": role},
        {
            "permissions": permissions.model_dump(),
            "updated_at": datetime.now(timezone.utc),
        },
        db=get_mongo_db(),
    )
    return result.modified_count > 0
