Try to identify the following parameters from the file name and its initial pages attached at the end of the prompt:
1. Summary: a brief summary
2. Category: a single category under which the file might fall into. If it comes under any one of these predefined categories: {categories}, then return that, or try to identify a new one
3. Zoning: a single zoning mentioned in the file, example: wetland
4. Keywords: an array of unique keywords or tags which could help in searching among many files based on a user question

Provide the answer as a JSON string like so: 'summary': ..., 'category': ..., 'zoning': ..., 'keywords': ...
If a parameter is not found then return either empty string or array

Following are the file name and its initial pages: