
additional_prompt_message = """Additional mpudCategory considerations:
        mpudCategory Selection Guidelines:
        The mpudCategory property must be set to one of these exact values:
        "non-substantial-modification"
        "substantial-modification"
        "zoning-amendment"
        "land-use-equivalency-request"
        """

additional_prompt_coa_message = """
            "Your task is to assist the user in selecting the titles needed for the conditions of approval."
            "First ask if they are ready to start with creating sections for COA (conditions of approval)"
            "Secondly, please present a well-organized and clearly formatted list of all available titles so the user can easily view and choose the appropriate options. ask them in the list which all would they need"
            "###Below are the sections\n\n"
            "Master Development Plans"
            "general"
            "environmental"
            "openspace/buffering"
            "Transportation/Circulation"
            "Access Management"
            "Dedication of Right-of-Way"
            "Design/Construction Specifications"
            "Utilities/Water Service/Wastewater Disposal"
            "Stormwater"
            "Land Use"
            "Procedures"
            "\n\n"
            "# Below is the structure of 'points for each sections'"
            "{"section1":{"sub_section":[{"point_title":"point_data"}, {"point_title":"point_data"}]}, section2":{"sub_section":["point_title":"point_data"]}}"
            "***main thing : Do not truncate the point data use as it is exactly"
            "So when they accept to start selecting points with a section ask them questions in this order (so as to keep the questions minimum)"
            "Follow the flow below:"
            "Ask if they want to select all points for the sub_section if they respond with 'no' ask them if they want to select the point_title (make the question as logical and meaning full as possible)
            And keep going on like this and when accepting accept the point_data"



            "###Below is the actual 'points for each section'"

        """

model_prompt = {

    "additional_prompt_message" : additional_prompt_message,
    "additional_prompt_coa_message" : additional_prompt_coa_message

}
