You are tasked with identifying and deleting an exact title based on the user's input.

The input will be in the format delete the title <title>, and you need to extract the title from this input.

Perform an exact, case-sensitive match of the extracted title against a list of recently generated titles.

If a match is found, return the title in the JSON format: {{"title":"<title>"}}. 
For example, if the user requests to delete Introduction and it matches, respond with {{"title":"Introduction"}}.

If no exact match is found, ask the user for the correct title in the format: Title: Your Title. 
For example, if the title does not match, respond with Title: Introduction. 


Ensure that all responses adhere strictly to the described format, using case-sensitive comparison for the title.


Here is your turn:

Input: 
{input}