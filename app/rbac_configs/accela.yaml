endpoints:
  # Authentication endpoints - JWT required except callback
  "/accela/login":
    get:
      requires_auth: true
      allowed_roles: ["*"]
      rate_limit:
        requests_per_minute: 10

  "/accela/callback":
    post:
      requires_auth: false
      rate_limit:
        requests_per_minute: 10

  "/accela/auth/status":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30

  "/accela/auth/logout":
    post:
      requires_auth: true
      allowed_roles: ["*"]
      rate_limit:
        requests_per_minute: 10

  "/accela/auth/refresh":
    post:
      requires_auth: false
      rate_limit:
        requests_per_minute: 10

  "/accela/auth/debug":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30

  "/accela/health":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 60

  # Records endpoints - no JWT required
  "/accela/records":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30
    post:
      requires_auth: false
      rate_limit:
        requests_per_minute: 20

  "/accela/records/{record_id}":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30
    put:
      requires_auth: false
      rate_limit:
        requests_per_minute: 20

  # Conditions endpoints - no JWT required
  "/accela/conditions/{record_id}":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30
    post:
      requires_auth: false
      rate_limit:
        requests_per_minute: 20

  "/accela/conditions/{record_id}/{condition_id}":
    put:
      requires_auth: false
      rate_limit:
        requests_per_minute: 20
    delete:
      requires_auth: false
      rate_limit:
        requests_per_minute: 10

  # Workflow endpoints - no JWT required
  "/accela/workflow/{record_id}":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30

  "/accela/workflow/{record_id}/actions":
    post:
      requires_auth: false
      rate_limit:
        requests_per_minute: 20

  # Document endpoints - no JWT required
  "/accela/documents/{record_id}":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30

  # Settings endpoints - no JWT required
  "/accela/settings/types":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30

  "/accela/settings/conditions/types":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30

  "/accela/settings/condition-groups":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30

  "/accela/settings/conditions/standard":
    get:
      requires_auth: false
      rate_limit:
        requests_per_minute: 30 