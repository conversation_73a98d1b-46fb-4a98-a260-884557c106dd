endpoints:
  "/users":
    get:
      requires_auth: true
      allowed_roles: ["superadmin", "admin"]
      required_permissions: ["viewUsers"] # Add required permissions
      rate_limit:
        requests_per_minute: 30

  "/users/{username}/role":
    put:
      requires_auth: true
      allowed_roles: ["superadmin", "admin"]
      required_permissions: ["editUser", "manageRoles"] # Add required permissions
      rate_limit:
        requests_per_minute: 20
