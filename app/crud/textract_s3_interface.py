import boto3
from app.core.config import settings
import json
from app.core.telemetry import get_logger

logger = get_logger("pasco.ai.connector")

s3_client = boto3.client(
    "s3",
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    region_name=settings.AWS_REGION,
)


def process_pdf_path(path):
    path = "textract" + path
    if path.endswith(".pdf"):
        return path[:-4] + ".json"
    if not path.endswith(".json"):
        return path + ".json"
    return path


def get_textract_obj_from_s3(pdf_bucket, pdf_path):
    try:
        logger.info("Getting from s3..")
        pdf_path = process_pdf_path(pdf_path)
        response = s3_client.get_object(Bucket=pdf_bucket, Key=pdf_path)
        file_content = response["Body"].read().decode("utf-8")
        json_data = json.loads(file_content)
        return json_data
    except Exception as e:
        logger.error(f"Error getting JSO<PERSON> from S3: {str(e)}")
        return None


def upload_obj_to_s3(pdf_bucket, pdf_path, json_obj):
    json_data = json.dumps(json_obj)
    try:
        pdf_path = process_pdf_path(pdf_path)
        s3_client.put_object(
            Bucket=pdf_bucket,
            Key=pdf_path,
            Body=json_data,
            ContentType="application/json",
        )

        logger.info(f"Successfully uploaded JSON to {pdf_bucket}/{pdf_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to upload JSON to S3: {str(e)}")
        return False
