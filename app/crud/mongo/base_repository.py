from app.database.database_storage import Database
from pymongo import ASCENDING


class BaseRepository(object):
    __slots__ = ["collection"]

    def __init__(self, table_name):
        self.collection: str = table_name

    def create(self, element, db: Database):
        """Create a new element in the repository."""
        insert_id = db[self.collection].insert_one(element)
        return insert_id

    def create_many(self, elements, db: Database):
        """Create a new element in the repository."""
        return db[self.collection].insert_many(elements)

    def get_all(self, db: Database, limit: int = 0):
        """Get all elements"""
        return list(db[self.collection].find({}, {"_id": 0}).limit(limit))

    def get_one_by_projection(self, querry: dict, project: dict, db: Database):
        """Get one projection"""
        return db[self.collection].find_one(querry, project)

    def get_one_by_filter(self, query, db: Database):
        return db[self.collection].find_one(query)

    def get_by_filter(self, query, projection, db: Database):
        return db[self.collection].find(query, projection)

    def get_all_by_projection(
        self,
        querry: dict,
        project: dict,
        db: Database,
        limit: int = 0,
        sort_key: str = None,
        sort_direction: int = ASCENDING,
    ):
        """Get all by projection"""
        cursor = db[self.collection].find(querry, project).limit(limit)
        if sort_key:
            cursor = cursor.sort(sort_key, sort_direction)
        return list(cursor)

    def update_one_by_query_upsert(self, query: dict, fields: dict, db: Database):
        """update one elements"""
        return db[self.collection].update_one(query, {"$set": fields}, upsert=True)

    def update_many_by_query_upsert(self, query: dict, fields: dict, db: Database):
        """update many elements"""
        return db[self.collection].update_many(query, {"$set": fields}, upsert=True)

    def aggregate_with_unwind(self, match: dict, unwind: dict, group: dict, db: Database):
        """update one elements"""
        return db[self.collection].aggregate([match, unwind, match, group])

    def distinct_key_by_query(self, key: str, querry: dict, db: Database):
        """distinct elements"""
        return db[self.collection].distinct(key, querry)

    def update_many_by_querry_upsert(self, querry: dict, fields: dict, db: Database):
        """update one elements"""
        return db[self.collection].update_many(querry, {"$set": fields}, upsert=True)

    def update_many_by_querry_upsert_array_filters(self, querry: dict, fields: dict, arrayFilters: list, db: Database):
        """update many by arrayFilters"""
        return db[self.collection].update_many(querry, {"$set": fields}, array_filters=arrayFilters, upsert=True)

    def delete_many(self, query: dict, db):
        """Delete many based on query"""
        return db[self.collection].delete_many(query)

    def delete_one(self, query: dict, db):
        """Delete many based on query"""
        return db[self.collection].delete_one(query)

