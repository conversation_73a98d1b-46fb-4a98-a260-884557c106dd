from langchain.agents import tool
from app.models.inspector import Execute<PERSON>uery, GenerateQuery
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from app.core.config import settings
from langchain_core.runnables import RunnablePassthrough
from langchain.agents.format_scratchpad.openai_tools import (
    format_to_openai_tool_messages,
)

from langchain.agents.output_parsers.openai_tools import OpenAIToolsAgentOutputParser
from langchain.prompts import PromptTemplate
from neo4j import GraphDatabase
from pathlib import Path
from langchain.agents import AgentExecutor
from langchain_openai import ChatOpenAI
import time
from app.core.telemetry import get_logger

logger = get_logger("pasco.ai.connector")

schema = """"""
relationships = """"""
label_description_dict = {}
driver = GraphDatabase.driver(
    settings.NEO4J_URI, auth=(settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD),
)
try:
    with driver.session() as session:
        query = """
        MATCH (n)
        WITH DISTINCT labels(n) AS nodeLabels, keys(n) AS propertyKeys
        UNWIND nodeLabels AS label
        RETURN label, COLLECT(DISTINCT propertyKeys) AS properties;
        """
        result = session.run(query)
        label_property_dict = {}

        for record in result:
            label = record["label"]
            properties = record["properties"]
            flat_properties = [item for sublist in properties for item in sublist]
            label_property_dict[label] = list(set(flat_properties))

        query = """
        MATCH (n)
        WITH DISTINCT labels(n) AS nodeLabels, n.description AS propertyValue
        UNWIND nodeLabels AS label
        RETURN label, propertyValue;
        """
        result = session.run(query)
        for record in result:
            label = record["label"]
            description = record["propertyValue"]
            if label not in label_description_dict:
                label_description_dict[label] = description

        for key, val in label_property_dict.items():
            node = (
                f"- {key}: {label_description_dict[key]}\n"
                "   properties:\n" + "\n".join([f"     - {i}" for i in val])
            )
            schema += node
            schema += "\n"

        query = """
        MATCH (n)
        WITH DISTINCT labels(n) AS nodeLabels, n
        UNWIND nodeLabels AS label
        WITH DISTINCT label, n
        MATCH (n)-[r]->(m)
        RETURN DISTINCT label, type(r) AS relationshipType, labels(m) AS targetLabels
        """
        result = session.run(query)

        for record in result:
            relationships += f"- ({record['label']}) -[:{record['relationshipType']}]-> ({record['targetLabels'][0]})\n"
except Exception:
    pass
finally:
    driver.close()


class InspectorRepository:
    def __init__(self):
        self.open_api_key = settings.OPENAI_API_KEY
        self.model = "gpt-4o-mini"
        self.llm_openai = ChatOpenAI(
            openai_api_key=self.open_api_key, model=self.model, temperature=0,
        )

    def _format_structure_content(self, content, indent_level=0):
        if not content:
            return ""

        result = []
        indent = "    " * indent_level  # 4 spaces per indentation level

        for key, value in content.items():
            key_str = str(key).strip()

            if isinstance(value, dict):
                # Handle nested dictionary
                nested_content = self._format_structure_content(value, indent_level + 1)
                result.append(f"{indent}{key_str}:\n{nested_content}")
            elif isinstance(value, list):
                # Handle list of items
                result.append(f"{indent}{key_str}:")
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        # Handle dictionary within list
                        list_content = self._format_structure_content(item, indent_level + 1)
                        result.append(f"{indent}Item {i + 1}:\n{list_content}")
                    else:
                        # Handle simple list items
                        result.append(f"{indent}    {str(item).strip()}")
            else:
                # Handle regular key-value pair
                value_str = str(value).strip()
                result.append(f"{indent}{key_str}:\n{indent}{value_str}")

        return "\n\n".join(result)

    def __format_history(self, history):
        formatted_history = []
        for each_his in history:
            formatted_history.append(("human", each_his["human"]))
            formatted_history.append(("ai", self._format_structure_content(each_his["ai"])))
        return formatted_history

    def __get_prompt(self, prompt_path):
        path = Path(__file__).parent / f"../prompts/{prompt_path}"
        base_prompt = open(path, mode="r").read()
        return base_prompt

    def __get_tools(self):
        @tool("generate-query", args_schema=GenerateQuery)
        def get_query(input: str) -> str:
            """
            Generates a Cypher query based on the user's input and the specified collection schema.

            **Parameters:**
            ----------
            input : str
                The user's natural language question or input describing the data they want to retrieve.

            **Returns:**
            -------
            str
                A string representing the Cypher query.
            """

            generate_query_prompt = self.__get_prompt("generate_query.prompt")
            query_creation_prompt = PromptTemplate(
                template=generate_query_prompt,
                input_variables=["user_question", "schema", "relationship"],
            )
            llm_sequence = query_creation_prompt | self.llm_openai
            response = llm_sequence.invoke(
                {
                    "user_question": input,
                    "schema": schema,
                    "relationship": relationships,
                },
            )

            response_text = (
                response.content.replace("Output: ", "")
                .replace("```json", "")
                .replace("`", "")
            )

            return response_text

        @tool("execute-query", args_schema=ExecuteQuery, return_direct=False)
        def exec_query(query: str) -> str:
            """
            Execute the Cypher query on neo4j and return the result

            **Parameters:**
            ----------
            query : str
                The Cypher query to be executed.

            **Returns:**
            -------
            str
                A string representing the Cypher query.
            """
            try:
                with driver.session() as session:
                    result = session.run(query)
                    structured_content = []
                    if "WHERE" in query:
                        data_list = list(result.data())
                        logger.debug("Data list: %s", data_list)
                        for data in data_list:
                            cur_data = {}
                            if data["n"]["level"] > 5:
                                result = session.run(
                                    """
                                    MATCH (n {coordinates: $coordinates})<-[]-(m) return m
                                    """,
                                    coordinates=data['n'].get('coordinates', ''),
                                )
                                header = list(result.data())
                                if not header:
                                    continue
                                else:
                                    header = header[0]
                                cur_data["title"] = header['m']['name']
                                cur_data["meta_data"] = {
                                    "file_name": header["m"]["file_name"],
                                    "page_no": header["m"].get('page', ''),
                                    "coordinates": header["m"].get('coordinates', ''),
                                }
                            else:
                                cur_data["title"] = data["n"].get(
                                    "data", data["n"].get("name", ""),
                                )
                                cur_data["meta_data"] = {
                                    "file_name": data["n"]["file_name"],
                                    "page_no": data["n"].get('page', ''),
                                    "coordinates": data["n"].get('coordinates', ''),
                                }
                            cur_data["conditions"] = []
                            sorted_nodes = sorted(
                                data["m_nodes"],
                                key=lambda x: (x["level"], x["sequence"]),
                            )
                            for each_node in sorted_nodes:
                                cur_cond = {
                                    "text": each_node.get(
                                        "data", each_node.get("name", ""),
                                    ),
                                    "meta_data": {
                                        "file_name": each_node["file_name"],
                                        "page_no": each_node.get('page', ''),
                                        "coordinates": each_node.get('coordinates', ''),
                                    },
                                }
                                cur_data["conditions"].append(cur_cond)
                            structured_content.append(cur_data)

                        if structured_content:
                            exec_query.return_direct = True
                            return (
                                "Please take a look on the MPUD previewer",
                                structured_content,
                            )
                        else:
                            answer = ""
                            data_list = list(result.data())
                            logger.debug("Data list: %s", data_list)
                            for ans in data_list:
                                if answer:
                                    answer += "\n"
                                answer += ans["m_nodes"][0]
                            return (answer, [])

                    else:
                        answer = ""
                        data_list = list(result.data())
                        logger.debug("Data list: %s", data_list)
                        for ans in data_list:
                            if answer:
                                answer += "\n"
                            answer += ans["m_nodes"][0]
                        return (answer, [])
            except Exception as e:
                logger.error("Error: %s", e)
                return None

        return [get_query, exec_query]

    def inspector(self, user_input: str, messages: list):
        tools = self.__get_tools()
        human = """
            {input}

            {agent_scratchpad}
            """
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", self.__get_prompt("inspector.prompt")),
                (MessagesPlaceholder(variable_name="history", optional=True)),
                ("user", human),
            ],
        )
        llm_with_tools = self.llm_openai.bind_tools(tools)
        agent = (
            {
                "input": lambda x: x["input"],
                "agent_scratchpad": lambda x: format_to_openai_tool_messages(
                    x["intermediate_steps"],
                ),
            }
            | RunnablePassthrough.assign(
                history=lambda x: self.__format_history(messages),
            )
            | prompt
            | llm_with_tools
            | OpenAIToolsAgentOutputParser()
        )
        agent_executor = AgentExecutor(
            agent=agent, tools=tools, verbose=True, max_iterations=5,
        )

        # Get the full response
        for response in agent_executor.stream({"input": user_input}):
            if "output" in response:
                res = response["output"]
                if isinstance(res, str):
                    answer = res
                    structured_content = []
                    words = answer.split(' ')
                    result = ''
                    for word in words:
                        result += word + ' '
                        yield {'answer': result, 'structured_content': []}
                        time.sleep(0.05)  # Optional delay to simulate streaming speed
                else:
                    answer, structured_content = res
                    words = answer.split(' ')
                    result = ''
                    for word in words:
                        result += word + ' '
                        yield {'answer': result, 'structured_content': []}
                        time.sleep(0.05)  # Optional delay to simulate streaming speed
                    for i in range(len(structured_content)):
                        yield {'answer': answer, 'structured_content': structured_content[0:i + 1]}
                        time.sleep(0.05)
                yield {'answer': answer, 'structured_content': structured_content}
