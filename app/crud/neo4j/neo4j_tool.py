from neo4j import GraphDatabase
import numpy as np
import time
import json
import anthropic
from app.core.config import settings


def process_similar_coa(similar_points_results, description, api_key=None):
    api_key = settings.ANTHROPIC_API_KEY if api_key is None else api_key
    client = anthropic.Client(api_key=api_key)

    system_prompt = """
You are a precise condition matching system that evaluates environmental conditions against specific descriptions.

Instructions for matching:
1. A condition matches ONLY if it directly and explicitly relates to the description
2. Reject matches that:
   - Are only tangentially related
   - Share keywords but different contexts
   - Are broader/narrower than the description
   - Require assumptions to connect to the description
3. For each condition, ask:
   - Does it explicitly address the core concept of the description?
   - Would a domain expert agree these are equivalent?
4. Return ONLY a JSON array of boolean values:
   {"matches": [true/false values]}
"""

    filtered_results = []

    for group in similar_points_results:
        conditions = group["conditions"]
        metadata = group["metadata"]
        avg_similarity = group["avg_similarity"]

        user_prompt = f"""<Description>
{description}
</Description>

Evaluate these conditions strictly:
{json.dumps(conditions)}

Return matches array in JSON:
{{"matches": [true/false values]}}"""

        try:
            response = client.messages.create(
                model="claude-3-haiku-20240307",
                system=system_prompt,
                messages=[
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=1024,
                temperature=0
            )

            content = response.content[0].text.strip()

            # Extract JSON content more robustly
            try:
                # Try direct JSON parsing first
                matches = json.loads(content)["matches"]
            except json.JSONDecodeError:
                # If direct parsing fails, try to extract JSON from markdown code blocks
                if '```json' in content:
                    json_content = content.split('```json')[1].split('```')[0].strip()
                elif '```' in content:
                    json_content = content.split('```')[1].split('```')[0].strip()
                else:
                    # Try to find JSON object boundaries
                    start = content.find('{')
                    end = content.rfind('}') + 1
                    if start >= 0 and end > start:
                        json_content = content[start:end]
                    else:
                        raise ValueError("Could not find valid JSON content in response")

                matches = json.loads(json_content)["matches"]

            # Validate matches array length
            if len(matches) != len(conditions):
                raise ValueError(f"Mismatch between conditions ({len(conditions)}) and matches ({len(matches)})")

            # Filter conditions based on matches
            matched_conditions = [cond for cond, match in zip(conditions, matches) if match]

            if matched_conditions:
                filtered_results.append({
                    "conditions": matched_conditions,
                    "metadata": metadata,  # Retain all metadata
                    "avg_similarity": avg_similarity
                })

        except Exception as e:
            print(f"Error processing response: {str(e)}")
            print(f"Response content: {content}")  # Log the problematic content
            continue

    return filtered_results


class Neo4jTool:
    def __init__(self, uri="neo4j://localhost:7687", user="neo4j", password="password"):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))

    def close(self):
        self.driver.close()

    def upsert_file_node(self, filename, uploaded_timestamp):
        with self.driver.session() as session:
            session.run("""
                MERGE (f:File {name: $filename})
                SET f.uploaded_timestamp = $uploaded_timestamp
            """, filename=filename, uploaded_timestamp=uploaded_timestamp)

    def upsert_section(self, parent_label, parent_name, section_name):
        with self.driver.session() as session:
            session.run(f"""
                MATCH (parent:{parent_label} {{name: $parent_name}})
                MERGE (child:Section {{name: $section_name}})
                MERGE (parent)-[:HAS_SECTION]->(child)
            """, parent_name=parent_name, section_name=section_name)

    def upsert_point(self, section_name, condition, filename, embedding, chunk_id=None, coordinates=None, page=None, model_name="voyage-3-large", uploaded_timestamp=None):
        if uploaded_timestamp is None:
            uploaded_timestamp = time.time()

        with self.driver.session() as session:
            session.run("""
                MATCH (s:Section {name: $section_name})
                MERGE (p:COA { condition: $condition, source: $filename })
                SET p.embedding = $embedding,
                    p.embedding_model_name = $model_name,
                    p.uploaded_timestamp = $uploaded_timestamp,
                    p.chunk_id = $chunk_id,
                    p.coordinates = $coordinates,
                    p.page = $page
                MERGE (s)-[:HAS_COA]->(p)
            """, section_name=section_name, condition=condition, filename=filename,
                        embedding=embedding, model_name=model_name, uploaded_timestamp=uploaded_timestamp,
                        chunk_id=chunk_id, coordinates=coordinates, page=page)

    def get_sections(self):
        with self.driver.session() as session:
            result = session.run("MATCH (s:Section) RETURN DISTINCT s.name AS name")
            return [record["name"] for record in result]

    def get_points_by_section(self, section_name):
        with self.driver.session() as session:
            records = session.run("""
                MATCH (f:File)-[:HAS_SECTION]->(s:Section {name: $section_name})-[:HAS_COA]->(p:COA)
                RETURN id(p) AS pid, p.condition AS condition, p.source AS source, p.embedding AS embedding, p.uploaded_timestamp AS uploaded_timestamp
            """, section_name=section_name)
            return [record.data() for record in records]

    def find_similar_points(self, section_name, similarity_threshold=0.9, min_source_ratio=0.6, max_distance=2):
        section_name_lower = section_name.lower()  # Convert the input section_name to lowercase
        with self.driver.session() as session:
            total_sources = session.run("""
                MATCH (s:Section)-[:HAS_COA]->(p:COA)
                WHERE apoc.text.distance(toLower(s.name), $section_name) <= $max_distance  // Fuzzy matching
                RETURN COUNT(DISTINCT p.source) AS total_sources
            """, section_name=section_name_lower, max_distance=max_distance).single()["total_sources"]

            min_sources_required = int(total_sources * min_source_ratio)

            result = session.run("""
                MATCH (s:Section)-[:HAS_COA]->(p:COA)
                WHERE apoc.text.distance(toLower(s.name), $section_name) <= $max_distance  // Fuzzy matching
                WITH collect(p) AS points
                UNWIND points AS p1
                UNWIND points AS p2
                WITH p1, p2
                WHERE elementId(p1) < elementId(p2)
                WITH p1, p2, gds.similarity.cosine(p1.embedding, p2.embedding) AS similarity
                WHERE similarity > $threshold
                RETURN
                    p1.condition AS condition1,
                    p2.condition AS condition2,
                    p1.source AS source1,
                    p2.source AS source2,
                    p1.uploaded_timestamp AS uploaded_timestamp1,
                    p2.uploaded_timestamp AS uploaded_timestamp2,
                    p1.coordinates AS coordinates1,
                    p2.coordinates AS coordinates2,
                    p1.chunk_id AS chunk_id1,
                    p2.chunk_id AS chunk_id2,
                    p1.page AS page1,
                    p2.page AS page2,
                    similarity
                ORDER BY similarity DESC
            """, section_name=section_name_lower, threshold=similarity_threshold, max_distance=max_distance)

            # Track all similar pairs by condition
            condition_pairs = {}
            for record in result:
                condition1, condition2 = record["condition1"], record["condition2"]
                source1, source2 = record["source1"], record["source2"]

                for cond, other_cond, src, other_src, ts, other_ts, coords, other_coords, chunk, other_chunk, page, other_page in [
                    (condition1, condition2, source1, source2,
                     record["uploaded_timestamp1"], record["uploaded_timestamp2"],
                     record["coordinates1"], record["coordinates2"],
                     record["chunk_id1"], record["chunk_id2"],
                     record["page1"], record["page2"]),
                    (condition2, condition1, source2, source1,
                     record["uploaded_timestamp2"], record["uploaded_timestamp1"],
                     record["coordinates2"], record["coordinates1"],
                     record["chunk_id2"], record["chunk_id1"],
                     record["page2"], record["page1"])
                ]:
                    if cond not in condition_pairs:
                        condition_pairs[cond] = {
                            "condition": cond,
                            "source": src,
                            "uploaded_timestamp": ts,
                            "coordinates": json.loads(coords) if coords else None,
                            "chunk_id": chunk,
                            "page": page,
                            "similar_to": {},
                            "similarities": []
                        }

                    condition_pairs[cond]["similar_to"][other_cond] = {
                        "source": other_src,
                        "uploaded_timestamp": other_ts,
                        "coordinates": json.loads(other_coords) if other_coords else None,
                        "chunk_id": other_chunk,
                        "page": other_page
                    }
                    condition_pairs[cond]["similarities"].append(record["similarity"])

            # Build groups based on source count
            filtered_groups = []
            processed_conditions = set()

            for condition_data in condition_pairs.values():
                if condition_data["condition"] in processed_conditions:
                    continue

                # Get unique sources for this condition and its similar conditions
                sources = {condition_data["source"]}
                sources.update(meta["source"] for meta in condition_data["similar_to"].values())

                if len(sources) >= min_sources_required:
                    # Create group with this condition and all its similar conditions
                    conditions = [condition_data["condition"]] + list(condition_data["similar_to"].keys())
                    metadata = []

                    # Add primary condition metadata
                    metadata.append({
                        "source": condition_data["source"],
                        "uploaded_timestamp": condition_data["uploaded_timestamp"],
                        "coordinates": condition_data["coordinates"],
                        "chunk_id": condition_data["chunk_id"],
                        "page": condition_data["page"]
                    })

                    # Add similar conditions metadata, ensuring no duplicate sources
                    added_sources = {condition_data["source"]}  # Track sources to avoid duplicates
                    for similar_cond in condition_data["similar_to"]:
                        sim_data = condition_data["similar_to"][similar_cond]
                        if sim_data["source"] not in added_sources:
                            metadata.append({
                                "source": sim_data["source"],
                                "uploaded_timestamp": sim_data["uploaded_timestamp"],
                                "coordinates": sim_data["coordinates"],
                                "chunk_id": sim_data["chunk_id"],
                                "page": sim_data["page"]
                            })
                            added_sources.add(sim_data["source"])

                    filtered_groups.append({
                        "conditions": conditions,
                        "metadata": metadata,
                        "avg_similarity": np.mean(condition_data["similarities"])
                    })

                    processed_conditions.update(conditions)

            return filtered_groups

    def find_similar_points_latest(self, section_name, similarity_threshold=0.85, min_source_ratio=0.6):
        groups = self.find_similar_points(section_name, similarity_threshold, min_source_ratio)

        latest_groups = []
        for group in groups:
            # Find latest timestamp
            latest_timestamp = max(meta["uploaded_timestamp"] for meta in group["metadata"])
            latest_conditions = [
                (cond, meta)
                for cond, meta in zip(group["conditions"], group["metadata"])
                if meta["uploaded_timestamp"] == latest_timestamp
            ]

            if latest_conditions:
                latest_condition, _ = latest_conditions[-1]  # Take last if multiple
                # Keep all metadata but only latest condition
                latest_groups.append({
                    "conditions": [latest_condition],
                    "metadata": group["metadata"],  # Keep all metadata
                    "avg_similarity": group["avg_similarity"]
                })

        return latest_groups


if __name__ == "__main__":
    neo4j_tool = Neo4jTool(uri=settings.NEO4J_URI, user=settings.NEO4J_USERNAME, password=settings.NEO4J_PASSWORD)
    results = neo4j_tool.find_similar_points_latest("environmental", similarity_threshold=0.8)
    print(results)
    print('Processing...')
    filtered_results = process_similar_coa(
        results,
        '''
Project type: Not specifically defined in the conditions
Proposed density/intensity: Not specified in the conditions
Location and access: Not specified in the conditions
Unique site characteristics: The conditions indicate potential presence of protected species habitat
Design elements: Not specified in the conditions
Integration with surrounding developments: Not specified in the conditions
Phasing considerations: The conditions mention phased development, specifically requiring Gopher Tortoise surveys for each phase
Environmental considerations:

Protected Species Requirements:

Mandatory stoppage of work if State or Federally protected plant/nesting animal species are discovered
Two-day notification requirement to Pasco County and applicable agencies upon discovery
Work can only resume after obtaining pertinent permits and written agency authorization
Specific focus on Gopher Tortoise protection with required:

Surveys following FFWCC guidelines for each development phase
Review and approval by County Biologist and FFWCC
Submission of FFWCC relocation permit and After-Action Report or incidental take permit before site development permit issuance
    ''',
        #    settings.OPENAI_API_KEY
    )

    for result in filtered_results:
        print("\nMatched conditions (Similarity: {:.3f}):".format(result['avg_similarity']))
        for condition in result["conditions"]:
            print(f"- {condition}")
        for metadata in result["metadata"]:
            print("\nMetadata:")
            print(f"Source: {metadata['source']}")
            print(f"Page: {metadata['page']}")
            print(f"Chunk ID: {metadata['chunk_id']}")
            if metadata['coordinates']:
                coords = metadata['coordinates']
                print("Coordinates:", coords)
        print("-" * 50)

    print('Finalized', filtered_results)
    print(results == filtered_results)
