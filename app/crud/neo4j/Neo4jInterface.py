from neo4j import Driver, GraphDatabase

"""
1. neo4j CRUD operations
"""


class Neo4JInterface:
    def __init__(self, uri, user, password):
        self.uri = uri
        self.user = user
        self.password = password

    def get_driver(self) -> Driver:
        return GraphDatabase.driver(self.uri, auth=(self.user, self.password))

    def get_node_count(self, node_label: str):
        with self.get_driver().session() as session:
            result = session.run(
                f"""
                    MATCH (X:{node_label})
                    RETURN count(*)
                """,
            )
            return result.single()[0]

    def get_document_count_by_county(self, county_name: str):
        with self.get_driver().session() as session:
            result = session.run(
                """
                MATCH (c:County {name: $county_name})-[:HAS]->(d:Document)
                RETURN c.total_documents AS doc_count
                """,
                county_name=county_name,
            )
            print(result, result.single())
            return result.single()[0] if result and result.single() else 0
