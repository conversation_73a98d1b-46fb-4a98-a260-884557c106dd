from typing import List, Dict, Optional, Any
from app.core.config import settings
from anthropic import Anthropic
from datetime import datetime
from neo4j import GraphDatabase
import voyageai
import json
import re
import concurrent.futures


def generate_condition_prompt(conditions, description, standard_conditions=None):
    """
    Generates a system prompt for Claude 3.5 Haiku to create conditions based on common conditions
    and project descriptions.

    Args:
        conditions (list): List of common conditions to reference
        description (str): Project description containing key details
        standard_conditions (list): List of standard Accela condition approvals to match against

    Returns:
        str: Formatted system prompt for Claude 3.5 Haiku
    """

    prompt = """You are an expert in generating precise and relevant conditions for development projects.
Your task is to identify the pattern in the provided common conditions and create a similar condition,
using only information from the project description to fill in specific details.

Guidelines:
1. Identify the main pattern and structure in the provided conditions
2. Keep the same sentence structure and flow
3. Only replace specific details using information from the project description
4. Do not add requirements beyond what's in the project description
5. **CRITICAL: When matching standard conditions, use the EXACT name as provided - never modify or create variations**
6. Format the response as a **JSON object** with the following keys:
   - `condition`: The generated condition following the given pattern and using project details
   - `trigger`: The event or timing from the project that activates the condition.
     - A trigger must **explicitly define a clear point in time** using a phrase like **"prior to," "before," or "at the time of"** a significant milestone.
     - **If no valid trigger is found in the condition, strictly return `"None"`** (Do **not** generate a trigger artificially)
   - `action_required`: The specific action needed based on project requirements
   - `responsible_party`: The entity mentioned in the project description
   - `deliverable`: The expected output based on project requirements
   - `enforcing_department`: The authority mentioned in the project description
   - `validating_department`: The reviewing entity from the project description
   - `accela_field`: The matching Accela field details from the standard conditions (if a match is found)

Flow for Extracting Actionable Conditions:
STEP 1: First, scan each condition for a specific timing trigger. Only proceed to Step 2 if the condition contains explicit timing language such as:
- "Prior to..."
- "Before..."
- "At the time of..."
- "Concurrent with..."
- "Within X days of..."
- "No later than..."

STEP 2: For conditions that passed Step 1, verify they require a concrete deliverable or measurable action, such as:
- Documents to be submitted
- Infrastructure to be constructed
- Studies to be performed
- Payments to be made
- Specific physical improvements

STEP 3: For conditions that passed Steps 1-2, make sure that the condition has an identifiable Pasco County department responsible for enforcement and validation (either explicitly stated or clearly inferable):
- An enforcing department responsible for ensuring the condition is met
- A validating department responsible for reviewing/approving the deliverable
Use these Pasco County departments:
    - Planning and Economic Growth Department (PEG)
    - Development Review Division
    - Engineering Services Department
    - Environmental Biologist/Natural Resources
    - Building Department
    - Utilities Services Branch
    - Parks and Recreation Department
    - Emergency Services Department
    - Permits and Development Information Services (PDIS)
    - Stormwater Management Division
    - Public Transportation Department (PCPT)
    - Real Estate Division
    - Mobility Fee Administration
    - Fire Rescue/Fire Marshal
- The department should be a Pasco County department and not a state or federal department.
- ** The enforcing department and validating department should be different departments.**

STEP 4: For conditions that passed Steps 1-3, confirm they require a specific one-time action, not an ongoing standard or rule.

**If any condition that fails any of the four steps, return only the Question, Question_already_generated, Word, and Point. All other fields should be "none"**

### Triggers:
✅ Extract the trigger **ONLY if it matches one of these exact phrases (word-for-word, case-insensitive):**
- **'Prior to ...'**
- **'Before approval of...'**
- **'At the time of...'**
- **'Within X days of...'**
- **'No later than...'**
- **'Concurrent with...'**

If the phrase is not an exact match, return:
"trigger": "None"

STEP 5:**Rules for Extracting the Responsible Party:**
    - The **Responsible Party** is the entity responsible for taking action on the requirement in the sentence.
    - The Responsible Party **must be explicitly mentioned in the input text**.
    - Skip this Step only if Responsible Party is not mentioned in the input text:
        -- If Responsible Party is not mentioned, then STRICTLY provide the developer's name from the project description.
        -- **If the Responsible Party is "Developer", replace it with the actual developer's name from the project description.[It should not be shown as Developer unless developer's name is not specified]**

STEP 6: **Accela Field Matching**
    - After generating the condition, find the best matching standard Accela condition from the provided list
    - Match based on:
        * Action required vs standard condition name
        * Deliverable vs standard condition short comments
        * Department matches (enforcing/validating vs standard condition type)
        * Keyword similarity (permit, plan, study, survey, etc.)
    - **CRITICAL: When a match is found, use the EXACT name from the standard condition**
    - **DO NOT modify, paraphrase, or create variations of the standard condition name**
    - **Copy the name field exactly as it appears in the standard condition list**
    - **CRITICAL: When a match is found, copy ALL the exact field values from the matched standard condition**
    - **DO NOT generate or modify any field values - use the exact values from the standard condition**
    - **Copy these fields exactly as they appear in the standard condition:**
        * statusType
        * publicDisplayMessage
        * shortComments
        * longComments
        * type (value and text)
        * group (value and text)
        * status (value and text)
        * priority (value and text) - if present
        * severity (value and text)
        * activeStatus (value and text)
        * inheritable (value and text)
        * displayNoticeInAgency
        * isIncludeNameInNotice
        * isIncludeShortCommentsInNotice
        * displayNoticeInCitizens
        * displayNoticeInCitizensFee
    - **Only update these fields with current values:**
        * statusDate: Use current date in YYYY-MM-DD HH:MM:SS format
        * appliedDate: Use current date in YYYY-MM-DD HH:MM:SS format
        * recordId: Use "REC25-00000-0002C"
    - If no good match is found, create a basic Accela field structure

## IMPORTANT
1.ENFORCING DEPARTMENT: [Specific Pasco County department that enforces - e.g., Planning and Economic Growth, Engineering Services, Utilities Services Branch, etc.]
2.VALIDATING DEPARTMENT: [Specific Pasco County department that validates completion]
3.Only include Pasco County internal departments for the enforcing and validating departments fields. External agencies like FFWCC, FDOT, SWFWMD, etc. should not be included.
4.For conditions requiring coordination with external agencies but still enforced by Pasco County, list the appropriate Pasco County department (e.g., Environmental Biologist, Natural Resources) as the enforcing/validating department, with the external agency listed in the coordination field.
5.Use specific department names rather than general 'Pasco County' designations, such as:
        1. Planning and Development Department
        2. Development Review Committee
        3. Engineering Services Department
        4. Environmental Lands Division
        5. Transportation Department
        6. Utilities Department
        7. Fire Rescue Department
        8. Building Department
        9. Emergency Services Department / Fire Rescue
        10. Parks and Recreation Department
        11. Permits and Development Information Services Department
        12. Real Estate Division
6.When inferring departments:
    - Consider the subject matter of the condition
    - Use knowledge of which department typically oversees that type of requirement
    - Select the most specific department rather than general 'Pasco County'
    - Distinguish between the department that enforces compliance versus the one that validates completion

Common Conditions to Reference:
{conditions}

Project Description:
{description}

{standard_conditions_section}

Required Output Format:
{{
    "condition": "<Generated condition using only project details>",
    "is_actionable": "<True or False>",
    "trigger": "<Clearly defined event or factor that activates the condition>",
    "action_required": "<Action based on project requirements>",
    "responsible_party": "<Use the Developer name from the description if available, otherwise 'Developer'>",
    "deliverable": "<expected output>",
    "enforcing_department": "<department ensuring compliance>",
    "validating_department": "<different department reviewing submission>",
    "is_valid_condition": "<Boolean value indicating if the condition is valid>",
    "accela_field": {{
        "statusType": "<Standard condition status type value>",
        "recordId": "REC25-00000-0002C",
        "name": "<EXACT name from standard condition - DO NOT MODIFY>",
        "publicDisplayMessage": "<Standard condition public display message>",
        "shortComments": "<Standard condition short comments>",
        "longComments": "<Standard condition long comments>",
        "statusDate": "<Current date in YYYY-MM-DD HH:MM:SS format>",
        "appliedDate": "<Current date in YYYY-MM-DD HH:MM:SS format>",
        "type": {{
            "value": "<Standard condition type value>",
            "text": "<Standard condition type text>"
        }},
        "group": {{
            "value": "<Standard condition group value>",
            "text": "<Standard condition group text>"
        }},
        "status": {{
            "value": "<Standard condition status value>",
            "text": "<Standard condition status text>"
        }},
        "priority": {{
            "value": "<Standard condition priority value>",
            "text": "<Standard condition priority text>"
        }},
        "severity": {{
            "value": "<Standard condition severity value>",
            "text": "<Standard condition severity text>"
        }},
        "activeStatus": {{
            "value": "<Standard condition active status value>",
            "text": "<Standard condition active status text>"
        }},
        "inheritable": {{
            "value": "<Standard condition inheritable value>",
            "text": "<Standard condition inheritable text>"
        }},
        "displayNoticeInAgency": true,
        "isIncludeNameInNotice": true,
        "isIncludeShortCommentsInNotice": true,
        "displayNoticeInCitizens": true,
        "displayNoticeInCitizensFee": false
    }}
}}

Important:
- Follow the exact pattern of the common conditions
- Only use details explicitly mentioned in the project description
- Keep the same sentence structure
- Do not add requirements not found in the description
- If multiple conditions show the same pattern, use that pattern
- Set is_valid_condition to false if the condition refers to something already done or completed (e.g., "from the observation", "from the plan", "from the land values", etc.)
- Set is_valid_condition to true only for conditions that require future actions or deliverables
- **CRITICAL FOR ACCELA FIELD: Use the EXACT name from the matching standard condition**
- **NEVER modify, paraphrase, or create variations of standard condition names**
- **Copy the name field exactly as it appears in the standard condition list**
- **CRITICAL: When a standard condition match is found, copy ALL field values exactly as they appear in the standard condition**
- **DO NOT generate, modify, or create any field values - use the exact values from the matched standard condition**
- **Only update statusDate and appliedDate with current date, and recordId with "REC25-00000-0002C"**
- For accela_field, use the exact structure from the matching standard condition, or create a basic structure if no match is found

Your response should only contain the JSON object with the generated condition."""

    formatted_conditions = "\n".join([f"- {cond}" for cond in conditions])

    # Add standard conditions section if provided
    standard_conditions_section = ""
    if standard_conditions:
        standard_conditions_section = """
Standard Accela Conditions to Match Against:
"""
        for i, std_cond in enumerate(standard_conditions):
            standard_conditions_section += f"""
{i + 1}. Name: {std_cond.get('name', 'N/A')}
   ID: {std_cond.get('id', 'N/A')}
   StatusType: {std_cond.get('statusType', 'N/A')}
   PublicDisplayMessage: {std_cond.get('publicDisplayMessage', 'N/A')}
   ShortComments: {std_cond.get('shortComments', 'N/A')}
   LongComments: {std_cond.get('longComments', 'N/A')}
   Type: {std_cond.get('type', {}).get('value', 'N/A')} / {std_cond.get('type', {}).get('text', 'N/A')}
   Group: {std_cond.get('group', {}).get('value', 'N/A')} / {std_cond.get('group', {}).get('text', 'N/A')}
   Status: {std_cond.get('status', {}).get('value', 'N/A')} / {std_cond.get('status', {}).get('text', 'N/A')}
   Priority: {std_cond.get('priority', {}).get('value', 'N/A')} / {std_cond.get('priority', {}).get('text', 'N/A')}
   Severity: {std_cond.get('severity', {}).get('value', 'N/A')} / {std_cond.get('severity', {}).get('text', 'N/A')}
   ActiveStatus: {std_cond.get('activeStatus', {}).get('value', 'N/A')} / {std_cond.get('activeStatus', {}).get('text', 'N/A')}
   Inheritable: {std_cond.get('inheritable', {}).get('value', 'N/A')} / {std_cond.get('inheritable', {}).get('text', 'N/A')}
   DisplayNoticeInAgency: {std_cond.get('displayNoticeInAgency', 'N/A')}
   IsIncludeNameInNotice: {std_cond.get('isIncludeNameInNotice', 'N/A')}
   IsIncludeShortCommentsInNotice: {std_cond.get('isIncludeShortCommentsInNotice', 'N/A')}
   DisplayNoticeInCitizens: {std_cond.get('displayNoticeInCitizens', 'N/A')}
   DisplayNoticeInCitizensFee: {std_cond.get('displayNoticeInCitizensFee', 'N/A')}
"""

    final_prompt = prompt.format(
        conditions=formatted_conditions,
        description=description,
        standard_conditions_section=standard_conditions_section
    )

    return final_prompt


async def extract_actionable_fields(conditions, description=None, accela_token=None):
    """
    Given a list of condition strings and a project description, generate actionable fields for each condition using Anthropic Claude (if available).
    Returns a list of dicts with keys: condition, is_actionable, extracted_data, is_valid_condition, accela_fields, etc.
    If description is None or empty, uses a fallback default description.
    Now includes standard conditions in the prompt for better matching.
    """
    import json
    import re

    results = []
    fallback_description = "No project description provided."
    if not description:
        description = fallback_description

    print(f"Processing {len(conditions)} conditions with description: {description[:100]}...")
    print(f"Accela token provided: {'Yes' if accela_token else 'No'}")

    # Get standard conditions from hardcoded list
    standard_conditions = get_standard_condition_approvals(accela_token)

    print(f"Retrieved {len(standard_conditions)} standard conditions from hardcoded list")

    # Log some sample standard conditions for debugging
    if standard_conditions:
        print("Sample standard conditions:")
        for i, std_cond in enumerate(standard_conditions[:3]):
            print(f"  {i + 1}. Name: {std_cond.get('name', 'N/A')}")
            print(f"     Type: {std_cond.get('type', {}).get('value', 'N/A')}")
            print(f"     Group: {std_cond.get('group', {}).get('value', 'N/A')}")
            print(f"     ID: {std_cond.get('id', 'N/A')}")
    else:
        print("No standard conditions available")

    # Check if Anthropic is available
    try:
        from anthropic import Anthropic
    except ImportError:
        print("Anthropic package is not installed. Using fallback values.")
        Anthropic = None

    if Anthropic is None:
        print("Anthropic package is not installed. Please install it to use this functionality.")
        for cond in conditions:
            results.append({
                "condition": cond,
                "is_actionable": True,
                "extracted_data": {},  # Empty dict instead of undefined variable
                "is_valid_condition": True,
                "accela_fields": None,  # None instead of undefined variable
                "record_id": "REC25-00000-0002C"
            })
        return results

    client = Anthropic(api_key=settings.ANTHROPIC_API_KEY)

    for cond in conditions:
        prompt = generate_condition_prompt([cond], description, standard_conditions)
        try:
            message = client.messages.create(
                model="claude-3-haiku-20240307",
                max_tokens=1024,
                temperature=0.7,
                system="You are an expert in generating development conditions. Only respond with the JSON object containing the generated condition.",
                messages=[{
                    "role": "user",
                    "content": prompt
                }]
            )
            response_text = message.content[0].text

            # Optimized extraction: Parse JSON once and extract all fields
            try:
                # Parse the entire response as JSON (most efficient approach)
                full_response = json.loads(response_text)

                # Extract all fields directly from the parsed JSON
                extracted_data = {
                    "condition": full_response.get("condition", cond),
                    "is_actionable": full_response.get("is_actionable", False),
                    "trigger": full_response.get("trigger", ""),
                    "action_required": full_response.get("action_required", ""),
                    "responsible_party": full_response.get("responsible_party", ""),
                    "deliverable": full_response.get("deliverable", ""),
                    "enforcing_department": full_response.get("enforcing_department", ""),
                    "validating_department": full_response.get("validating_department", ""),
                    "is_valid_condition": full_response.get("is_valid_condition", False),
                    "accela_field": full_response.get("accela_field")
                }

                # Ensure boolean fields are properly typed
                if isinstance(extracted_data["is_actionable"], str):
                    extracted_data["is_actionable"] = extracted_data["is_actionable"].lower() == "true"
                if isinstance(extracted_data["is_valid_condition"], str):
                    extracted_data["is_valid_condition"] = extracted_data["is_valid_condition"].lower() == "true"

                # Use the parsed data directly
                response_data = {"condition": extracted_data["condition"]}
                accela_field = extracted_data["accela_field"]

            except json.JSONDecodeError:
                # Fallback: if JSON parsing fails, use minimal regex extraction

                # Extract only essential fields with minimal regex
                extracted_data = {"condition": cond}

                # Simple regex for boolean fields
                is_actionable_match = re.search(r'"is_actionable":\s*(true|false)', response_text)
                if is_actionable_match:
                    extracted_data["is_actionable"] = is_actionable_match.group(1).lower() == "true"
                else:
                    extracted_data["is_actionable"] = False

                is_valid_match = re.search(r'"is_valid_condition":\s*(true|false)', response_text)
                if is_valid_match:
                    extracted_data["is_valid_condition"] = is_valid_match.group(1).lower() == "true"
                else:
                    extracted_data["is_valid_condition"] = False

                # Extract other fields with simple patterns
                for field in ["trigger", "action_required", "responsible_party", "deliverable",
                              "enforcing_department", "validating_department"]:
                    pattern = rf'"{field}":\s*"([^"]*)"'
                    match = re.search(pattern, response_text)
                    extracted_data[field] = match.group(1) if match else ""

                # Extract accela_field with minimal processing
                accela_field = None
                accela_start = response_text.find('"accela_field":')
                if accela_start != -1:
                    brace_start = response_text.find('{', accela_start)
                    if brace_start != -1:
                        # Quick brace counting
                        brace_count = 1
                        pos = brace_start + 1
                        while pos < len(response_text) and brace_count > 0:
                            if response_text[pos] == '{':
                                brace_count += 1
                            elif response_text[pos] == '}':
                                brace_count -= 1
                            pos += 1

                        if brace_count == 0:
                            try:
                                accela_field = json.loads(response_text[brace_start:pos])
                            except json.JSONDecodeError:
                                accela_field = None

                extracted_data["accela_field"] = accela_field
                response_data = {"condition": extracted_data.get("condition", cond)}

            # Determine is_actionable if not set
            is_actionable = extracted_data.get("is_actionable")
            if is_actionable is None:
                # Heuristic: actionable if both trigger and action_required are present
                is_actionable = bool(extracted_data.get("trigger") and extracted_data.get("action_required"))

            results.append({
                "condition": response_data.get("condition", cond),
                "is_actionable": is_actionable,
                "extracted_data": extracted_data,
                "is_valid_condition": extracted_data.get("is_valid_condition"),
                "accela_fields": [accela_field] if accela_field is not None else None,
                "record_id": "REC25-00000-0002C"
            })
        except Exception as e:
            results.append({
                "condition": cond,
                "is_actionable": None,
                "extracted_data": {},  # Empty dict instead of undefined variable
                "is_valid_condition": None,
                "accela_fields": None,  # None instead of undefined variable
                "record_id": "REC25-00000-0002C",
                "error": str(e)
            })
    return results


def get_accela_field_from_llm(extracted_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Get the accela_field from LLM response if present, otherwise return None.
    Args:
        extracted_data: The extracted condition data from LLM
    Returns:
        Accela field structure from LLM or None if not provided
    """
    return extracted_data.get("accela_field")


def extract_actionable_data_from_condition(condition_text):
    """
    Extracts key metadata from a generated condition using optimized parsing.

    Args:
        condition_text (str): The text containing the generated condition.

    Returns:
        dict: Extracted metadata fields.
    """
    # Optimized extraction: Try JSON parsing first, fallback to minimal regex
    try:
        # Parse the entire response as JSON (most efficient)
        full_response = json.loads(condition_text)

        extracted_data = {
            "condition": full_response.get("condition", ""),
            "is_actionable": full_response.get("is_actionable", False),
            "trigger": full_response.get("trigger", ""),
            "action_required": full_response.get("action_required", ""),
            "responsible_party": full_response.get("responsible_party", ""),
            "deliverable": full_response.get("deliverable", ""),
            "enforcing_department": full_response.get("enforcing_department", ""),
            "validating_department": full_response.get("validating_department", ""),
            "is_valid_condition": full_response.get("is_valid_condition", False),
            "accela_field": full_response.get("accela_field")
        }

        # Ensure boolean fields are properly typed
        if isinstance(extracted_data["is_actionable"], str):
            extracted_data["is_actionable"] = extracted_data["is_actionable"].lower() == "true"
        if isinstance(extracted_data["is_valid_condition"], str):
            extracted_data["is_valid_condition"] = extracted_data["is_valid_condition"].lower() == "true"

    except json.JSONDecodeError:
        # Fallback: minimal regex extraction for malformed JSON
        extracted_data = {"condition": ""}

        # Simple regex for boolean fields
        is_actionable_match = re.search(r'"is_actionable":\s*(true|false)', condition_text)
        extracted_data["is_actionable"] = is_actionable_match.group(1).lower() == "true" if is_actionable_match else False

        is_valid_match = re.search(r'"is_valid_condition":\s*(true|false)', condition_text)
        extracted_data["is_valid_condition"] = is_valid_match.group(1).lower() == "true" if is_valid_match else False

        # Extract other fields with simple patterns
        for field in ["trigger", "action_required", "responsible_party", "deliverable",
                      "enforcing_department", "validating_department"]:
            pattern = rf'"{field}":\s*"([^"]*)"'
            match = re.search(pattern, condition_text)
            extracted_data[field] = match.group(1) if match else ""

        # Extract accela_field with minimal processing
        accela_field = None
        accela_start = condition_text.find('"accela_field":')
        if accela_start != -1:
            brace_start = condition_text.find('{', accela_start)
            if brace_start != -1:
                # Quick brace counting
                brace_count = 1
                pos = brace_start + 1
                while pos < len(condition_text) and brace_count > 0:
                    if condition_text[pos] == '{':
                        brace_count += 1
                    elif condition_text[pos] == '}':
                        brace_count -= 1
                    pos += 1

                if brace_count == 0:
                    try:
                        accela_field = json.loads(condition_text[brace_start:pos])
                    except json.JSONDecodeError:
                        accela_field = None

        extracted_data["accela_field"] = accela_field

    return extracted_data


def generate_condition(conditions, description, standard_conditions=None):
    """
    Generates a condition using the Anthropic Claude API.

    Args:
        conditions (list): List of common conditions
        description (str): Project description
        standard_conditions (list): List of standard Accela condition approvals to match against

    Returns:
        dict: Generated condition as a JSON object
    """
    try:
        client = Anthropic(api_key=settings.ANTHROPIC_API_KEY)

        # Generate the prompt
        prompt = generate_condition_prompt(conditions, description, standard_conditions)

        # Create the message using Claude 3.5 Haiku
        message = client.messages.create(
            model="claude-3-haiku-20240307",
            max_tokens=1024,
            temperature=0.7,
            system="You are an expert in generating development conditions. Only respond with the JSON object containing the generated condition. CRITICAL: Use EXACT names and ALL field values from standard conditions - never modify, paraphrase, or generate new values. Copy everything exactly as it appears in the matched standard condition.",
            messages=[{
                "role": "user",
                "content": prompt
            }]
        )

        response_text = message.content[0].text

        # Use the optimized extraction function
        extracted_data = extract_actionable_data_from_condition(response_text)

        # Use extracted data directly instead of re-parsing
        response_data = {
            "condition": extracted_data.get("condition", ""),
            "is_actionable": extracted_data.get("is_actionable", False),
            "is_valid_condition": extracted_data.get("is_valid_condition", False),
            "extracted_data": extracted_data,
            "accela_field": extracted_data.get("accela_field")
        }

        return response_data

    except Exception as e:
        print(f"Error generating condition: {str(e)}")
        return None


def _process_single_condition(condition_item, description, standard_conditions=None):
    """Helper function to process a single condition for parallel execution"""
    new_condition = generate_condition(condition_item["contents"], description, standard_conditions)
    if new_condition:
        condition_item['conditions'] = [new_condition.get('condition', '')]
        condition_item['is_actionable'] = new_condition.get('is_actionable', False)
        condition_item['extracted_data'] = new_condition.get('extracted_data', {})
        condition_item['accela_field'] = new_condition.get('accela_field', {})

        # Properly handle is_valid_condition
        is_valid = new_condition.get('is_valid_condition')
        # If it's a string, convert to boolean
        if isinstance(is_valid, str):
            is_valid = is_valid.lower() == 'true'
        condition_item['is_valid_condition'] = is_valid

    return condition_item


def process_all_condition(data: list[Dict], description='', max_workers=None, standard_conditions=None):
    """
    Process all conditions with parallel execution.

    Args:
        data (list[Dict]): List of condition dictionaries
        description (str): Project description
        max_workers (int, optional): Maximum number of worker threads to use.
                                    Default is None (uses CPU count)
        standard_conditions (list): List of standard Accela condition approvals to match against

    Returns:
        list: List of processed valid conditions
    """
    all_processed_conditions = []

    # Use ThreadPoolExecutor for parallel API calls
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        # Submit all tasks
        future_to_condition = {
            executor.submit(_process_single_condition, condition_item, description, standard_conditions): condition_item
            for condition_item in data
        }

        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_condition):
            try:
                processed_condition = future.result()
                # Add to all processed conditions regardless of validity
                all_processed_conditions.append(processed_condition)
            except Exception as e:
                print(f"Error processing condition: {str(e)}")

    # Filter valid conditions (keeping original filtering logic)
    new_data = [condition for condition in all_processed_conditions
                if condition.get('is_valid_condition', False)]
    return new_data


class MPUDToolkit:
    def __init__(self, uri: str, username: str, password: str, voyage_api_key: str):
        """
        Initialize the predictor with Neo4j and VoyageAI credentials for MPUD analysis
        """
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        self.voyage_client = voyageai.Client(api_key=voyage_api_key)

    def create_mpud_schema(self):
        """
        Set up Neo4j schema for MPUD with updated structure including categories and sections
        """
        with self.driver.session() as session:
            # Create constraints
            session.run("""
                CREATE CONSTRAINT mpud_id IF NOT EXISTS
                FOR (m:MPUD) REQUIRE m.id IS UNIQUE
            """)

            session.run("""
                CREATE CONSTRAINT mpud_category_name IF NOT EXISTS
                FOR (mc:MPUDCategory) REQUIRE mc.name IS UNIQUE
            """)

            session.run("""
                CREATE CONSTRAINT section_id IF NOT EXISTS
                FOR (s:Section) REQUIRE s.id IS UNIQUE
            """)

            session.run("""
                CREATE CONSTRAINT condition_id IF NOT EXISTS
                FOR (c:Condition) REQUIRE c.id IS UNIQUE
            """)

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Get embeddings for text using VoyageAI
        """
        print('Extracting {} embeddings'.format(len(texts)))
        embeddings = []
        for text in texts:
            response = self.voyage_client.embed(text, model="voyage-02")
            embeddings.append(response.embeddings)
        return embeddings

    def store_mpud(self,
                   mpud_id: str,
                   mpud_category: str,
                   project_type: str,
                   sections: Dict[str, Dict[str, List[Dict[str, Any]]]],
                   metadata: Optional[Dict[str, str]] = None):
        """
        Store MPUD structure in Neo4j with better handling of invalid section names
        """
        # Filter out invalid sections (None, null, empty strings)
        valid_sections = {
            name: data for name, data in sections.items()
            if name and name.lower() != "null" and isinstance(name, str)
        }

        print(f"Original sections: {list(sections.keys())}")
        print(f"Valid sections after filtering: {list(valid_sections.keys())}")
        if not valid_sections:
            print("Warning: No valid sections found after filtering")
            return

        with self.driver.session() as session:
            # Create MPUD category node
            session.run("""
                MERGE (mc:MPUDCategory {name: $category})
            """, category=mpud_category)

            # Create MPUD node with metadata
            mpud_properties = {
                "id": mpud_id,
                "project_type": project_type
            }
            if metadata:
                mpud_properties.update(metadata)

            # Create MPUD and connect to category
            session.run("""
                MERGE (m:MPUD {id: $id})
                SET m += $properties
                WITH m
                MATCH (mc:MPUDCategory {name: $category})
                MERGE (m)-[:HAS_CATEGORY]->(mc)
            """, id=mpud_id, properties=mpud_properties, category=mpud_category)

            # Process only valid sections
            for section_name, section_data in valid_sections.items():
                try:
                    # Create section node with sanitized name
                    section_id = f"{mpud_id}_{section_name}"
                    sanitized_name = section_name.strip()

                    session.run("""
                        MERGE (s:Section {id: $section_id, name: $section_name})
                        WITH s
                        MATCH (m:MPUD {id: $mpud_id})
                        MERGE (m)-[:HAS_SECTION]->(s)
                    """, section_id=section_id, section_name=sanitized_name, mpud_id=mpud_id)

                    # Process conditions in batch
                    if 'conditions' in section_data and section_data['conditions']:
                        conditions = section_data['conditions']

                        # Validate conditions
                        valid_conditions = [
                            cond for cond in conditions
                            if isinstance(cond, dict) and 'condition' in cond
                        ]

                        if not valid_conditions:
                            print(f"No valid conditions found for section {section_name}")
                            continue

                        # Get all condition texts at once
                        condition_texts = [cond['condition'] for cond in valid_conditions]

                        # Get embeddings for all conditions in batch
                        embeddings = self.get_embeddings(condition_texts)

                        # Prepare all condition properties
                        conditions_batch = []
                        for idx, (condition_data, embedding) in enumerate(zip(valid_conditions, embeddings)):
                            condition_id = f"{section_id}_condition_{idx}"
                            embedding_str = ','.join(map(str, embedding))

                            condition_properties = {
                                "id": condition_id,
                                "content": condition_data['condition'],
                                "embedding_str": embedding_str,
                                "position": idx
                            }

                            # Add any metadata from condition
                            if 'meta_data' in condition_data:
                                condition_properties.update(condition_data['meta_data'])

                            conditions_batch.append(condition_properties)

                        if conditions_batch:
                            # Create all condition nodes
                            session.run("""
                                UNWIND $conditions_batch AS condition
                                MERGE (c:Condition {id: condition.id})
                                SET c += condition
                                WITH c, condition
                                MATCH (s:Section {id: $section_id})
                                MERGE (s)-[:HAS_CONDITION]->(c)
                            """,
                                        conditions_batch=conditions_batch,
                                        section_id=section_id
                                        )

                            # Create NEXT relationships if there are multiple conditions
                            if len(conditions_batch) > 1:
                                session.run("""
                                    MATCH (s:Section {id: $section_id})-[:HAS_CONDITION]->(c:Condition)
                                    WHERE c.position < $max_position
                                    WITH c, c.position as pos
                                    MATCH (s:Section {id: $section_id})-[:HAS_CONDITION]->(next:Condition {position: pos + 1})
                                    MERGE (c)-[:NEXT]->(next)
                                """,
                                            section_id=section_id,
                                            max_position=len(conditions_batch) - 1
                                            )

                except Exception as e:
                    print(f"Error processing section {section_name}: {str(e)}")
                    continue

    def diagnose_mpud_data(self, mpud_category: str, section_name: str):
        """
        Diagnose the data available in the database for a given category and section
        """
        with self.driver.session() as session:
            # Check total MPUDs in this category
            mpud_count = session.run("""
                MATCH (mc:MPUDCategory {name: $category})
                <-[:HAS_CATEGORY]-(m:MPUD)
                RETURN count(m) as mpud_count
            """, category=mpud_category).single()['mpud_count']

            # Check sections and conditions
            section_stats = session.run("""
                MATCH (mc:MPUDCategory {name: $category})
                <-[:HAS_CATEGORY]-(m:MPUD)
                -[:HAS_SECTION]->(s:Section {name: $section_name})
                -[:HAS_CONDITION]->(c:Condition)
                RETURN
                    count(DISTINCT m) as mpuds_with_section,
                    count(DISTINCT s) as section_count,
                    count(c) as total_conditions,
                    count(DISTINCT c.position) as unique_positions
            """, category=mpud_category, section_name=section_name).single()

            # Check NEXT relationships
            next_stats = session.run("""
                MATCH (mc:MPUDCategory {name: $category})
                <-[:HAS_CATEGORY]-(m:MPUD)
                -[:HAS_SECTION]->(s:Section {name: $section_name})
                -[:HAS_CONDITION]->(c:Condition)-[:NEXT]->(next:Condition)
                RETURN count(DISTINCT c) as conditions_with_next
            """, category=mpud_category, section_name=section_name).single()

            print(f"\nDiagnostic Results for {mpud_category} - {section_name}:")
            print(f"Total MPUDs in category: {mpud_count}")
            print(f"MPUDs with this section: {section_stats['mpuds_with_section']}")
            print(f"Total conditions: {section_stats['total_conditions']}")
            print(f"Unique positions: {section_stats['unique_positions']}")
            print(f"Conditions with NEXT relationship: {next_stats['conditions_with_next']}")

            # Sample some actual conditions
            conditions = session.run("""
                MATCH (mc:MPUDCategory {name: $category})
                <-[:HAS_CATEGORY]-(m:MPUD)
                -[:HAS_SECTION]->(s:Section {name: $section_name})
                -[:HAS_CONDITION]->(c:Condition)
                RETURN c.position as position, c.content as content
                ORDER BY c.position
                LIMIT 5
            """, category=mpud_category, section_name=section_name).data()

            print("\nSample conditions:")
            for cond in conditions:
                print(f"Position {cond['position']}: {cond['content'][:100]}...")

    def predict_next_conditions(
        self,
        previous_conditions: List[str],
        section_name: str,
        mpud_category: Optional[str] = None,
        project_type: Optional[str] = None,
        filter_dict: Optional[Dict[str, Any]] = None,
        next_k: int = 5,
        similarity_threshold: float = 0.6,
        random_state: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Predict or recommend the next set of conditions for a given section.
        """
        def sanitize_text(text: str) -> str:
            if not text:
                return ""
            return text.replace("'", "\\'").replace('"', '\\"')

        filter_dict = filter_dict or {}

        with self.driver.session() as session:
            if not previous_conditions:
                try:
                    # Base query
                    query = """
                    MATCH (m:MPUD)-[:HAS_CATEGORY]->(mc:MPUDCategory),
                        (m)-[:HAS_SECTION]->(s:Section),
                        (s)-[:HAS_CONDITION]->(c:Condition)
                    WHERE true
                    """

                    # Add project type condition if specified
                    if project_type:
                        query += f"\nAND m.project_type IS NOT NULL AND toLower(m.project_type) CONTAINS toLower('{sanitize_text(project_type)}')"

                    # Add section name condition
                    query += f"\nAND s.name IS NOT NULL AND toLower(s.name) CONTAINS toLower('{sanitize_text(section_name)}')"

                    # Add match score calculation based on metadata fields
                    query += """
                    WITH m, s, c,
                        CASE WHEN $random_state IS NOT NULL THEN rand() ELSE 1 END as random_score,
                        0 as initial_score
                    """

                    # Add score for each metadata field
                    for field, value in filter_dict.items():
                        if value:
                            clean_value = sanitize_text(value.lower())
                            query += f"""
                            WITH m, s, c, random_score,
                                initial_score + CASE
                                    WHEN m.{field} IS NOT NULL AND
                                        toLower(m.{field}) CONTAINS '{clean_value}'
                                    THEN 1
                                    ELSE 0
                                END as initial_score
                            """

                    # Add final ordering and return
                    query += """
                    WITH c, initial_score, random_score
                    ORDER BY
                        CASE WHEN $random_state IS NOT NULL
                            THEN random_score
                            ELSE initial_score
                        END DESC
                    LIMIT $k
                    RETURN
                        apoc.map.removeKey(c, 'embedding_str') AS condition_data,
                        initial_score as match_score,
                        c.mpud_id as mpud_id
                    """

                    print("Executing query:", query)

                    result = session.run(query, {
                        "random_state": random_state,
                        "k": next_k
                    })

                    return [
                        dict(
                            record["condition_data"],
                            match_score=record["match_score"],
                            mpud_id=record["mpud_id"]
                        )
                        for record in result
                    ]

                except Exception as e:
                    print(f"Query execution error in metadata matching: {str(e)}")
                    print(f"Generated query: {query}")
                    return []

            else:
                # Embedding-based similarity search
                try:
                    last_condition = previous_conditions[-1]
                    embedding = self.get_embeddings([last_condition])[0]
                    embedding_str = ','.join(map(str, embedding))

                    query = """
                    MATCH (m:MPUD)-[:HAS_CATEGORY]->(mc:MPUDCategory),
                        (m)-[:HAS_SECTION]->(s:Section),
                        (s)-[:HAS_CONDITION]->(c:Condition)
                    WHERE
                        c.embedding_str IS NOT NULL
                        AND s.name IS NOT NULL
                        AND toLower(s.name) CONTAINS toLower($section_name)
                    """

                    if project_type:
                        query += "\nAND m.project_type IS NOT NULL AND toLower(m.project_type) CONTAINS toLower($project_type)"

                    if mpud_category:
                        query += "\nAND mc.name IS NOT NULL AND toLower(mc.name) CONTAINS toLower($category)"

                    query += """
                    WITH m, s, c,
                        [x IN split($embedding, ',') | toFloat(x)] AS query_vec,
                        [x IN split(c.embedding_str, ',') | toFloat(x)] AS cond_vec
                    WHERE gds.similarity.cosine(query_vec, cond_vec) >= $threshold
                    WITH c,
                        gds.similarity.cosine(query_vec, cond_vec) AS similarity_score,
                        CASE WHEN $random_state IS NOT NULL THEN rand() ELSE 0 END as random_score
                    ORDER BY
                        CASE WHEN $random_state IS NOT NULL
                            THEN random_score
                            ELSE similarity_score
                        END DESC
                    LIMIT $k
                    RETURN
                        apoc.map.removeKey(c, 'embedding_str') AS condition_data,
                        similarity_score,
                        c.mpud_id as mpud_id
                    """

                    params = {
                        "section_name": section_name,
                        "project_type": project_type,
                        "category": mpud_category,
                        "embedding": embedding_str,
                        "threshold": similarity_threshold,
                        "random_state": random_state,
                        "k": next_k
                    }

                    result = session.run(query, params)
                    return [
                        dict(
                            record["condition_data"],
                            similarity_score=record["similarity_score"],
                            mpud_id=record["mpud_id"]
                        )
                        for record in result
                    ]

                except Exception as e:
                    print(f"Query execution error in similarity search: {str(e)}")
                    return []

    def get_section_conditions(self, mpud_id: str, section_name: str) -> List[Dict[str, Any]]:
        """
        Retrieve all conditions for a specific section of an MPUD
        """
        with self.driver.session() as session:
            conditions = session.run("""
                MATCH (m:MPUD {id: $mpud_id})
                -[:HAS_SECTION]->(s:Section {name: $section_name})
                -[:HAS_CONDITION]->(c:Condition)
                RETURN c.content as content, c.position as position,
                       c.meta_data as meta_data
                ORDER BY c.position
            """, mpud_id=mpud_id, section_name=section_name).data()

            return conditions

    def find_similar_conditions(self, section_name: str, similarity_threshold: float = 0.7, min_file_group: int = 3, limit: int = 10):
        """
        Find similar conditions across different files within sections that match the given name
        """
        with self.driver.session() as session:
            query = """
            MATCH (m:MPUD)-[HAS_SECTION]->(s:Section)-[HAS_CONDITION]->(c:Condition)
            WHERE apoc.text.sorensenDiceSimilarity(toLower(s.name), toLower($section_name)) > $similarity_threshold

            // First, collect and normalize all conditions
            WITH COLLECT(DISTINCT {
                content: c.content,
                normalized_content: apoc.text.clean(c.content),
                page: c.page,
                file_name: c.file_name,
                coordinates: c.coordinates,
                source: COALESCE(c.file_path, c.file_name),
                chunk_id: c.chunk_id,
                section: s.name
            }) AS conditions

            // Group by normalized content first to avoid duplicates
            WITH conditions,
                apoc.map.groupByMulti(conditions, 'normalized_content') AS content_groups

            // Process each group once
            UNWIND keys(content_groups) AS normalized_content
            WITH normalized_content, content_groups[normalized_content] AS group_conditions

            // For each group, find similar conditions
            WITH group_conditions[0].content AS base_content,
                [x IN group_conditions | {
                    page: x.page,
                    content: x.content,
                    normalized_content: x.normalized_content,
                    similarity: CASE WHEN x.content = group_conditions[0].content THEN 1.0
                                ELSE apoc.text.sorensenDiceSimilarity(x.content, group_conditions[0].content) END,
                    source: x.source,
                    coordinates: x.coordinates,
                    chunk_id: x.chunk_id,
                    section: x.section
                }] AS similar_conditions

            // Only keep groups with minimum required sources
            WITH base_content, similar_conditions
            WHERE size(apoc.coll.toSet([x IN similar_conditions | x.source])) >= $min_file_group

            // Return results
            RETURN
                base_content,
                similar_conditions
            ORDER BY size(similar_conditions) DESC
            LIMIT $limit
            """

            result = session.run(
                query,
                section_name=section_name,
                similarity_threshold=similarity_threshold,
                min_file_group=min_file_group,
                limit=limit
            )

            formatted_results = []
            for record in result:
                similar_conditions = record["similar_conditions"]

                # Use dictionary to keep only first occurrence per source
                source_dict = {}
                contents = []
                for condition in similar_conditions:
                    source = condition["source"]
                    if source not in source_dict:
                        try:
                            # Parse coordinates JSON string to dict
                            coordinates = json.loads(condition["coordinates"])
                        except BaseException:
                            coordinates = {
                                "Width": 0,
                                "Height": 0,
                                "Left": 0,
                                "Top": 0
                            }

                        metadata_obj = {
                            "source": source,
                            "coordinates": [coordinates],  # Put in list as per required format
                            "chunk_id": condition["chunk_id"],
                            "page": condition["page"],
                        }
                        source_dict[source] = metadata_obj
                        contents.append(condition["content"])

                formatted_results.append({
                    "conditions": [record["base_content"]],
                    "metadata": list(source_dict.values()),
                    "contents": contents
                })

            return formatted_results

    def close(self):
        """
        Close Neo4j connection
        """
        self.driver.close()


async def common_points_format_change(data, header):
    result = {
        "message": {
            "modifications": [
                {
                    "modified_node": {"Points": []},
                    "new_child_nodes": None,
                    "modified_children": None,
                    "modified_siblings": None,
                    "modified_similar_nodes": None,
                    "new_relationships": None,
                    "modified_relationships": None,
                    "new_relationship_types": None,
                    "created_at": datetime.utcnow().isoformat(),
                }
            ],
            "discussion_type": "layout_" + header,
        },
        "chat_id": "111",
    }

    points = []
    # Iterate through the provided data chunks
    for chunk in data:
        # Extract relevant data for each chunk
        chunk_id = chunk["_id"]
        data = chunk["text"]["data"]
        is_actionable = chunk["is_actionable"]
        extracted_data = chunk["extracted_data"]

        if 'common_points' in chunk:
            meta_lis = []
            for _meta in chunk['meta_data']:
                meta = {}  # Create new dictionary for each iteration
                meta['file_name'] = _meta['source']
                meta['coordinates'] = _meta['coordinates'][0] if isinstance(_meta['coordinates'], list) else _meta['coordinates']
                meta['page_no'] = _meta['page']
                meta_lis.append(meta)

            points.append({"point": data, "chunk_id": chunk_id, "meta_data": meta_lis, "is_actionable": is_actionable, "extracted_data": extracted_data})
        else:
            meta_data = {}  # Reset meta_data for non-common points
            coordinates = (
                chunk["text"]["coordinates"][0]
                if isinstance(chunk["text"]["coordinates"], list)
                else chunk["text"]["coordinates"]
            )

            meta_data["file_name"] = chunk["file_name"]
            meta_data["coordinates"] = coordinates
            meta_data["page_no"] = chunk["text"]["page"]
            points.append({"point": data, "chunk_id": chunk_id, "meta_data": [meta_data], "is_actionable": is_actionable, "extracted_data": extracted_data})

    result["message"]["modifications"][0]["modified_node"]["Points"] = points
    yield result


def init_predictor(settings):
    """
    Initialize the MPUDToolkit with settings
    """
    return MPUDToolkit(
        uri=settings.NEO4J_URI,
        username=settings.NEO4J_USERNAME,
        password=settings.NEO4J_PASSWORD,
        voyage_api_key=settings.VOYAGE_API_KEY
    )


# Hardcoded standard conditions based on Accela API response
STANDARD_CONDITIONS = [
    {
        "statusDate": "2025-07-24 00:00:00",
        "publicDisplayMessage": "Attach a copy of the owner / officer / partner or member¿s drivers license or identification card to your application. The address on the drivers license or ID card must be the same as the home based business address.",
        "id": 3,
        "name": "Driver's License",
        "shortComments": "This requirement is met by submitting a copy of the driver's license or identification card.",
        "statusType": "Applied",
        "appliedDate": "2025-07-24 00:00:00",
        "type": {
            "value": "License Required Documents",
            "text": "License Required Documents"
        },
        "group": {
            "value": "General",
            "text": "General"
        },
        "status": {
            "value": "Pending",
            "text": "Pending"
        },
        "priority": {
            "value": "1",
            "text": "1"
        },
        "severity": {
            "value": "Required",
            "text": "Required"
        },
        "activeStatus": {
            "value": "A",
            "text": "Active"
        },
        "inheritable": {
            "value": "N",
            "text": "No"
        },
        "displayNoticeInAgency": True,
        "isIncludeNameInNotice": True,
        "isIncludeShortCommentsInNotice": True,
        "displayNoticeInCitizens": True,
        "displayNoticeInCitizensFee": True
    },
    {
        "statusDate": "2025-07-24 00:00:00",
        "id": 12,
        "name": "Gen - Obtain Permits",
        "shortComments": "The applicant shall obtain all necessary building permits prior to commencing construction.",
        "longComments": "The applicant shall obtain all necessary building permits prior to commencing construction.",
        "statusType": "Applied",
        "appliedDate": "2025-07-24 00:00:00",
        "type": {
            "value": "Planning COA",
            "text": "Planning COA"
        },
        "group": {
            "value": "General",
            "text": "General"
        },
        "status": {
            "value": "Pending",
            "text": "Pending"
        },
        "severity": {
            "value": "Notice",
            "text": "Notice"
        },
        "activeStatus": {
            "value": "A",
            "text": "Active"
        },
        "inheritable": {
            "value": "N",
            "text": "No"
        },
        "displayNoticeInAgency": True,
        "isIncludeNameInNotice": True,
        "isIncludeShortCommentsInNotice": True,
        "displayNoticeInCitizens": True,
        "displayNoticeInCitizensFee": False
    },
    {
        "statusDate": "2025-07-24 00:00:00",
        "id": 13,
        "name": "Ext - No Other Exterior Work",
        "shortComments": "No other exterior work is allowed. All other notes and drawings on the final plans as submitted by the applicant are deemed conditions of approval.",
        "longComments": "No other exterior work is allowed. All other notes and drawings on the final plans as submitted by the applicant are deemed conditions of approval.",
        "statusType": "Applied",
        "appliedDate": "2025-07-24 00:00:00",
        "type": {
            "value": "Planning COA",
            "text": "Planning COA"
        },
        "group": {
            "value": "General",
            "text": "General"
        },
        "status": {
            "value": "Pending",
            "text": "Pending"
        },
        "severity": {
            "value": "Notice",
            "text": "Notice"
        },
        "activeStatus": {
            "value": "A",
            "text": "Active"
        },
        "inheritable": {
            "value": "N",
            "text": "No"
        },
        "displayNoticeInAgency": True,
        "isIncludeNameInNotice": True,
        "isIncludeShortCommentsInNotice": True,
        "displayNoticeInCitizens": True,
        "displayNoticeInCitizensFee": False
    },
    {
        "statusDate": "2025-07-24 00:00:00",
        "id": 14,
        "name": "Plans - No Changes to Plans",
        "shortComments": "Any additional changes, additions, or modifications shall require Planning review and approval.",
        "longComments": "All other notes and drawings on the final plans as submitted by the applicant are deemed conditions of approval.  Any work that differs from the final set of plans stamped by Planning staff shall be subject to review and approval prior to issuance of a building permit or work undertaken.",
        "statusType": "Applied",
        "appliedDate": "2025-07-24 00:00:00",
        "type": {
            "value": "Planning COA",
            "text": "Planning COA"
        },
        "group": {
            "value": "General",
            "text": "General"
        },
        "status": {
            "value": "Pending",
            "text": "Pending"
        },
        "severity": {
            "value": "Notice",
            "text": "Notice"
        },
        "activeStatus": {
            "value": "A",
            "text": "Active"
        },
        "inheritable": {
            "value": "N",
            "text": "No"
        },
        "displayNoticeInAgency": True,
        "isIncludeNameInNotice": True,
        "isIncludeShortCommentsInNotice": True,
        "displayNoticeInCitizens": True,
        "displayNoticeInCitizensFee": False
    },
    {
        "statusDate": "2025-07-24 00:00:00",
        "id": 15,
        "name": "Plans - Conform to Approved Plans",
        "shortComments": "The project shall substantially conform to the approved plans as shown on the attached exhibits (including building colors and materials).",
        "longComments": "The project shall substantially conform to the approved plans as shown on the attached exhibits (including building colors and materials).",
        "statusType": "Applied",
        "appliedDate": "2025-07-24 00:00:00",
        "type": {
            "value": "Planning COA",
            "text": "Planning COA"
        },
        "group": {
            "value": "General",
            "text": "General"
        },
        "status": {
            "value": "Pending",
            "text": "Pending"
        },
        "severity": {
            "value": "Notice",
            "text": "Notice"
        },
        "activeStatus": {
            "value": "A",
            "text": "Active"
        },
        "inheritable": {
            "value": "N",
            "text": "No"
        },
        "displayNoticeInAgency": True,
        "isIncludeNameInNotice": True,
        "isIncludeShortCommentsInNotice": True,
        "displayNoticeInCitizens": True,
        "displayNoticeInCitizensFee": False
    },
    {
        "statusDate": "2025-07-24 00:00:00",
        "id": 16,
        "name": "Gen - Match In-Kind",
        "shortComments": "Match original in-kind shall mean matching materials, design, dimensions, profiles, placement and finishes.",
        "longComments": "Match original in-kind shall mean matching materials, design, dimensions, profiles, placement and finishes.",
        "statusType": "Applied",
        "appliedDate": "2025-07-24 00:00:00",
        "type": {
            "value": "Planning COA",
            "text": "Planning COA"
        },
        "group": {
            "value": "General",
            "text": "General"
        },
        "status": {
            "value": "Pending",
            "text": "Pending"
        },
        "severity": {
            "value": "Notice",
            "text": "Notice"
        },
        "activeStatus": {
            "value": "A",
            "text": "Active"
        },
        "inheritable": {
            "value": "N",
            "text": "No"
        },
        "displayNoticeInAgency": True,
        "isIncludeNameInNotice": True,
        "isIncludeShortCommentsInNotice": True,
        "displayNoticeInCitizens": True,
        "displayNoticeInCitizensFee": False
    },
    {
        "statusDate": "2025-07-24 00:00:00",
        "id": 17,
        "name": "As Built Plans",
        "shortComments": "As-Built Plans are required for all construction that has been altered from approved plans during the construction phase.",
        "longComments": "As-Built Plans are required for all construction that has been altered from approved plans during the construction phase",
        "statusType": "Applied",
        "appliedDate": "2025-07-24 00:00:00",
        "type": {
            "value": "Building Permit",
            "text": "Building Permit"
        },
        "group": {
            "value": "General",
            "text": "General"
        },
        "status": {
            "value": "Pending",
            "text": "Pending"
        },
        "severity": {
            "value": "Notice",
            "text": "Notice"
        },
        "activeStatus": {
            "value": "A",
            "text": "Active"
        },
        "inheritable": {
            "value": "N",
            "text": "No"
        },
        "displayNoticeInAgency": True,
        "isIncludeNameInNotice": True,
        "isIncludeShortCommentsInNotice": True,
        "displayNoticeInCitizens": True,
        "displayNoticeInCitizensFee": False
    },
    {
        "statusDate": "2025-07-24 00:00:00",
        "id": 19601,
        "name": "Utility Easements",
        "shortComments": "The order of abandonment shall provide public utility easements...",
        "longComments": "The order of abandonment shall provide public utility easements for existing utilities, unless the utilities are relocated to the approval of the servicing utility company prior  to issuing the order of abandonment.",
        "statusType": "Applied",
        "appliedDate": "2025-07-24 00:00:00",
        "type": {
            "value": "Planning COA",
            "text": "Planning COA"
        },
        "group": {
            "value": "General",
            "text": "General"
        },
        "status": {
            "value": "Pending",
            "text": "Pending"
        },
        "severity": {
            "value": "Notice",
            "text": "Notice"
        },
        "activeStatus": {
            "value": "A",
            "text": "Active"
        },
        "inheritable": {
            "value": "N",
            "text": "No"
        },
        "displayNoticeInAgency": True,
        "isIncludeNameInNotice": False,
        "isIncludeShortCommentsInNotice": False,
        "displayNoticeInCitizens": True,
        "displayNoticeInCitizensFee": False
    },
    {
        "statusDate": "2025-07-24 00:00:00",
        "id": 22323,
        "name": "Submit a detailed landscape plan",
        "shortComments": "null",
        "statusType": "Applied",
        "appliedDate": "2025-07-24 00:00:00",
        "type": {
            "value": "Planning COA",
            "text": "Planning COA"
        },
        "group": {
            "value": "General",
            "text": "General"
        },
        "status": {
            "value": "Pending",
            "text": "Pending"
        },
        "severity": {
            "value": "Notice",
            "text": "Notice"
        },
        "activeStatus": {
            "value": "A",
            "text": "Active"
        },
        "inheritable": {
            "value": "N",
            "text": "No"
        },
        "displayNoticeInAgency": True,
        "isIncludeNameInNotice": False,
        "isIncludeShortCommentsInNotice": False,
        "displayNoticeInCitizens": True,
        "displayNoticeInCitizensFee": False
    }
]


def get_standard_condition_approvals(accela_token: str = None) -> List[Dict[str, Any]]:
    """
    Get standard condition approvals from hardcoded list instead of API call.
    The accela_token parameter is kept for backward compatibility but is no longer used.
    """
    return STANDARD_CONDITIONS

    def get_section_conditions(self, mpud_id: str, section_name: str) -> List[Dict[str, Any]]:
        """
        Retrieve all conditions for a specific section of an MPUD
        """
        with self.driver.session() as session:
            conditions = session.run("""
                MATCH (m:MPUD {id: $mpud_id})
                -[:HAS_SECTION]->(s:Section {name: $section_name})
                -[:HAS_CONDITION]->(c:Condition)
                RETURN c.content as content, c.position as position,
                       c.meta_data as meta_data
                ORDER BY c.position
            """, mpud_id=mpud_id, section_name=section_name).data()

            return conditions

    def find_similar_conditions(self, section_name: str, similarity_threshold: float = 0.7, min_file_group: int = 3, limit: int = 10):
        """
        Find similar conditions across different files within sections that match the given name
        """
        with self.driver.session() as session:
            query = """
            MATCH (m:MPUD)-[HAS_SECTION]->(s:Section)-[HAS_CONDITION]->(c:Condition)
            WHERE apoc.text.sorensenDiceSimilarity(toLower(s.name), toLower($section_name)) > $similarity_threshold

            // First, collect and normalize all conditions
            WITH COLLECT(DISTINCT {
                content: c.content,
                normalized_content: apoc.text.clean(c.content),
                page: c.page,
                file_name: c.file_name,
                coordinates: c.coordinates,
                source: COALESCE(c.file_path, c.file_name),
                chunk_id: c.chunk_id,
                section: s.name
            }) AS conditions

            // Group by normalized content first to avoid duplicates
            WITH conditions,
                apoc.map.groupByMulti(conditions, 'normalized_content') AS content_groups

            // Process each group once
            UNWIND keys(content_groups) AS normalized_content
            WITH normalized_content, content_groups[normalized_content] AS group_conditions

            // For each group, find similar conditions
            WITH group_conditions[0].content AS base_content,
                [x IN group_conditions | {
                    page: x.page,
                    content: x.content,
                    normalized_content: x.normalized_content,
                    similarity: CASE WHEN x.content = group_conditions[0].content THEN 1.0
                                ELSE apoc.text.sorensenDiceSimilarity(x.content, group_conditions[0].content) END,
                    source: x.source,
                    coordinates: x.coordinates,
                    chunk_id: x.chunk_id,
                    section: x.section
                }] AS similar_conditions

            // Only keep groups with minimum required sources
            WITH base_content, similar_conditions
            WHERE size(apoc.coll.toSet([x IN similar_conditions | x.source])) >= $min_file_group

            // Return results
            RETURN
                base_content,
                similar_conditions
            ORDER BY size(similar_conditions) DESC
            LIMIT $limit
            """

            result = session.run(
                query,
                section_name=section_name,
                similarity_threshold=similarity_threshold,
                min_file_group=min_file_group,
                limit=limit
            )

            formatted_results = []
            for record in result:
                similar_conditions = record["similar_conditions"]

                # Use dictionary to keep only first occurrence per source
                source_dict = {}
                contents = []
                for condition in similar_conditions:
                    source = condition["source"]
                    if source not in source_dict:
                        try:
                            # Parse coordinates JSON string to dict
                            coordinates = json.loads(condition["coordinates"])
                        except BaseException:
                            coordinates = {
                                "Width": 0,
                                "Height": 0,
                                "Left": 0,
                                "Top": 0
                            }

                        metadata_obj = {
                            "source": source,
                            "coordinates": [coordinates],  # Put in list as per required format
                            "chunk_id": condition["chunk_id"],
                            "page": condition["page"],
                        }
                        source_dict[source] = metadata_obj
                        contents.append(condition["content"])

                formatted_results.append({
                    "conditions": [record["base_content"]],
                    "metadata": list(source_dict.values()),
                    "contents": contents
                })

