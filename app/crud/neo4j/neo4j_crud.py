from neo4j import GraphDatabase
from app.crud.neo4j.schema import Node
from app.core.config import settings

uri = settings.NEO4J_URI
username = settings.NEO4J_USERNAME
password = settings.NEO4J_PASSWORD

driver = GraphDatabase.driver(uri, auth=(username, password))


def escape_value(val):
    return val.replace("(", "").replace(")", "").replace('"', "'")


def add_nodes(nodes):
    def create_nodes(tx, nodes):
        for node in nodes:
            tx.run(node.to_cypher())

    with driver.session() as session:
        session.write_transaction(create_nodes, nodes)


def update_node_prop(node, prop, val):
    def update_node(tx, node, prop, val):
        tx.run(node.to_update_prop(prop, val))

    with driver.session() as session:
        session.write_transaction(update_node, node, prop, val)


def inc_node_prop(node, prop):
    def update_node(tx, node, prop):
        tx.run(
            node.to_inc_prop(
                prop, get_one_node_data(label=node.label, props=node.properties),
            ),
        )

    with driver.session() as session:
        session.write_transaction(update_node, node, prop)


def add_relationship(relationships):
    def create_relationship(tx, relationships):
        for rel in relationships:
            tx.run(rel.to_cypher())

    with driver.session() as session:
        session.write_transaction(create_relationship, relationships)


def get_one_node_data(label, props={}) -> Node:
    property = ", ".join(
        f'{k}: "{escape_value(v) if isinstance(v, str) else v}"'
        for k, v in props.items()
        if k == "name"
    )
    property = f" {{{property}}}" if property else ""
    query = f"""
        MATCH (n:{label} {property})
        RETURN labels(n) AS label, n AS properties;
        """
    with driver.session() as session:
        result = session.run(query)
        for record in result:
            return Node(record["label"][0], record["properties"]._properties)

        return None


def get_last_node(node, type):
    props = ", ".join(
        f'{k}: "{escape_value(v) if isinstance(v, str) else v}"'
        for k, v in node.properties.items()
        if k == "name"
    )
    props = f" {{{props}}}" if props else ""
    query = f"""
        MATCH (d:{node.label} {props})-[r]->(related:{type})
        RETURN related
        ORDER BY related.sequence DESC
        LIMIT 1
    """
    with driver.session() as session:
        result = session.run(query)
        for record in result:
            related_node = record["related"]
            if not related_node:
                return None
            label = next(iter(related_node.labels), None)
            return Node(label, related_node._properties)
        return None
