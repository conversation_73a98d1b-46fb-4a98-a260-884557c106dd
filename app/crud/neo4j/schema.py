class Node:
    @staticmethod
    def __get_desc_data():
        with open("app/crud/neo4j/desc.json") as file:
            import json

            data = json.load(file)
        return data

    def __init__(self, label, properties):
        self.label = label
        self.properties = properties
        self.__attach_desc(Node.__get_desc_data())

    def __attach_desc(self, desc_node_map):
        if self.properties:
            self.properties["description"] = desc_node_map[self.label]["desc"]
            self.properties["level"] = desc_node_map[self.label]["level"]
        else:
            self.properties = {"description": desc_node_map[self.label]["desc"]}
            self.properties["level"] = desc_node_map[self.label]["level"]

    def to_cypher(self):
        def escape_value(val):
            return val.replace("(", "").replace(")", "").replace('"', "'")

        props = ", ".join(
            f"{k}: toInteger({v})"
            if isinstance(v, int)
            else f'{k}: "{escape_value(v)}"'
            if isinstance(v, str)
            else f'{k}: "{v}"'
            for k, v in self.properties.items()
        )
        return f"MERGE (:{self.label} {{{props}}})"

    def to_update_prop(self, prop, val):
        props = ", ".join(
            f'{k}: "{v}"' for k, v in self.properties.items() if k == "name"
        )
        props = f" {{{props}}}" if props else ""
        self.properties[prop] = val
        return f"""
        MATCH (n:{self.label} {props})
        SET n.{prop} = {val}
        """

    def to_inc_prop(self, prop, latest_node):
        props = ", ".join(
            f'{k}: "{v}"' for k, v in self.properties.items() if k == "name"
        )
        props = f" {{{props}}}" if props else ""
        latest_node.properties[prop] = self.properties.get(prop, 0) + 1
        return f"""
        MATCH (n:{self.label} {props})
        SET n.{prop} = toInteger({latest_node.properties[prop]})
        """


class Relationship:
    def __init__(self, node1, node2, rel_type, properties=None):
        self.node1 = node1
        self.node2 = node2
        self.rel_type = rel_type
        self.properties = properties or {}

    def to_cypher(self):
        def escape_value(val):
            return val.replace("(", "").replace(")", "").replace('"', "'")

        props = ", ".join(f'{k}: "{v}"' for k, v in self.properties.items())
        props = f" {{{props}}}" if props else ""
        return f"""
        MATCH (a:{self.node1.label} {{name: "{self.node1.properties['name']}"}}),
              (b:{self.node2.label} {{name: "{self.node2.properties['name']}"}})
        MERGE (a)-[:{self.rel_type}{props}]->(b)
        """
