from app.database.database_storage import Database


class BaseRepository(object):
    __slots__ = ["collection"]

    def __init__(self, table_name):
        self.collection: str = table_name

    def create(self, element, db: Database):
        """Create a new element in the repository."""
        try:
            insert_id = db.insert(collection_name=self.collection, data=element)
            return insert_id

        except Exception as e:
            return e

    def get_all(self, db: Database):
        """Get all elements"""
        try:
            return db.query(
                collection_name=self.collection,
                limit=5,
                output_fields=["text", "page", "fileName"],
            )

        except Exception as e:
            return {"message": e}

    def query(self, output_fieldss, expression, db: Database):
        """get a new element from querry."""
        try:
            fetched_rec = db.query(collection_name=self.collection, filter=expression, limit=1, output_fields=output_fieldss)
            return fetched_rec

        except Exception as e:
            # print("except", e)
            return {"message": e}

    def upsert(self, input_data, db: Database):
        """upsert element from querry."""
        try:
            upsert_rec = db.upsert(collection_name=self.collection, data=input_data)
            return upsert_rec

        except Exception as e:
            # print("except", e)
            return {"message": e}

