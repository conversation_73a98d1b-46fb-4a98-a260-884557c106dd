import pymilvus


milvus_fields = {
    "default": [
        pymilvus.FieldSchema(
            name="id", dtype=pymilvus.DataType.INT64, is_primary=True, auto_id=True,
        ),
        pymilvus.FieldSchema(
            name="fileName", dtype=pymilvus.DataType.VARCHAR, max_length=256,
        ),
        pymilvus.FieldSchema(
            name="vector", dtype=pymilvus.DataType.FLOAT_VECTOR, dim=1536,
        ),
        pymilvus.FieldSchema(
            name="page", dtype=pymilvus.DataType.VARCHAR, max_length=4,
        ),
        pymilvus.FieldSchema(
            name="text", dtype=pymilvus.DataType.VARCHAR, max_length=25600,
        ),
        pymilvus.FieldSchema(
            name="chunkId", dtype=pymilvus.DataType.VARCHAR, max_length=4,
        ),
        pymilvus.FieldSchema(
            name="documentId", dtype=pymilvus.DataType.VARCHAR, max_length=60,
        ),
    ],
    "version_data": [
        pymilvus.FieldSchema(
            name="id", dtype=pymilvus.DataType.VARCHAR, is_primary=True, max_length=60,
        ),
        pymilvus.FieldSchema(name="mpud_history", dtype=pymilvus.DataType.JSON),
        pymilvus.FieldSchema(
            name="vector", dtype=pymilvus.DataType.FLOAT_VECTOR, dim=1536,
        ),
        pymilvus.FieldSchema(
            name="mpud_history_id", dtype=pymilvus.DataType.VARCHAR, max_length=60,
        ),
        pymilvus.FieldSchema(
            name="user_id", dtype=pymilvus.DataType.VARCHAR, max_length=60,
        ),
        pymilvus.FieldSchema(
            name="county_name", dtype=pymilvus.DataType.VARCHAR, max_length=60,
        ),
    ],
    "document_metadata": [
        pymilvus.FieldSchema(
            name="id", dtype=pymilvus.DataType.INT64, is_primary=True, auto_id=True,
        ),
        pymilvus.FieldSchema(
            name="vector", dtype=pymilvus.DataType.FLOAT_VECTOR, dim=1536,
        ),
        pymilvus.FieldSchema(
            name="documentId", dtype=pymilvus.DataType.VARCHAR, max_length=60,
        ),
    ],
}

default = [
    pymilvus.FieldSchema(
        name="id", dtype=pymilvus.DataType.INT64, is_primary=True, auto_id=True,
    ),
    pymilvus.FieldSchema(
        name="fileName", dtype=pymilvus.DataType.VARCHAR, max_length=256,
    ),
    pymilvus.FieldSchema(name="vector", dtype=pymilvus.DataType.FLOAT_VECTOR, dim=1536),
    pymilvus.FieldSchema(name="page", dtype=pymilvus.DataType.VARCHAR, max_length=4),
    pymilvus.FieldSchema(
        name="text", dtype=pymilvus.DataType.VARCHAR, max_length=25600,
    ),
    pymilvus.FieldSchema(name="chunkId", dtype=pymilvus.DataType.VARCHAR, max_length=4),
]

version_data_fields = [
    pymilvus.FieldSchema(
        name="id", dtype=pymilvus.DataType.INT64, is_primary=True, auto_id=True,
    ),
    pymilvus.FieldSchema(
        name="mpudHistory", dtype=pymilvus.DataType.JSON, max_length=256,
    ),
    pymilvus.FieldSchema(
        name="vector", dtype=pymilvus.DataType.FLOAT_VECTOR, dim=1536,
    ),
]
