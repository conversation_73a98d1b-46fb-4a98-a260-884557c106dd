from pymilvus import (
    connections,
    utility,
    Collection,
    CollectionSchema,
    FieldSchema,
    DataType
)
import numpy as np
from typing import List, Tuple, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MilvusStore:
    def __init__(
        self,
        collection_name: str = "embeddings_store",
        dim: int = 1536,
        host: str = "localhost",
        port: str = "19530"
    ):
        """Initialize Milvus vector store."""
        self.collection_name = collection_name
        self.dim = dim

        try:
            # Connect to Milvus server
            if not connections.has_connection("default"):
                connections.connect("default", host=host, port=port)
                logger.info(f"Connected to Milvus at {host}:{port}")

            # Create collection if it doesn't exist
            if not utility.has_collection(self.collection_name):
                self._create_collection()

            # Get collection instance
            self.collection = Collection(self.collection_name)
            self.collection.load()
            logger.info(f"Collection loaded: {collection_name}")

            # Log collection info
            num_entities = self.collection.num_entities
            logger.info(f"Collection size: {num_entities} entities")

        except Exception as e:
            logger.error(f"Error initializing MilvusStore: {str(e)}")
            raise

    def _create_collection(self):
        """Create new collection with schema."""
        try:
            fields = [
                FieldSchema(
                    name="id",
                    dtype=DataType.VARCHAR,
                    max_length=100,
                    is_primary=True
                ),
                FieldSchema(
                    name="fileid",  # Added fileid field
                    dtype=DataType.VARCHAR,
                    max_length=1000
                ),
                FieldSchema(
                    name="section",
                    dtype=DataType.VARCHAR,
                    max_length=100
                ),
                FieldSchema(
                    name="embedding",
                    dtype=DataType.FLOAT_VECTOR,
                    dim=self.dim
                )
            ]

            schema = CollectionSchema(
                fields=fields,
                description=f"Collection for {self.collection_name}"
            )

            collection = Collection(
                name=self.collection_name,
                schema=schema
            )

            index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 1024}
            }
            collection.create_index(
                field_name="embedding",
                index_params=index_params
            )

            logger.info(f"Created collection: {self.collection_name}")

        except Exception as e:
            logger.error(f"Error creating collection: {str(e)}")
            raise

    def _normalize_vector(self, vector: List[float]) -> List[float]:
        """Normalize vector for cosine similarity."""
        try:
            vector_np = np.array(vector, dtype=np.float32)
            norm = np.linalg.norm(vector_np)
            if norm == 0:
                return vector
            return (vector_np / norm).tolist()
        except Exception as e:
            logger.error(f"Error normalizing vector: {str(e)}")
            raise

    def upsert(self, embedding: List[float], id: str, fileid: str, section: str):
        """Insert or update embedding vector."""
        try:
            if len(embedding) != self.dim:
                raise ValueError(f"Expected vector dimension {self.dim}, got {len(embedding)}")

            normalized_embedding = self._normalize_vector(embedding)
            self.collection.delete(f"id in ['{id}']")

            entities = [{
                "id": id,
                "fileid": fileid,
                "section": section,
                "embedding": normalized_embedding
            }]

            self.collection.insert(entities)
            self.collection.flush()
            logger.info(f"Upserted vector with id: {id}, fileid: {fileid}, section: {section}")

        except Exception as e:
            logger.error(f"Error upserting vector: {str(e)}")
            raise

    def batch_upsert(self, embeddings: List[List[float]], ids: List[str], fileids: List[str], sections: List[str], batch_size: int = 1000):
        """Batch insert or update embedding vectors."""
        try:
            if not (len(embeddings) == len(ids) == len(fileids) == len(sections)):
                raise ValueError("Length mismatch between inputs")

            if any(len(emb) != self.dim for emb in embeddings):
                raise ValueError(f"All embeddings must have dimension {self.dim}")

            for i in range(0, len(embeddings), batch_size):
                batch_embeddings = embeddings[i:i + batch_size]
                batch_ids = ids[i:i + batch_size]
                batch_fileids = fileids[i:i + batch_size]
                batch_sections = sections[i:i + batch_size]

                normalized_embeddings = [self._normalize_vector(emb) for emb in batch_embeddings]
                id_list = [f"'{id}'" for id in batch_ids]
                self.collection.delete(f"id in [{','.join(id_list)}]")

                entities = [{
                    "id": id,
                    "fileid": fileid,
                    "section": section,
                    "embedding": emb
                } for id, fileid, section, emb in zip(batch_ids, batch_fileids, batch_sections, normalized_embeddings)]

                self.collection.insert(entities)
                self.collection.flush()
                logger.info(f"Batch upserted vectors {i} to {i + len(batch_embeddings)}")

        except Exception as e:
            logger.error(f"Error in batch upserting vectors: {str(e)}")
            raise

    def batch_get(
        self,
        embeddings: List[List[float]],
        sections: Optional[List[str]] = None,
        top_k: int = 5,
        threshold: float = 0.7,
        batch_size: int = 50
    ) -> List[List[Tuple[str, str, str, float]]]:  # Updated return type to include fileid
        """Batch get similar vectors by matching sections."""
        try:
            if any(len(emb) != self.dim for emb in embeddings):
                raise ValueError(f"All embeddings must have dimension {self.dim}")

            if sections and len(sections) != len(embeddings):
                raise ValueError("Length of sections must match embeddings")

            all_results = []
            for i in range(0, len(embeddings), batch_size):
                batch_embeddings = embeddings[i:i + batch_size]
                batch_sections = sections[i:i + batch_size] if sections else [None] * len(batch_embeddings)
                batch_results = []

                for emb, section in zip(batch_embeddings, batch_sections):
                    search_params = {"metric_type": "COSINE", "params": {"nprobe": 16}}
                    expr = f"section == '{section}'" if section else None

                    results = self.collection.search(
                        data=[emb],
                        anns_field="embedding",
                        param=search_params,
                        limit=top_k,
                        expr=expr,
                        output_fields=["section", "fileid"]  # Added fileid to output
                    )

                    query_results = []
                    for hits in results:
                        for hit in hits:
                            similarity = float(hit.score) if hasattr(hit, 'score') else 1 - float(hit.distance)
                            if similarity >= threshold:
                                query_results.append(
                                    (str(hit.id), str(hit.entity.fileid), str(hit.entity.section), similarity)
                                )

                    batch_results.append(query_results)

                all_results.extend(batch_results)
                logger.info(f"Processed batch {i // batch_size + 1}/{(len(embeddings) + batch_size - 1) // batch_size}")

            return all_results
        except Exception as e:
            logger.error(f"Error in batch searching vectors: {str(e)}")
            raise

    def get(self, embedding: List[float], section: Optional[str] = None, top_k: int = 5, threshold: float = 0.8) -> List[Tuple[str, str, str, float]]:
        """Get similar vectors above threshold, optionally filtered by section."""
        try:
            if len(embedding) != self.dim:
                raise ValueError(f"Expected vector dimension {self.dim}, got {len(embedding)}")

            normalized_embedding = self._normalize_vector(embedding)

            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10}
            }

            expr = f"section == '{section}'" if section else None

            results = self.collection.search(
                data=[normalized_embedding],
                anns_field="embedding",
                param=search_params,
                limit=top_k,
                expr=expr,
                output_fields=["section", "fileid"]  # Added fileid
            )

            filtered_results = []
            for hits in results:
                for hit in hits:
                    try:
                        similarity = float(hit.score)
                        if similarity >= threshold:
                            filtered_results.append(
                                (str(hit.id), str(hit.entity.fileid), str(hit.entity.section), similarity)
                            )
                    except AttributeError:
                        distance = float(hit.distance)
                        similarity = 1 - distance
                        if similarity >= threshold:
                            filtered_results.append(
                                (str(hit.id), str(hit.entity.fileid), str(hit.entity.section), similarity)
                            )

            logger.info(f"Found {len(filtered_results)} results above threshold {threshold}")
            return filtered_results

        except Exception as e:
            logger.error(f"Error searching vectors: {str(e)}")
            raise

    def close(self):
        """Release collection resources."""
        try:
            if hasattr(self, 'collection'):
                self.collection.release()
            logger.info(f"Released collection: {self.collection_name}")
        except Exception as e:
            logger.error(f"Error releasing collection: {str(e)}")
            raise


def main():
    """Example usage of MilvusStore with section field."""
    try:
        # Initialize store
        store = MilvusStore(
            collection_name="test_collection",
            dim=4
        )

        # Example vectors with sections
        vectors = [
            ([1.0, 2.0, 3.0, 4.0], "test_id_1", "section_A"),
            ([2.0, 3.0, 4.0, 5.0], "test_id_2", "section_B"),
            ([3.0, 4.0, 5.0, 6.0], "test_id_3", "section_A")
        ]

        # Insert vectors
        for vector, id, section in vectors:
            store.upsert(vector, id, section)

        # Give some time for the inserts to be processed
        import time
        time.sleep(1)

        # Search with debug output

        # Test search with section filter
        query_vector = [1.0, 2.0, 3.0, 4.0]

        print("\nResults with section_A filter:")
        results_a = store.get(query_vector, section="section_A", top_k=5)
        for id, section, similarity in results_a:
            print(f"ID: {id}, Section: {section}, Similarity: {similarity}")

        print("\nResults with section_B filter:")
        results_b = store.get(query_vector, section="section_B", top_k=5)
        for id, section, similarity in results_b:
            print(f"ID: {id}, Section: {section}, Similarity: {similarity}")

        print("\nResults without section filter:")
        results_all = store.get(query_vector, top_k=5)
        for id, section, similarity in results_all:
            print(f"ID: {id}, Section: {section}, Similarity: {similarity}")

        # Collection info
        print("\nCollection info:")
        print(f"Row count: {store.collection.num_entities}")

        # Cleanup
        store.close()

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        raise


if __name__ == "__main__":
    main()
