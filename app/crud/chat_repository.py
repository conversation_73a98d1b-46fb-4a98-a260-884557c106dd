from dataclasses import dataclass
from app.database.base_graph_db import BaseGraphDB
import uuid
import json
from app.crud.mongo.base_repository import BaseRepository as MongoRepo
from app.crud.application_repository import ApplicationRepository
from app.crud.layout_repository import LayoutRepository
from app.crud.conditions_of_approval_repository import ConditionsOfApprovalRepository
from datetime import datetime
from app.database.database_storage import get_mongo_db_client
from app.database.neo4j_db import Neo4jDB
from functools import lru_cache

_CHUNK_REPO = MongoRepo("text_chunks")
_APPLICATION_REPO = ApplicationRepository
_LAYOUT_REPO = LayoutRepository
_CONDITION_REPO = ConditionsOfApprovalRepository


@dataclass
class ChatRepository:
    user: dict
    db: BaseGraphDB
    mongo_client = get_mongo_db_client()
    mongo_db = mongo_client["lightrag"]
    text_chunks = mongo_db["text_chunks"]
    # Class level cache for chunk metadata
    _chunk_metadata_cache = {}
    # Class level Neo4j driver
    _neo4j_driver = Neo4jDB().driver

    @lru_cache(maxsize=1000)
    def _get_chunk_metadata(self, chunk_id):
        """Cache individual chunk metadata"""
        if chunk_id in self._chunk_metadata_cache:
            return self._chunk_metadata_cache[chunk_id]

        with self._neo4j_driver.session() as session:
            result = session.run(
                """
                MATCH (n:Condition)
                WHERE n.chunk_id = $chunk_id
                RETURN n.chunk_id as _id, n.file_name as file_name, n.page as page,
                       n.coordinates as coordinates, n.content as content
                """,
                chunk_id=chunk_id,
            )
            record = result.single()
            if record:
                metadata = {
                    "_id": record["_id"],
                    "file_name": record["file_name"],
                    "text": {
                        "coordinates": record["coordinates"],
                        "data": record["content"],
                        "page": record["page"],
                    },
                }
                self._chunk_metadata_cache[chunk_id] = metadata
                return metadata
            return None

    def _get_chunks_metadata_batch(self, chunk_ids):
        """Get metadata for multiple chunks in one query"""
        # First check cache
        results = []
        missing_chunks = []
        for chunk_id in chunk_ids:
            if chunk_id in self._chunk_metadata_cache:
                results.append(self._chunk_metadata_cache[chunk_id])
            else:
                missing_chunks.append(chunk_id)

        if missing_chunks:
            with self._neo4j_driver.session() as session:
                result = session.run(
                    """
                    MATCH (n:Condition)
                    WHERE n.chunk_id IN $chunk_ids
                    RETURN n.chunk_id as _id, n.file_name as file_name, n.page as page,
                           n.coordinates as coordinates, n.content as content
                    """,
                    chunk_ids=missing_chunks,
                )

                for record in result:
                    metadata = {
                        "_id": record["_id"],
                        "file_name": record["file_name"],
                        "text": {
                            "coordinates": record["coordinates"],
                            "data": record["content"],
                            "page": record["page"],
                        },
                    }
                    self._chunk_metadata_cache[record["_id"]] = metadata
                    results.append(metadata)

        return results

    def _create_conditions_of_approval_node(self, data):
        application_id = str(uuid.uuid4())
        application = self.db.create_node(
            "conditions_of_approval", {**data, "id": application_id}
        )
        return application

    def format_chat_history(self, message):
        # Validate required fields
        if not isinstance(message, dict):
            return None

        created_at = message.get("created_at")
        content = message.get("content")
        role = message.get("role")

        # Skip invalid messages
        if not all([created_at, content, role]):
            return None

        if created_at in self.uniques:
            return None

        self.uniques.add(created_at)

        formatted_message = {
            "created_at": created_at,
            "discussion_type": message.get("discussion_type", ""),
        }

        content_obj = {"message": content}
        if role == "user":
            formatted_message["human"] = content_obj
        elif role == "assistant":
            formatted_message["ai"] = content_obj

        return formatted_message

    def get_chat_details(self, chat_id, limit=100):
        node = self.db.get_one_by_id(properties={"id": chat_id})
        if not node:
            raise ValueError("Invalid chat Id")
        child_chat_nodes = self.db.get_nodes_with_parent(
            "Discussion", "DiscussionRoot", "HAS_CHILD", {"id": node["id"]}
        )
        child_chat_nodes.sort(key=lambda x: x["step"])
        formatted_history = []
        for chat_node in child_chat_nodes:
            history = chat_node.get("discussion_so_far", "[]")
            history = json.loads(history)
            self.uniques = set()
            formatted_history += [
                i for i in map(self.format_chat_history, history[2:][:limit]) if i
            ]
            formatted_history.sort(
                key=lambda x: datetime.fromisoformat(x["created_at"])
            )
            self.uniques = set()
        return {"id": chat_id, "history": formatted_history}

    def add_source_to_conditions(self, value):
        chunk_d = self.chunks_data[value["chunk_id"]]

        return {
            "condition": value["point"],
            "chunk_id": value["chunk_id"],
            "meta_data": {
                "file_name": chunk_d.get("file_name", ""),
                "coordinates": {
                    "Width": chunk_d.get("text", {})
                    .get("coordinates", {})
                    .get("Width", 0),
                    "Height": chunk_d.get("text", {})
                    .get("coordinates", {})
                    .get("Height", 0),
                    "Left": chunk_d.get("text", {})
                    .get("coordinates", {})
                    .get("Left", 0),
                    "Top": chunk_d.get("text", {}).get("coordinates", {}).get("Top", 0),
                },
                "page_no": chunk_d.get("text", {}).get("page", ""),
            },
        }

    def source_detail_from_chunk(self, points):
        try:
            # Collect all chunk IDs first
            chunk_id_list = []
            for pts in points:
                if "chunk_id" in pts and pts["chunk_id"] not in chunk_id_list:
                    chunk_id_list.append(pts["chunk_id"])

            # Get metadata for all chunks in one batch
            mongo_resp = self._get_chunks_metadata_batch(chunk_id_list)

            # Process the metadata
            map = {}
            for rec in mongo_resp:
                coordinates = (
                    rec["text"]["coordinates"][0]
                    if isinstance(rec["text"]["coordinates"], list)
                    else rec["text"]["coordinates"]
                )
                if rec["_id"] not in map:
                    map[rec["_id"]] = {"meta_data": []}
                map[rec["_id"]]["meta_data"].append(
                    {
                        "file_name": rec["file_name"],
                        "coordinates": coordinates,
                        "page_no": rec["text"]["page"],
                    }
                )

            # Attach metadata to points
            for pts in points:
                if "chunk_id" in pts and pts["chunk_id"] in map:
                    if "meta_data" not in pts:
                        pts["meta_data"] = []
                    pts["meta_data"].extend(map[pts["chunk_id"]].get("meta_data", []))

            return points
        except Exception as e:
            print("ERROR in source_detail_from_chunk:", str(e))
            import traceback

            traceback.print_exc()
            return points

    def get_chat_data(self, chat_id, mongo_db):
        # Fetch the DiscussionRoot node using chat_id
        node = self.db.get_one_by_id(properties={"id": chat_id})
        section_available = mongo_db["text_chunks"].distinct("header.data", {})
        if not node:
            raise ValueError("Invalid chat Id")

        # Fetch all Discussion nodes under the DiscussionRoot
        child_chat_nodes = self.db.get_nodes_with_parent(
            "Discussion", "DiscussionRoot", "HAS_CHILD", {"id": node["id"]}
        )

        # Sort discussions by 'step' in ascending order
        child_chat_nodes.sort(key=lambda x: x["step"])

        # Initialize mpud_info from root node data
        mpud_info = {}

        # Check if root node has stored mpud_info (from MPUD extraction)
        if "mpud_info" in node:
            try:
                stored_mpud_info = json.loads(node["mpud_info"])
                mpud_info.update(stored_mpud_info)
            except (json.JSONDecodeError, TypeError):
                # If it's not valid JSON, treat as raw data
                mpud_info["raw_mpud_info"] = node["mpud_info"]

        # Add other relevant fields from root node
        root_fields_to_include = ["extracted_at", "filename", "asset_id"]
        for field in root_fields_to_include:
            if field in node:
                mpud_info[field] = node[field]

        conditions = {k: [] for k in section_available}
        processed_points = {}  # Track points by discussion type

        # Process each chat node
        for chat_node in child_chat_nodes:
            modifications = json.loads(chat_node.get("modifications", "{}"))
            modified_node = modifications.get("modified_node", {})
            common_points = json.loads(chat_node.get("common_points", "{}"))

            # Get discussion type and remove 'layout_' prefix if present
            discussion_type = chat_node.get("discussion_type", "").lower()
            if discussion_type.startswith("layout_"):
                discussion_type = discussion_type[7:]

            # Initialize dictionary for this discussion type if not exists
            if discussion_type not in processed_points:
                processed_points[discussion_type] = {}

            # Process points from common_points
            if common_points:
                for item in common_points[0]["message"]["modifications"]:
                    points = item["modified_node"]["Points"]
                    # Add source details to points
                    points = self.source_detail_from_chunk(points)
                    for point in points:
                        point_text = point["point"]
                        if point_text not in processed_points[discussion_type]:
                            processed_points[discussion_type][point_text] = {
                                "point": point_text,
                                "chunk_id": point.get("chunk_id", ""),
                                "meta_data": point.get("meta_data", []),
                                "is_actionable": point.get("is_actionable", False),
                                "extracted_data": point.get("extracted_data", {}),
                            }
                        # Merge metadata
                        if "meta_data" in point:
                            meta_data = point["meta_data"]
                            if isinstance(meta_data, list):
                                processed_points[discussion_type][point_text][
                                    "meta_data"
                                ].extend(meta_data)
                            elif meta_data:
                                processed_points[discussion_type][point_text][
                                    "meta_data"
                                ].append(meta_data)

            # Add points from modified_node
            if "Points" in modified_node:
                points = modified_node["Points"]
                # Add source details to points
                points = self.source_detail_from_chunk(points)
                for point in points:
                    point_text = point["point"]
                    if point_text not in processed_points[discussion_type]:
                        processed_points[discussion_type][point_text] = {
                            "point": point_text,
                            "chunk_id": point.get("chunk_id", ""),
                            "meta_data": point.get("meta_data", []),
                            "is_actionable": point.get("is_actionable", False),
                            "extracted_data": point.get("extracted_data", {}),
                            "accela_fields": point.get("accela_fields", {}),
                            "trigger": point.get("trigger"),
                            "action_required": point.get("action_required"),
                            "responsible_party": point.get("responsible_party"),
                            "deliverable": point.get("deliverable"),
                            "enforcing_department": point.get("enforcing_department"),
                            "validating_department": point.get("validating_department"),
                            "is_valid_condition": point.get("is_valid_condition", False),
                        }
                    # Merge metadata
                    if "meta_data" in point:
                        meta_data = point["meta_data"]
                        if isinstance(meta_data, list):
                            processed_points[discussion_type][point_text][
                                "meta_data"
                            ].extend(meta_data)
                        elif meta_data:
                            processed_points[discussion_type][point_text][
                                "meta_data"
                            ].append(meta_data)

            # Process non-Points fields for mpud_info
            mpud_fields = {
                k.lower(): v for k, v in modified_node.items() if k != "Points"
            }
            mpud_info.update(mpud_fields)

        # Convert processed points to final format and assign to correct categories
        for discussion_type, points in processed_points.items():
            if discussion_type in conditions:
                # Remove duplicate metadata for each point
                for point in points.values():
                    if "meta_data" in point:
                        seen = set()
                        unique_metadata = []
                        for meta in point["meta_data"]:
                            meta_str = json.dumps(meta, sort_keys=True)
                            if meta_str not in seen:
                                seen.add(meta_str)
                                unique_metadata.append(meta)
                        point["meta_data"] = unique_metadata

                # Assign points to correct category
                conditions[discussion_type] = list(points.values())

        # Also check for conditions stored directly on root node (fallback)
        if "conditions" in node and not any(conditions.values()):
            try:
                stored_conditions = json.loads(node["conditions"])
                if isinstance(stored_conditions, dict):
                    for section_name, section_conditions in stored_conditions.items():
                        # Ensure the section exists in our conditions dict
                        if section_name not in conditions:
                            conditions[section_name] = []
                        # Add conditions if the section is currently empty
                        if not conditions[section_name] and section_conditions:
                            conditions[section_name] = section_conditions
            except (json.JSONDecodeError, TypeError):
                # If parsing fails, store as raw data for debugging
                mpud_info["raw_conditions"] = node["conditions"]

        return {
            "mpud_info": mpud_info,
            "conditions": conditions,
        }

    def update_chat(self, chat_id, data):
        """
        Update chat using node ID (integer)
        """
        query = """
        MATCH (n)
        WHERE ID(n) = toInteger($chat_id)
        SET n += $data
        RETURN n, ID(n) as nodeid
        """
        result = self.db.execute_query(
            query, properties={"chat_id": chat_id, "data": data}
        )
        if not result:
            return None

        # Add nodeid to chat properties, following project repository pattern
        result[0]["n"]["nodeid"] = result[0]["nodeid"]
        return result[0]["n"]

    def get_assets(self, chat_id):
        """
        Get both assets and document_ids for a chat using node ID (integer)
        Returns empty list if either assets or document_ids are not found
        """
        query = """
        MATCH (n)
        WHERE ID(n) = toInteger($chat_id)
        RETURN n.assets, n.document_ids
        """
        result = self.db.execute_query(query, properties={"chat_id": chat_id})

        if not result:
            return []

        # Set empty string if assets or document_ids are None
        if result[0]["n.assets"] is None:
            result[0]["n.assets"] = "[]"
        if result[0]["n.document_ids"] is None:
            result[0]["n.document_ids"] = "[]"

        return result

    def update_discussion_so_far(self, chat_id, assets_content):
        """
        Update discussion_so_far field in Discussion node to include assets,
        replacing any existing asset content. Creates discussion if not found.
        """
        # First verify chat exists
        verify_query = """
        MATCH (root:DiscussionRoot)
        WHERE ID(root) = toInteger($chat_id)
        RETURN root
        """

        result = self.db.execute_query(verify_query, properties={"chat_id": chat_id})

        if not result:
            raise ValueError(f"Chat with ID {chat_id} not found")

        # Now check for discussion node
        discussion_query = """
        MATCH (root:DiscussionRoot)-[:HAS_CHILD]->(d:Discussion)
        WHERE ID(root) = toInteger($chat_id)
        RETURN d.discussion_so_far as discussion_so_far
        """

        discussion_result = self.db.execute_query(discussion_query, properties={"chat_id": chat_id})

        # If no discussion node or empty discussion_so_far, create initial structure
        if not discussion_result or not discussion_result[0].get("discussion_so_far"):
            # Create a new discussion node if needed
            if not discussion_result:
                create_discussion = """
                MATCH (root:DiscussionRoot)
                WHERE ID(root) = toInteger($chat_id)
                CREATE (root)-[:HAS_CHILD]->(d:Discussion {id: $discussion_id, step: 1})
                RETURN d
                """
                self.db.execute_query(
                    create_discussion,
                    properties={
                        "chat_id": chat_id,
                        "discussion_id": str(uuid.uuid4())
                    }
                )

            # Initialize empty discussion with assets
            initial_discussion = [
                {
                    "role": "system",
                    "content": f"<assets>\n{assets_content}\n</assets>"
                }
            ]

            # Update with initial discussion content
            update_query = """
            MATCH (root:DiscussionRoot)-[:HAS_CHILD]->(d:Discussion)
            WHERE ID(root) = toInteger($chat_id)
            SET d.discussion_so_far = $updated_discussion
            """

            self.db.execute_query(
                update_query,
                properties={
                    "chat_id": chat_id,
                    "updated_discussion": json.dumps(initial_discussion)
                }
            )

            return initial_discussion

        # Normal flow for existing discussion
        discussion_list = json.loads(discussion_result[0]["discussion_so_far"])

        # Update assets content in each message
        for item in discussion_list:
            if item.get("role") in ["user", "system"] and item.get("content"):
                content = item["content"]
                if "<assets>" in content and "</assets>" in content:
                    # Find everything between <assets> tags
                    start = content.find("<assets>")
                    end = content.find("</assets>") + len("</assets>")

                    # Replace content between tags
                    item["content"] = (
                        content[:start]
                        + "\n <assets> \n"
                        + assets_content
                        + "\n </assets> \n"
                        + content[end:]
                    )

        # Convert back to JSON string
        updated_discussion = json.dumps(discussion_list)

        # Update node
        update_query = """
        MATCH (root:DiscussionRoot)-[:HAS_CHILD]->(d:Discussion)
        WHERE ID(root) = toInteger($chat_id)
        SET d.discussion_so_far = $updated_discussion
        """

        self.db.execute_query(
            update_query,
            properties={
                "chat_id": chat_id,
                "updated_discussion": updated_discussion
            }
        )

        return discussion_list
