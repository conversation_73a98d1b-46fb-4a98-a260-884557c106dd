from app.crud.neo4j.schema import Node, Relationship
from app.crud.neo4j.neo4j_crud import (
    add_nodes,
    add_relationship,
    get_last_node,
)
import json
import uuid
import traceback

already_picked = set()


def is_related(prev_node, cur_node):
    prev_coordinates = prev_node.properties["coordinates"]
    cur_coordinates = cur_node.properties["coordinates"]
    if isinstance(prev_coordinates, str):
        prev_coordinates = prev_coordinates.replace("'", '"')
        prev_coordinates = json.loads(prev_coordinates)
    prev_node_right = prev_coordinates["Left"] + prev_coordinates["Width"]
    prev_node_bottom = prev_coordinates["Top"] + prev_coordinates["Height"]
    cur_node_left = cur_coordinates["Left"]
    cur_node_top = cur_coordinates["Top"]
    return (
        cur_node_left < 0.25
        and cur_node_top < 0.15
        and prev_node_bottom > 0.8
        and prev_node_right > 0.8
    )


def get_title_header_text(prev_title, page_data: list, document: Node) -> dict:
    total_len = len(page_data)
    unlabeled = Node(
        "UNLABELED",
        {"name": file__name, "file_name": file__name, "id": str(uuid.uuid4())},
    )
    add_nodes([unlabeled])
    add_relationship([Relationship(document, unlabeled, "CONTAINS_UNLABELED_TITLE")])
    i = 0
    text_index = 0
    title_index = 0
    header_index = 0
    while i < total_len:
        if "LAYOUT_TITLE" in page_data[i].keys() or prev_title:
            if (
                "LAYOUT_TITLE" in page_data[i].keys()
                and page_data[i].get("LAYOUT_TITLE", "") not in already_picked
            ):
                prev_title = Node(
                    "TITLE",
                    {
                        "name": page_data[i].get("LAYOUT_TITLE", "").replace("\n", " ").replace('\\', '').replace('"', "'"),
                        "data": page_data[i].get("LAYOUT_TITLE", "").replace("\n", " ").replace('\\', '').replace('"', "'"),
                        "coordinates": page_data[i]["Geometry"]["BoundingBox"],
                        "sequence": int(title_index),
                        "file_name": file__name,
                        "page": int(page_data[i].get("Page", "")),
                    },
                )
                text_index = 0
                title_index += 1
                header_index = 0
                add_nodes(nodes=[prev_title])
                add_relationship([Relationship(document, prev_title, "CONTAINS_TITLE")])
                already_picked.add(page_data[i].get("LAYOUT_TITLE", ""))
            i += 1
            header = None
            while i < total_len and (
                "LAYOUT_TITLE" not in page_data[i].keys()
                or page_data[i].get("LAYOUT_TITLE", "") in already_picked
            ):
                if "LAYOUT_SECTION_HEADER" in page_data[i]:
                    header = Node(
                        "HEADER",
                        {
                            "name": page_data[i]
                            .get("LAYOUT_SECTION_HEADER", "")
                            .replace("\n", " ").replace('\\', '').replace('"', "'"),
                            "data": page_data[i]
                            .get("LAYOUT_SECTION_HEADER", "")
                            .replace("\n", " ").replace('\\', '').replace('"', "'"),
                            "coordinates": page_data[i]["Geometry"]["BoundingBox"],
                            "sequence": int(header_index),
                            "file_name": file__name,
                            "page": int(page_data[i].get("Page", "")),
                        },
                    )
                    header_index += 1
                    text_index = 0
                    add_nodes(nodes=[header])
                    add_relationship(
                        [Relationship(prev_title, header, "CONTAINS_HEADER")],
                    )
                elif "LAYOUT_TEXT" in page_data[i]:
                    text = Node(
                        "TEXT",
                        {
                            "name": f'{uuid.uuid4()}-text-{text_index}-{page_data[i].get("Page", "")}',
                            "file_name": file__name,
                            "data": page_data[i].get("LAYOUT_TEXT", ""),
                            "sequence": int(text_index),
                            "coordinates": page_data[i]["Geometry"]["BoundingBox"],
                            "page": int(page_data[i].get("Page", "")),
                        },
                    )
                    text_parent = header if header else prev_title
                    last_node = get_last_node(node=text_parent, type="TEXT")
                    if (
                        not last_node
                        or last_node.properties["page"] == text.properties["page"]
                        or is_related(last_node, text)
                    ):
                        add_nodes([text])
                        add_relationship(
                            [
                                Relationship(
                                    header if header else prev_title,
                                    text,
                                    "CONTAINS_TEXT",
                                ),
                            ],
                        )
                        text_index += 1
                    else:
                        text_index = 0
                        header_index = 0
                        prev_title = unlabeled
                i += 1
        else:
            i += 1
    return prev_title


last_title = None
file__name = None


def do_create_chunk_from_page(layout_data: list, document: Node, file_name: str):
    global last_title, file__name
    file__name = file_name
    last_title = get_title_header_text(last_title, layout_data, document)
    file__name = file_name


def convert_document_to_title_header_text(document_name, textract_resp, file_name):
    try:
        document = Node("DOCUMENT", {"name": document_name.replace('\\', '').replace('"', "'")})
        do_create_chunk_from_page(textract_resp, document, file_name)
    except Exception as e:
        print(f'error : convert_document_to_title_header_text : {file_name} - {document_name}', e, "".join(traceback.format_exc()))
