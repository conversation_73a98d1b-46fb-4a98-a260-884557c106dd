from app.crud.ocr.config import AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION
import boto3
import time
import traceback
from datetime import datetime
from app.crud.mongo.base_repository import BaseRepository as MongoRepo
from app.utils.base_utils import (
    create_embedding_of_document,
    create_embedding_of_metadata,
)
from docx import Document as DocxDocument
from io import BytesIO
import re
import mimetypes
from app.core.open_ai import OpenAI
from app.core.config import settings
from bson import ObjectId
from app.database.database_storage import get_mongo_db
from app.models.document import Document, UploadStatus
from app.core.telemetry import get_logger

logger = get_logger("pasco.ai.connector")

_MONGO_REPO = MongoRepo("files_data")
_File_Status_Collection = MongoRepo("files_status")
DOCUMENT_DETAILS_COLLECTION = MongoRepo("documents")
CHAT_DETAILS = MongoRepo("chat_history")


class Textract:
    def __init__(self) -> None:
        self.key_id = AWS_ACCESS_KEY_ID
        self.key = AWS_SECRET_ACCESS_KEY
        self.region = AWS_REGION
        self.textract = boto3.client(
            "textract",
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION,
        )
        self.s3Client = boto3.client(
            "s3",
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION,
        )

    def tokens(self, token, job_id):
        resp = self.textract.get_document_text_detection(
            JobId=job_id,
            NextToken=token,
        )
        return resp

    def get_job_status(self, job_id):
        response = self.textract.get_document_text_detection(
            JobId=job_id,
        )
        return response['JobStatus'], response

    def read_word_document(self, file_path, document_name):
        try:
            document = DocxDocument(file_path)
            json_data = {}

            current_page = []
            page_number = 1

            for para in document.paragraphs:
                if para.text.strip():  # Ensure the paragraph is not empty
                    current_page.append(para.text)

                # Check for a manual page break (section break)
                if para._element.xpath('.//w:sectPr'):
                    # Store the current page
                    json_data[str(page_number)] = "\n".join(current_page)
                    current_page = []  # Reset for the next page
                    page_number += 1

            # Append the last page if there are remaining paragraphs
            if current_page:
                json_data[str(page_number)] = "\n".join(current_page)

            json_data['fileName'] = document_name
            return json_data
        except Exception as e:
            logger.error(f'Exception word: {e}')
            return None

    def extract_pdf(self, bucket_name: str, document_name: str, db, mongo_document_id):
        try:
            data = {}
            blocks = []
            result = _File_Status_Collection.update_one_by_query_upsert({"file_name": document_name, 'bucket_name': bucket_name, "inserted_time": datetime.now()}, {"status.ocr.status": 1}, db)
            if result.upserted_id is not None:
                mongo_document_id = result.upserted_id
            if '.' in document_name and document_name.split('.')[-1].strip().lower() == 'docx':
                response = self.s3Client.get_object(Bucket=bucket_name, Key=document_name)
                file_stream = BytesIO(response['Body'].read())
                data = self.read_word_document(file_stream, document_name)
            else:
                response = self.textract.start_document_text_detection(
                    DocumentLocation={
                        'S3Object': {
                            'Bucket': bucket_name,
                            'Name': document_name,
                        },
                    })
                job_id = response['JobId']
                while True:
                    status, response = self.get_job_status(job_id)
                    if status in ['SUCCEEDED', 'FAILED']:
                        blocks.extend(response['Blocks'])
                        token = None
                        if "NextToken" in response and response["NextToken"]:
                            token = response["NextToken"]
                            while (token):
                                resp = self.tokens(token, job_id)
                                token = resp.get('NextToken')
                                blocks.extend(resp['Blocks'])

                        break
                    time.sleep(10)  # Wait for 10 seconds before polling again

                if status == 'SUCCEEDED':
                    context = ''
                    current_page = None

                    for block in blocks:
                        page_num = block['Page']

                        if current_page is None or page_num != current_page:
                            context = ''
                            current_page = page_num

                        if block['BlockType'] == 'LINE':
                            context += block['Text']
                        data[str(page_num)] = context
                        data['fileName'] = document_name
                    data['blocks'] = blocks
                else:
                    raise Exception('Error in start_document_text_detection')

            _MONGO_REPO.create(data, db)
            pattern_conditions = re.compile('Conditions of Approval', re.IGNORECASE)
            pattern_master_planned = re.compile('MASTER PLANNED UNIT DEVELOPMENT', re.IGNORECASE)
            combined_text = ' '.join(str(value) for key, value in data.items() if isinstance(value, str))
            if pattern_conditions.search(combined_text) and pattern_master_planned.search(combined_text):
                _File_Status_Collection.update_one_by_query_upsert({"file_name": document_name, "bucket_name": bucket_name}, {"status.ocr.status": 2}, db)

                self.__extract_metadata_params(data, mongo_document_id, db)
                resp = create_embedding_of_document(data, mongo_document_id)
                logger.info("EMBEDDING DOCUMENT: %s", resp)
                if "status" in resp and resp["status"] == "ok":
                    return {"status": "ok", "message": "data stored to milvus"}
                else:
                    _File_Status_Collection.update_one_by_query_upsert(
                        {"file_name": document_name, "bucket_name": bucket_name},
                        {
                            "status.ocr.status": 4,
                            "status.ocr.error": resp,
                        },
                        db,
                    )
                    return {"status": "non-ok", "message": "data stored to milvus failed"}
            else:
                logger.warning("File is not a MPUD Document")
                _File_Status_Collection.update_one_by_query_upsert({"file_name": document_name, "bucket_name": bucket_name}, {"status.ocr.status": 2, "status.ocr.error": "File is not a MPUD Document"}, db)
                return {
                    "status": "non-ok",
                    "message": "File is not a MPUD Document",
                }
        except BaseException:
            _File_Status_Collection.update_one_by_query_upsert(
                {"file_name": document_name, "bucket_name": bucket_name},
                {
                    "status.ocr.status": 4,
                    "status.ocr.error": "".join(traceback.format_exc()),
                },
                db,
            )
            return {"message": "".join(traceback.format_exc())}
            # raise Exception('Error in OCR process')

    def check_file_in_mongodb(self, file_key, db):
        try:
            querry = {}
            querry["file_name"] = file_key
            querry["status.ocr.status"] = 2
            resp = _File_Status_Collection.get_one_by_projection(
                querry, {"file_name": 1}, db,
            )
            if resp:
                return True
            else:
                return False
        except BaseException:
            # raise Exception("Error in fetch file check")
            return False

    def loop_files_in_s3Bucket(self, s3Bucket: str, db) :
        try :
            folder_prefix = 'MPUD Records/'
            response = self.s3Client.list_objects_v2(Bucket=s3Bucket, Prefix=folder_prefix)
            if 'Contents' in response :
                for obj in response['Contents'] :
                    file_key = obj['Key']
                    if not self.check_file_in_mongodb(file_key, db) :
                        self.extract_pdf(s3Bucket, file_key, db)
                    else:
                        pass
        except BaseException:
            # raise Exception('Error in fetching file from s3')
            return {"message": "Error in fetching file from s3 Bucket"}

    def get_blocks_from_mongo(self, s3_path: str, db):
        try:
            fnd_rec = _MONGO_REPO.get_one_by_projection({"fileName": "/".join(s3_path.split('/')[1:])}, {"blocks": 1, "geometry_data": 1}, db)

            if fnd_rec:
                logger.debug('Found record')
            if fnd_rec and 'blocks' in fnd_rec:
                blocks = fnd_rec['blocks']
            if 'geometry_data' not in fnd_rec :

                coordinates = {}
                text_dict = {}
                line_dict = {}

                word_list = []
                line_list = []

                for block in blocks:
                    temp_dict = {}
                    if block['BlockType'] == 'WORD':
                        geometry = {}
                        text = block['Text']
                        page_number = block['Page']
                        # Get the geometry of the block (coordinates)
                        geometry['top_left'] = block['Geometry']['Polygon'][0]
                        geometry['top_right'] = block['Geometry']['Polygon'][1]
                        geometry['bottom_right'] = block['Geometry']['Polygon'][2]
                        geometry['bottom_left'] = block['Geometry']['Polygon'][3]
                        temp_dict['word'] = text
                        temp_dict['coordinates'] = geometry
                        temp_dict['pageno'] = page_number

                        word_list.append(temp_dict)

                    elif block['BlockType'] == 'LINE':
                        line_text = block['Text']
                        line_page_number = block['Page']
                        line_geometry = {}
                        line_geometry['top_left'] = block['Geometry']['Polygon'][0]
                        line_geometry['top_right'] = block['Geometry']['Polygon'][1]
                        line_geometry['bottom_right'] = block['Geometry']['Polygon'][2]
                        line_geometry['bottom_left'] = block['Geometry']['Polygon'][3]

                        temp_dict['word'] = line_text
                        temp_dict['coordinates'] = line_geometry
                        temp_dict['pageno'] = line_page_number

                        line_list.append(temp_dict)

                coordinates['word_dict'] = text_dict
                coordinates['line_dict'] = line_dict
                _MONGO_REPO.update_one_by_query_upsert({'fileName': "/".join(s3_path.split('/')[1:])}, {'geometry_data': {'words': word_list, 'lines': line_list}}, db)

            else:
                logger.debug('Geometry data present already')
                coordinates = fnd_rec['geometry_data']

            return coordinates

        except Exception as e:
            logger.error('Error in get_blocks_from_mongo: %s', e)

    def find_text_coordinates(self, page_number: str, search_text: str, s3_path: str, db, mock=False):
        def filetrmock(resp, page_number, tosearch):
            respo = {}
            if "_lines" in resp:
                filtered_lines = [
                    line for line in resp["_lines"]
                    if re.search(tosearch, line["word"], re.IGNORECASE) and line["pageno"] == page_number
                ]
                respo["lines"] = filtered_lines

            elif "_words" in resp:
                filtered_lines = [
                    word for word in resp["_words"]
                    if re.search(tosearch, word["word"], re.IGNORECASE) and word["pageno"] == page_number
                ]
                respo["words"] = filtered_lines
            else:
                respo = {}
            return respo

        try:
            if mock :
                line_projection = {"geometry_data.lines": 1}
                word_projection = {"geometry_data.words": 1}
            else :
                line_projection = {"geometry_data.lines.$": 1}
                word_projection = {"geometry_data.words.$": 1}

            self.get_blocks_from_mongo(s3_path, db)
            dic = {}
            wordsToSearch = search_text.split('\n')
            for line in wordsToSearch:
                resp = _MONGO_REPO.get_one_by_projection({
                    "geometry_data.lines": {
                        "$elemMatch": {
                            "word" : {"$regex": f"^{line}$", "$options": "i"},
                            "pageno" : page_number,
                        }}},
                    line_projection, db)
                if resp :
                    if mock:
                        resp = filetrmock({"_lines": resp['geometry_data']['lines']}, page_number, line)
                        if resp['lines']:
                            dic[resp['lines'][0]['word']] = resp['lines'][0]['coordinates']

                    else:
                        dic[resp['geometry_data']['lines'][0]['word']] = resp['geometry_data']['lines'][0]['coordinates']

                if not resp :
                    for word in line.split(' '):
                        resp = _MONGO_REPO.get_one_by_projection({
                            "geometry_data.words": {
                                "$elemMatch": {
                                    "word" : {"$regex": word, "$options": "i"},
                                    "pageno" : page_number,
                                }}},
                            word_projection, db)

                        if resp :
                            if mock:
                                resp = filetrmock({"_words": resp['geometry_data']['words']}, page_number, word)
                                if resp['words']:
                                    dic[resp['words'][0]['word']] = resp['words'][0]['coordinates']

                            else:
                                dic[resp['geometry_data']['words'][0]['word']] = resp['geometry_data']['words'][0]['coordinates']

            return dic

        except Exception as e:
            logger.error('Error in find_text_coordinates: %s', e)
            return {}

    def upload_file_to_s3(
        self,
        file,
        s3Bucket: str,
        location: str,
        file_name: str,
        user_id,
        user_name,
        county,
        upload_id,
        upload_status,
        db,
    ):
        try:
            content_type, encoding = mimetypes.guess_type(file_name)
            if content_type is None:
                content_type = 'application/pdf'
            result = None
            self.s3Client.upload_fileobj(file, s3Bucket, location, ExtraArgs={'ContentType': content_type})
            logger.info("Uploaded file to s3: %s", file_name)
            result = self.insert_to_documents_collection(file_name, s3Bucket, location, user_id, user_name, county, upload_id, upload_status, db)
            logger.debug("DOCUMENT: %s", result)
            DOCUMENT_DETAILS_COLLECTION.update_one_by_query_upsert(
                {'_id': result.inserted_id} ,
                {"status": "Uploaded to S3", "upload_status": UploadStatus.UPLOADING}, db)

            return result
        except Exception as e:
            if result and result.inserted_id:
                DOCUMENT_DETAILS_COLLECTION.update_one_by_query_upsert({'_id': result.inserted_id} , {"error": e}, db)
            else:
                DOCUMENT_DETAILS_COLLECTION.update_one_by_query_upsert({'file_name': file_name} , {"error": e}, db)
            raise Exception("Error in upload file to s3")

    def insert_to_documents_collection(self, file_name: str, s3Bucket: str, location: str, user_id: str, user_name: str, county: str, upload_id: str, upload_status: UploadStatus, db):
        try:
            document = Document(
                upload_id=upload_id,
                upload_status=upload_status,
                file_name=file_name,
                s3_path=s3Bucket + '/' + location,
                uploaded_at=datetime.now(),
                user_id=user_id,
                user_name=user_name,
                county_name=county,
                category="",
                keywords=[],
                summary="",
                zoning="",
            )
            result = DOCUMENT_DETAILS_COLLECTION.create(
                document.model_dump(), db,
            )
            return result
        except Exception as e:
            logger.error('Error in insert_to_documents_collection: %s', e)
            return False

    def __extract_metadata_params(self, data: dict, mongo_document_id: str, db):
        # number of pages is either env variable or if its set 0 then all the keys minus one 'fileName'
        no_pages = int(settings.NO_PAGE_FOR_PARAM_EXTRACT) or (len(data.keys()) - 1)
        pages = [data[str(i)] for i in range(1, no_pages + 1)]

        # TODO: get existing and pre-determined categories
        categories = self.__get_all_categories()

        open_ai = OpenAI(settings.OPENAI_API_KEY)
        # TODO: choose model based on user preference
        metadata = open_ai.extract_metadata_params(
            "gpt-4o-mini", data["fileName"], pages, categories,
        )
        logger.debug("METADATA: %s", metadata)

        # TODO: if params are not undefined then update in Mongo and create their embedding
        update_result = DOCUMENT_DETAILS_COLLECTION.update_one_by_query_upsert(
            {"_id": ObjectId(mongo_document_id)},
            {
                "summary": metadata.get("summary", ""),
                "category": metadata.get("category", ""),
                "zoning": metadata.get("zoning", ""),
                "keywords": metadata.get("keywords", []),
            },
            db,
        )
        logger.debug("MONGO DOCUMENT UPDATE: %s", update_result)

        create_embedding_of_metadata(metadata, mongo_document_id)

    def __get_all_categories(self):
        distinct_categories = DOCUMENT_DETAILS_COLLECTION.distinct_key_by_query(
            "category", {}, get_mongo_db(),
        )

        # TODO: save these predefined somewhere and fetch from there
        categories = ["COA", "Conditions of Approval"]
        categories.extend(distinct_categories)
        logger.debug("CATEGORIES: %s", categories)

        return categories

    def get_files_data(self, db):
        try:
            response = DOCUMENT_DETAILS_COLLECTION.get_all(db)
            return response
        except BaseException:
            raise Exception("Error in get Files data")

    def chat_data(self, db):
        try:
            # Fetching all chat data using get_all
            chats = CHAT_DETAILS.get_all(db)  # Assuming this returns all documents

            # Using map to process each chat and extract fields without explicit loops
            response = list(map(lambda chat: {
                "human_messages": chat.get("content", {}).get("human", {}).get("message", ""),
                "ai_messages": chat.get("content", {}).get("ai", {}).get("message", ""),
                "document_titles": chat.get("document", [{}])[0].get("title", ""),
                "document_contents": chat.get("document", [{}])[0].get("content", ""),
                "document_last_updated": chat.get("document", [{}])[0].get("last_updated", ""),
                "document_status": chat.get("document", [{}])[0].get("status", ""),
            }, chats))

            return response

        except BaseException:
            raise Exception("Error in Chat Details")
