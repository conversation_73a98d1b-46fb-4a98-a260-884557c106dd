class BlocksProcessor:
    def __init__(self, blocks):
        self.blocks = blocks
        self.key_map = {}
        self.value_map = {}
        self.block_map = {}
        self.table_blocks = []
        self.layout_fig_map = {}

    def update_blocks(self, blocks):
        self.blocks = blocks
        self.key_map.clear()
        self.value_map.clear()
        self.block_map.clear()
        self.table_blocks.clear()
        self.layout_fig_map.clear()

    def get_kv_map(self):
        blocks = self.blocks
        for block in blocks:
            block_id = block["Id"]
            self.block_map[block_id] = block

            if block["BlockType"] == "KEY_VALUE_SET":
                if "KEY" in block["EntityTypes"]:
                    self.key_map[block_id] = block
                else:
                    self.value_map[block_id] = block
            elif block["BlockType"] == "TABLE":
                self.table_blocks.append(block)
            elif "LAYOUT_" in block["BlockType"]:
                self.layout_fig_map[block_id] = block

        return (
            self.key_map,
            self.value_map,
            self.block_map,
            self.table_blocks,
            self.layout_fig_map,
        )

    def get_kv_relationship(self):
        kvs = {}
        for block_id, key_block in self.key_map.items():
            value_block = self.find_value_block(key_block)
            key = self.get_text(key_block)
            val = self.get_text(value_block) if value_block else ""
            if key not in kvs:
                kvs[key] = []
            kvs[key].append(val)
        return kvs

    def find_value_block(self, key_block):
        for relationship in key_block["Relationships"]:
            if relationship["Type"] == "VALUE":
                for value_id in relationship["Ids"]:
                    return self.value_map.get(value_id)
        return None

    def get_text(self, result):
        text = ""
        if "Relationships" in result:
            for relationship in result["Relationships"]:
                if relationship["Type"] == "CHILD":
                    for child_id in relationship["Ids"]:
                        word = self.block_map[child_id]
                        if word["BlockType"] == "WORD":
                            text += word["Text"] + " "
                        elif word["BlockType"] == "LINE":
                            text += word["Text"] + "\n"
                        elif word["BlockType"] == "SELECTION_ELEMENT":
                            if word["SelectionStatus"] == "SELECTED":
                                text += "X "
        return text.strip()

    def extract_table(self, table_block, block_map):
        """
        Extract rows and cells from a table block.
        """
        table = []

        for relationship in table_block.get("Relationships", []):
            if relationship["Type"] == "CHILD":
                # Extract cells directly, skipping the row level
                for cell_id in relationship["Ids"]:
                    cell_block = block_map.get(cell_id)
                    if cell_block and cell_block["BlockType"] == "CELL":
                        row_index = cell_block["RowIndex"]
                        col_index = cell_block["ColumnIndex"]

                        # Ensure the table has enough rows
                        while len(table) < row_index:
                            table.append([])

                        # Ensure the row has enough columns
                        while len(table[row_index - 1]) < col_index:
                            table[row_index - 1].append("")

                        # Extract cell content
                        cell_content = self.extract_text_from_cell(
                            cell_block,
                            block_map,
                        )
                        table[row_index - 1][col_index - 1] = cell_content

        return table

    def extract_tables(self):
        blocks = self.blocks
        block_map = {block["Id"]: block for block in blocks}

        # Table container
        tables = []

        # Iterate over blocks to find table blocks
        for block in blocks:
            if block["BlockType"] == "TABLE":
                # Extract rows and cells from the table
                table = self.extract_table(block, block_map)
                if table:  # Only append non-empty tables
                    tables.append(table)

        return tables

    def extract_text_from_cell(self, cell_block, block_map):
        """
        Extract text from a cell block.
        """
        cell_text = ""

        if "Relationships" in cell_block:
            for relationship in cell_block["Relationships"]:
                if relationship["Type"] == "CHILD":
                    # Extract text elements (WORDS)
                    for child_id in relationship["Ids"]:
                        word_block = block_map.get(child_id)
                        if word_block and word_block["BlockType"] == "WORD":
                            cell_text += word_block.get("Text", "") + " "

        return cell_text.strip()

    def layout_fig(self):
        fig_list = []
        for block_id, key_block in self.layout_fig_map.items():
            fig_dict = {"Page": key_block["Page"], "Geometry": key_block["Geometry"]}
            res = self.get_text(key_block)
            fig_dict[key_block["BlockType"]] = res
            fig_list.append(fig_dict)
        return fig_list

    def extract_layout(self, data):
        result = {}
        last_section_header = None
        previous_page = ""
        previous_title = ""
        for item in data:
            if "LAYOUT_FIGURE" in item:
                continue
            page = str(item.get("Page"))

            title = item.get("LAYOUT_TITLE")
            text = item.get("LAYOUT_TEXT")
            section_header = item.get("LAYOUT_SECTION_HEADER")
            layout_header = item.get("LAYOUT_HEADER")

            if section_header is None:
                section_header = layout_header

            if page != previous_page:
                if previous_title:
                    if previous_title != title:
                        section_header = "Untitled"
                    if section_header is not None:
                        section_header = section_header

                previous_page = page

            # Initialize the page entry if it doesn't exist
            if page not in result:
                result[page] = {"title": None, "sections": {}}

            # Set the title if available
            if title:
                result[page]["title"] = title

            # Update current section header
            if (
                title is not None
                and previous_title is not None
                and (title.lower() == previous_title.lower())
            ):
                current_section_header = last_section_header  # Retain last section header if no new one is found

            elif section_header:
                current_section_header = section_header
                last_section_header = current_section_header
                if current_section_header not in result[page]["sections"]:
                    result[page]["sections"][current_section_header] = []
            else:
                if title:
                    current_section_header = last_section_header

            # Collect text
            if text:
                if title is None and current_section_header is None:
                    # If title is None and no section header, store text under "Untitled"
                    result[page]["sections"].setdefault("Untitled", []).append(text)
                elif current_section_header:
                    # Append to the current section if there's a valid section header
                    result[page]["sections"].setdefault(
                        current_section_header,
                        [],
                    ).append(text)
                elif title == result.get(str(int(page) - 1), {}).get("title"):
                    # If title matches the previous page's title, append to the last section header
                    if last_section_header:
                        result[str(int(page) - 1)]["sections"][
                            last_section_header
                        ].append(text)
                else:
                    # Default case: if no section header, store text under "Untitled"
                    result[page]["sections"].setdefault("Untitled", []).append(text)
            if title:
                previous_title = title

        return result
