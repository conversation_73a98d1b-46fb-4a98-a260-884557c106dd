from app.database.database_storage import get_mongo_config_db, get_mongo_db
from app.crud.mongo.base_repository import BaseRepository as MongoRepo

_MONGO_REPO = MongoRepo("entity_config")
_COA_header_config = MongoRepo("COA_header_config")
_KB_templates = MongoRepo("KB_templates")
_Temp_coa_points = MongoRepo("Temp_coa_data")

Kb_template_data = [
    {
        "name": "pasco",
        "type": "info",
        "file_to_search": "MEMORANDUM",
        "fields": {
            "file_no": {"key": ["FILE NO.:"], "parent": "root", "end_key": ["DATE:"]},
            "subject": {
                "key": ["SUBJECT:"],
                "parent": "root",
                "delimiter": "- ",
                "multi_line": True,
                "end_key": ["REFERENCES:", "REFERENCE:"],
                "subfields": {
                    "file_type": {"key": "", "parent": "subject", "index": 0},
                    "community": {"key": "", "parent": "subject", "index": 1},
                    "developers": {"key": "", "parent": "subject", "index": 2},
                    "request": {"key": "", "parent": "subject", "index": 3},
                },
            },
            "reference": {
                "key": ["REFERENCES:", "REFERENCE:"],
                "parent": "root",
                "multi_line": True,
                "end_key": ["AGENDA SECTION:", "THRU"],
            },
        },
    },
    {
        "is_mpud": True,
        "key1": "Conditions of Approval",
        "key2": "MASTER PLANNED UNIT DEVELOPMENT",
    },
    {
        "name": "coa_extracted_headers",
        "type": "header_extraction",
        "fields": {
            "relevant_section": "CONDITIONS OF APPROVAL",
            "stop_keyword": "OWNER/DEVELOPER ACKNOWLEDGMENT",
            "excluded_headers": ["Untitled", "General"],
            "excluded_patterns": [
                "^[0-9]+\\.",
                "^[A-Z]\\.$",
                "^[a-z]\\.$",
                "^[A-Z]\\)$",
                "^\\([a-z]\\)\\s",
            ],
        },
    },
]

kb_entity_data = {
    "default_mpud_entities": [
        "administrative action",
        "agreement",
        "application",
        "concept",
        "contact_information",
        "community",
        "development",
        "development project",
        "document",
        "document section",
        "event",
        "geo",
        "guideline",
        "governance decision",
        "infrastructure",
        "initiative",
        "legal provision",
        "legislative",
        "location",
        "planning guideline",
        "planning initiative",
        "process",
        "proposal",
        "project",
        "regulation",
        "regulatory",
        "regulatory framework",
        "regulatory requirement",
        "request",
        "residential",
        "role",
        "technology",
        "zoning classification",
    ]
}

COA_header_data = {
    "headerList": [
        "general",
        "environmental",
        "openspace/buffering",
        "transportation/circulation",
        "access management",
        "dedication of right-of-way",
        "design/construction specifications",
        "utilities/water service/wastewater disposal",
        "stormwater",
        "land use",
        "procedures",
    ]
}


async def get_config_from_db():
    try:
        await load_entity_config()
        return _MONGO_REPO.get_all(get_mongo_config_db())
    except BaseException:
        return [{"default_mpud_entities": []}]


async def load_KG_templates():
    try:
        resp = _KB_templates.get_all(get_mongo_db())
        if not resp:
            _KB_templates.create_many(Kb_template_data, get_mongo_db())
        return resp
        # return _KB_templates.get_all(get_mongo_db())
    except BaseException:
        return {"error": True}


async def load_entity_config():
    try:
        resp = _MONGO_REPO.get_all(get_mongo_config_db())
        if not resp:
            _MONGO_REPO.create(kb_entity_data, get_mongo_config_db())
        return resp

    except BaseException:
        return {"error": True}


async def load_COA_header_config():
    try:
        resp = _COA_header_config.get_all(get_mongo_config_db())
        if not resp:
            _COA_header_config.create(COA_header_data, get_mongo_config_db())
        return resp

    except BaseException:
        return {"error": True}


async def get_temp_coa_points_from_db():
    try:
        resp = _Temp_coa_points.get_all(get_mongo_config_db())
        if not resp:
            _Temp_coa_points.create({}, get_mongo_config_db())
            return {}
        return resp
    except BaseException:
        return {"error": True}


def load_temp_coa_points(coa_points):
    try:
        _Temp_coa_points.delete_one({}, get_mongo_config_db())
        _Temp_coa_points.create(coa_points, get_mongo_config_db())
        return True

    except BaseException:
        return {"error": True}
