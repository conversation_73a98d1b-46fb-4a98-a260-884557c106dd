from app.crud.mongo.base_repository import BaseRepository
import json
from typing import Dict, List
import traceback
from app.crud.neo4j.Neo4jInterface import Neo4JInterface
from datetime import datetime
import time
from app.crud.DocumentAnalysis import BlocksProcessor
import boto3
from app.core.config import settings
from app.crud.textract_s3_interface import upload_obj_to_s3, get_textract_obj_from_s3
from app.crud.ocr.main import Textract
from app.core.lightragv2.lightrag_fine_tuned import CustomRag
import re
from app.models.document import UploadStatus
from app.utils.appsync_utils import send_progress_event
from app.core.telemetry import get_logger
from app.crud.neo4j.mpud_toolkit import extract_actionable_fields

logger = get_logger("pasco.ai.connector")

with open("app/crud/neo4j/desc.json") as file:
    desc_node_map = json.load(file)


class KnowledgeGraphRepositoryV2:
    def __init__(self, db):
        self.status_repo = BaseRepository("files_status")
        self.data_repo = BaseRepository("files_data")
        self.documents_repo = BaseRepository("documents")
        self.templates_repo = BaseRepository("KB_templates")
        self.db = db
        self.ocr = Textract()
        self.neo4j_interface = Neo4JInterface(
            settings.NEO4J_URI,
            settings.NEO4J_USERNAME,
            settings.NEO4J_PASSWORD,
        )
        self.textract_client = boto3.client(
            "textract",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID_S3_Textract,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY_S3_Textract,
            region_name=settings.AWS_REGION,
        )
        self.s3Client = boto3.client(
            "s3",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION,
        )
        self.driver = self.neo4j_interface.get_driver()
        self.blocks_processor = BlocksProcessor([])

    def _update_files_status(
        self,
        pdf_bucket: str,
        pdf_path: str,
        update_data: dict,
    ) -> bool:
        try:
            found_doc = self.status_repo.get_one_by_filter(
                {
                    "bucket_name": pdf_bucket,
                    "file_name": pdf_path,
                    "status.ocr.status": 2,
                },
                self.db,
            )
            if found_doc:
                self.status_repo.update_one_by_query_upsert(
                    {"bucket_name": pdf_bucket, "file_name": pdf_path},
                    update_data,
                    self.db,
                )
                logger.info(
                    "File already loaded: %s/%s in collection",
                    pdf_bucket,
                    pdf_path,
                )
                return (False, found_doc, [], [])
            else:
                self.status_repo.update_one_by_query_upsert(
                    {"bucket_name": pdf_bucket, "file_name": pdf_path},
                    update_data,
                    self.db,
                )

                logger.info(
                    "Upserted record for file: %s in collection %s",
                    pdf_bucket,
                    pdf_path,
                )

            return (True, {}, [], [])
        except Exception as e:
            logger.error("mongo error: %s\n%s", str(e), traceback.format_exc())
            return (False, {}, [], [])

    def _update_files_data(
        self,
        pdf_bucket: str,
        pdf_path: str,
        update_data: dict,
    ) -> bool:
        try:
            self.data_repo.update_one_by_query_upsert(
                {"filePath": f"{pdf_bucket}/{pdf_path}"},
                update_data,
                self.db,
            )
            logger.info(
                "Upserted record for file: %s/%s in collection %s",
                pdf_bucket,
                pdf_path,
            )

            return True
        except Exception as e:
            logger.error("mongo error" + e, "".join(traceback.format_exc()))
            return False

    def __get_textract_job_status(self, job_id: str) -> tuple[str, Dict[str, any]]:
        response = self.textract_client.get_document_analysis(
            JobId=job_id,
        )
        return response["JobStatus"], response

    def __get_textract_tokens(self, token: str, job_id: str) -> dict:
        resp = self.textract_client.get_document_analysis(
            JobId=job_id,
            NextToken=token,
        )
        return resp

    def _read_pdf(
        self,
        pdf_bucket: str,
        pdf_path: str,
        county: str,
        user_name: str,
    ) -> List[range]:
        try:
            logger.info("Reading PDF: %s", pdf_path)

            pages = {}
            blocks = []
            response = self.textract_client.start_document_analysis(
                DocumentLocation={
                    "S3Object": {
                        "Bucket": pdf_bucket,
                        "Name": pdf_path,
                    },
                },
                FeatureTypes=["LAYOUT"],
            )  # 'FORMS', 'TABLES'
            job_id = response["JobId"]

            # Add timeout logic to prevent infinite hanging
            MAX_POLLING_TIME = 30 * 60  # 30 minutes timeout for document analysis
            start_time = time.time()

            while True:
                status, response = self.__get_textract_job_status(job_id)
                if status in ["SUCCEEDED", "FAILED"]:
                    blocks.extend(response["Blocks"])
                    token = None
                    if "NextToken" in response and response["NextToken"]:
                        token = response["NextToken"]
                        while token:
                            resp = self.__get_textract_tokens(token, job_id)
                            token = resp.get("NextToken")
                            blocks.extend(resp["Blocks"])

                    break

                # Check for timeout
                elapsed_time = time.time() - start_time
                if elapsed_time > MAX_POLLING_TIME:
                    logger.error(f"Textract document analysis timeout after {MAX_POLLING_TIME / 60} minutes for job {job_id}")
                    raise TimeoutError(
                        f"Document analysis polling exceeded the maximum time limit of {MAX_POLLING_TIME / 60} minutes"
                    )

                # Log progress every 30 seconds
                if int(elapsed_time) % 30 == 0:
                    logger.info(f"Textract job {job_id} still processing, elapsed: {elapsed_time:.1f}s")
                time.sleep(5)  # Wait for 5 seconds before polling again

            if status == "SUCCEEDED":
                logger.info("PDF extracted successfully!")
                total_pages = 0
                context = ""
                current_page = None

                for block in blocks:
                    page_num = block["Page"]

                    if int(page_num) > total_pages:
                        total_pages = int(page_num)

                    if current_page is None or page_num != current_page:
                        context = ""
                        current_page = page_num

                    if block["BlockType"] == "LINE":
                        context += block["Text"]
                    pages[str(page_num)] = context
                    pages["fileName"] = pdf_path

                logger.info("PDF has %d pages", total_pages)
                self.blocks_processor.update_blocks(blocks)

                key_map, value_map, block_map, table_blocks, layout_fig_map = (
                    self.blocks_processor.get_kv_map()
                )
                kvp = self.blocks_processor.get_kv_relationship()
                table_data = self.blocks_processor.extract_tables()

                layout_data = self.blocks_processor.layout_fig()
                combined_layout_data = self.blocks_processor.extract_layout(layout_data)

                document_name = pdf_path
                return (
                    pages,
                    document_name,
                    total_pages,
                    blocks,
                    kvp,
                    table_data,
                    layout_data,
                    combined_layout_data,
                )
        except Exception as e:
            logger.error("Error in _read_pdf: %s", str(e))
            logger.error("Traceback: %s", traceback.format_exc())
            raise

    def check_file_in_mongodb(self, file_key):
        try:
            querry = {}
            querry["file_name"] = file_key
            querry["status.ocr.status"] = 2
            resp = self.status_repo.get_one_by_projection(
                querry,
                {"file_name": 1},
                self.db,
            )
            if resp:
                return True
            else:
                return False
        except BaseException:
            return False

    def loop_files_in_s3Bucket(
        self,
        s3Bucket: str,
        pdf_path: str,
        county: str,
        user_name: str,
        user_id: str,
        user_upload: bool,
        documents_id,
    ):
        try:
            folder_prefix = "MPUD Records/"
            response = self.s3Client.list_objects_v2(
                Bucket=s3Bucket,
                Prefix=folder_prefix,
            )
            if "Contents" in response:
                for obj in response["Contents"]:
                    file_key = obj["Key"]
                    if not self.check_file_in_mongodb(file_key):
                        documents = self.ocr.insert_to_documents_collection(
                            pdf_path.split("/")[-1],
                            s3Bucket,
                            pdf_path,
                            user_id,
                            user_name,
                            county,
                            self.db,
                        )

                        documents_id = documents.inserted_id
                        self.documents_repo.update_one_by_query_upsert(
                            {"_id": documents_id},
                            {"status": "Picked for OCR", "upload_initiator": "system"},
                            self.db,
                        )

                        self.process_pdf(
                            s3Bucket,
                            file_key,
                            county,
                            user_name,
                            user_upload,
                            documents_id,
                        )
        except BaseException:
            return {"message": "Error in fetching file from s3 Bucket"}

    async def process_data(
        self,
        pdf_bucket: str,
        pdf_path: str,
        user_id: str,
        county: str,
        user_name: str,
        single_file: bool,
        user_upload: bool,
        upload_id: str,
        documents_id,
    ) -> str:
        try:
            logger.info("Starting to process data")
            if documents_id:
                self.documents_repo.update_one_by_query_upsert(
                    {"_id": documents_id},
                    {
                        "status": "Picked for OCR",
                        "upload_initiator": "manual",
                        "upload_status": UploadStatus.OCR,
                    },
                    self.db,
                )
            else:
                logger.info("Inserting to documents collection")
                documents = self.ocr.insert_to_documents_collection(
                    pdf_path.split("/")[-1],
                    pdf_bucket,
                    pdf_path,
                    user_id,
                    user_name,
                    county,
                    upload_id,
                    UploadStatus.OCR,
                    self.db,
                )

                documents_id = documents.inserted_id
                self.documents_repo.update_one_by_query_upsert(
                    {"_id": documents_id},
                    {
                        "status": "Picked for OCR",
                        "upload_initiator": "system",
                        "upload_status": UploadStatus.OCR,
                    },
                    self.db,
                )

            if not single_file:
                resp = self.loop_files_in_s3Bucket(
                    pdf_bucket,
                    pdf_path,
                    county,
                    user_name,
                    user_id,
                    user_upload,
                    False,
                )
            else:
                resp = await self.process_pdf(
                    pdf_bucket,
                    pdf_path,
                    county,
                    user_name,
                    user_upload,
                    documents_id,
                    upload_id,
                )
                await send_progress_event(
                    upload_id=upload_id,
                    status=UploadStatus.COMPLETED,
                    progress=100,
                    message="Ready to use",
                )
            logger.info("Process complete")
            return resp
        except Exception as e:
            logger.error("Error in process_data: %s", str(e))
            raise

    def get_job_status(self, job_id):
        response = self.textract_client.get_document_text_detection(
            JobId=job_id,
        )
        return response["JobStatus"], response

    def tokens(self, token, job_id):
        resp = self.textract_client.get_document_text_detection(
            JobId=job_id,
            NextToken=token,
        )
        return resp

    def is_mpud(self, pdf_bucket, pdf_path):
        try:
            # Fetch the configuration from the templates collection
            template = self.templates_repo.get_one_by_filter({"is_mpud": True}, self.db)
            if not template:
                raise Exception("Template for is_mpud not found in the database")

            key1 = template.get("key1", "")
            key2 = template.get("key2", "")

            # Check if the file is already marked as not MPUD
            if self.status_repo.get_one_by_projection(
                {"file_name": pdf_path, "bucket_name": pdf_bucket, "mpudFile": False},
                {"_id": 1},
                self.db,
            ):
                logger.warning(f"{pdf_bucket}/{pdf_path} File is not a MPUD document")
                return False

            # Fetch document data
            data = self.data_repo.get_one_by_projection(
                {"filePath": f"{pdf_bucket}/{pdf_path}", "mpudFile": False},
                {"_id": 0},
                self.db,
            )
            if not data:
                data = {}
                blocks = []
                response = self.textract_client.start_document_text_detection(
                    DocumentLocation={
                        "S3Object": {
                            "Bucket": pdf_bucket,
                            "Name": pdf_path,
                        },
                    }
                )
                job_id = response["JobId"]

                # Timeout logic
                MAX_POLLING_TIME = 2 * 60 * 60
                start_time = time.time()

                while True:
                    status, response = self.get_job_status(job_id)

                    if status in ["SUCCEEDED", "FAILED"]:
                        blocks.extend(response["Blocks"])
                        token = None
                        if "NextToken" in response and response["NextToken"]:
                            token = response["NextToken"]
                            while token:
                                resp = self.tokens(token, job_id)
                                token = resp.get("NextToken")
                                blocks.extend(resp["Blocks"])
                        break

                    # Check for timeout
                    elapsed_time = time.time() - start_time
                    if elapsed_time > MAX_POLLING_TIME:
                        raise TimeoutError(
                            "Polling for document text detection exceeded the maximum time limit of 2 hours"
                        )

                    logger.info("sleep for 10 seconds detect document text.")
                    time.sleep(10)

                if status == "SUCCEEDED":
                    context = ""
                    current_page = None

                    for block in blocks:
                        page_num = block["Page"]

                        if current_page is None or page_num != current_page:
                            context = ""
                            current_page = page_num

                        if block["BlockType"] == "LINE":
                            context += block["Text"]
                        data[str(page_num)] = context
                    data["blocks"] = blocks
                else:
                    raise Exception("Error in start_document_text_detection")

            if data:
                self.data_repo.update_one_by_query_upsert(
                    {"filePath": f"{pdf_bucket}/{pdf_path}"},
                    data,
                    self.db,
                )

            # Use the patterns from the template
            pattern_conditions = re.compile(key1, re.IGNORECASE)
            pattern_master_planned = re.compile(key2, re.IGNORECASE)

            combined_text = " ".join(
                str(value) for key, value in data.items() if isinstance(value, str)
            )
            if pattern_conditions.search(
                combined_text
            ) and pattern_master_planned.search(combined_text):
                self.status_repo.update_one_by_query_upsert(
                    {"file_name": pdf_path, "bucket_name": pdf_bucket},
                    {"status.ocr.status": 2},
                    self.db,
                )
                return True
            else:
                self.status_repo.update_one_by_query_upsert(
                    {"file_name": pdf_path, "bucket_name": pdf_bucket},
                    {"mpudFile": False, "status.ocr.status": 2},
                    self.db,
                )
                logger.warning(f"{pdf_bucket}/{pdf_path} File is not a MPUD document")
                return False

        except Exception as e:
            logger.error("Error in is_mpud: %s", str(e))
            logger.error("Traceback: %s", traceback.format_exc())
            return False

    def fetch_template(self, name, type_):
        logger.info(f"Fetching template: name={name}, type={type_}")
        templates_collection = self.templates_repo
        template = templates_collection.get_one_by_filter(
            {"name": name, "type": type_}, self.db
        )
        if not template:
            raise ValueError(
                f"Template with name '{name}' and type '{type_}' not found in the database."
            )
        template.pop("_id", None)
        logger.info(f"Retrieved template: {template}")
        return template

    def extract_field(
        self, field_key, textract_blocks, delimiter=None, subfields=None, end_key=None
    ):
        data = []
        capturing = False
        field_keys = field_key if isinstance(field_key, list) else [field_key]
        end_keys = end_key if isinstance(end_key, list) else [end_key]

        for block in textract_blocks:
            if block.get("BlockType") != "LINE":
                continue
            text = block.get("Text", "")
            if any(key in text for key in field_keys):  # Start capturing if key matches
                capturing = True
                matched_key = next(key for key in field_keys if key in text)
                data.append(text.split(matched_key, 1)[1].strip())
            elif capturing:
                if any(key in text for key in end_keys):  # Stop capturing at end_key
                    capturing = False
                    break
                data.append(text.strip())

        if data and subfields:
            normalized = (
                " ".join(data).replace("–", "-") if delimiter else " ".join(data)
            )
            parts = [part.strip() for part in normalized.split(delimiter)]
            return {
                subfield: (
                    parts[subfields[subfield]["index"]]
                    if len(parts) > subfields[subfield]["index"]
                    else None
                )
                for subfield in subfields
            }
        return " ".join(data).strip() if data else None

    def _clean_section_header(self, header_text):
        """
        Clean section header by removing trailing punctuation and normalizing whitespace.

        Examples:
        - "PLANNING:" -> "PLANNING"
        - "Environmental;" -> "Environmental"
        - "Transportation/Circulation: " -> "Transportation/Circulation"
        - "Utilities:;" -> "Utilities"
        """
        if not header_text:
            return header_text

        # Remove trailing punctuation (common in headers: : ; , . ! ?)
        # But preserve forward slashes as they're meaningful (e.g., "Transportation/Circulation")
        cleaned = header_text.strip()
        while cleaned and cleaned[-1] in ':;,.!?':
            cleaned = cleaned[:-1].strip()

        return cleaned

    def _is_conditions_of_approval_section(self, text):
        """
        Check if text contains "CONDITIONS OF APPROVAL" with flexible pattern matching.
        Handles variations like:
        - "CONDITIONS OF APPROVAL"
        - "CONDITIONS OF APPROVAL FOR"
        - "CONDITIONS OF APPROVAL IS"
        - "The CONDITIONS OF APPROVAL"
        - "CONDITIONS OF APPROVAL XXX" (anything after)
        - Case insensitive matching
        - Extra whitespace handling
        """
        import re
        if not text:
            return False

        # Normalize whitespace and convert to uppercase
        normalized_text = ' '.join(text.upper().split())

        # Pattern to match "CONDITIONS OF APPROVAL" with optional prefix/suffix
        # \b ensures word boundaries to avoid partial matches
        # \s* handles variable whitespace between words
        pattern = r'\bCONDITIONS\s*OF\s*APPROVAL\b'
        return bool(re.search(pattern, normalized_text))

    def extract_coa_headers(self, layout_data):
        try:
            fields = {
                "relevant_section": "CONDITIONS OF APPROVAL",
                "stop_keyword": "OWNER/DEVELOPER ACKNOWLEDGMENT",
                "excluded_headers": ["Untitled", "General"],
                "excluded_patterns": [
                    "^[0-9]+\\.",
                    "^[A-Z]\\.$",
                    "^[a-z]\\.$",
                    "^[A-Z]\\)$",
                    "^\\([a-z]\\)\\s",
                ],
            }

            # relevant_section = fields.get("relevant_section", "").upper()  # Unused variable
            stop_keyword = fields.get("stop_keyword", "").upper()
            excluded_headers = set(fields.get("excluded_headers", []))
            excluded_patterns = fields.get("excluded_patterns", [])

            is_conditions_of_approval = False
            last_section_header = None
            for recd in layout_data:
                layout_title = recd.get("LAYOUT_TITLE", "").upper()
                layout_header = recd.get("LAYOUT_HEADER", "").strip()
                layout_header_upper = layout_header.upper()

                # Check if relevant section is in the layout title
                if self._is_conditions_of_approval_section(layout_title):
                    is_conditions_of_approval = True

                # Stop collecting if the stop keyword is found in the layout title
                if stop_keyword in layout_title:
                    is_conditions_of_approval = False

                # Start collecting if relevant section is found in layout header
                if self._is_conditions_of_approval_section(layout_header_upper):
                    is_conditions_of_approval = True

                # Stop collecting if stop keyword is found in layout header
                if stop_keyword in layout_header_upper:
                    is_conditions_of_approval = False

                # If we're in the "Conditions of Approval" section, process the data
                if is_conditions_of_approval:
                    section = self._clean_section_header(layout_header)
                    # If the header is not excluded and not empty, update last_section_header
                    if section and section.upper() not in excluded_headers and not any(re.match(pattern, section) for pattern in excluded_patterns):
                        last_section_header = section
                    # If the header is empty or excluded, use the last_section_header
                    if not section or section.upper() in excluded_headers or any(re.match(pattern, section) for pattern in excluded_patterns):
                        recd["LAYOUT_HEADER"] = last_section_header if last_section_header else "General"
                    else:
                        recd["LAYOUT_HEADER"] = section
                    recd["is_coa_data"] = True

            logger.info("Extracted COA headers with propagated section headers.")
            return layout_data

        except Exception as e:
            logger.error("Error:", e)
            return None

    def parse_textract_using_template(
        self, textract_blocks, template_name, template_type
    ):
        try:
            template = self.fetch_template(template_name, template_type)
        except ValueError as e:
            logger.error(f"Error fetching template '{template_name}': {e}")
            raise

        fields = template["fields"]
        file_to_search = template["file_to_search"]

        # Initialize extracted_fields with null values for each field, including subfields at the top level
        extracted_fields = {
            field_name: None
            for field_name in fields.keys()
            if "subfields" not in fields[field_name]
        }

        # Add subfields to extracted_fields as top-level keys
        for field_name, field_info in fields.items():
            if "subfields" in field_info:
                for subfield in field_info["subfields"].keys():
                    extracted_fields[subfield] = None

        # Search for the file_to_search in the textract_blocks
        found_file_to_search = any(
            block.get("BlockType") == "LINE" and file_to_search in block.get("Text", "")
            for block in textract_blocks
        )
        logger.info(f"File to search '{file_to_search}' found: {found_file_to_search}")

        # If file_to_search is not found, return extracted_fields with null values
        if not found_file_to_search:
            logger.info(
                f"File to search '{file_to_search}' not found in the document. Returning fields with null values."
            )
            return extracted_fields  # Fields will already have None as values

        # If file_to_search is found, extract the data
        for field_name, field_info in fields.items():
            field_data = self.extract_field(
                field_key=field_info["key"],
                textract_blocks=textract_blocks,
                delimiter=field_info.get("delimiter"),
                subfields=field_info.get("subfields"),
                end_key=field_info.get("end_key"),
            )

            # If the field contains subfields, flatten them into top-level fields
            if isinstance(field_data, dict):
                for subfield, value in field_data.items():
                    extracted_fields[subfield] = value
            else:
                if (
                    field_name in extracted_fields
                ):  # Avoid adding parent fields like "subject"
                    extracted_fields[field_name] = field_data

        logger.info(f"Extracted fields: {extracted_fields}")
        return extracted_fields

    async def process_pdf(
        self,
        pdf_bucket: str,
        pdf_path: str,
        county: str,
        user_name: str,
        user_upload: bool,
        documents_id,
        upload_id: str,
        template_name: str = "pasco",  # Default for now
        template_type: str = "info",  # Default for now
    ) -> str:
        try:
            logger.info(f"Starting to process PDF: {pdf_path}")
            if user_upload or self.is_mpud(pdf_bucket, pdf_path):
                update_resp, found_rec, blocks, layout_data = self._update_files_status(
                    pdf_bucket,
                    pdf_path,
                    {"status.ocr.status": 0},
                )
                logger.info(f"File status updated. Response: {update_resp}")
            if documents_id:
                self.documents_repo.update_one_by_query_upsert(
                    {"_id": documents_id},
                    {
                        "status": "OCR in progress. It may take a while",
                        "upload_status": UploadStatus.OCR,
                    },
                    self.db,
                )
                await send_progress_event(
                    upload_id=upload_id,
                    status=UploadStatus.OCR,
                    progress=100,
                    message="OCR in progress...",
                )
                logger.info(f"Updated document status to OCR in progress for {documents_id}")

                textract_resp = get_textract_obj_from_s3(pdf_bucket, pdf_path)
                if textract_resp:
                    logger.info(
                        f"File already processed: {pdf_bucket}/{pdf_path} in collection {pdf_bucket}"
                    )
                    (
                        pages,
                        document_name,
                        total_pages,
                        blocks,
                        kvp,
                        organized_table_data,
                        layout_data,
                        combined_layout_data,
                    ) = (
                        textract_resp["pages"],
                        textract_resp["document_name"],
                        textract_resp["total_pages"],
                        textract_resp["blocks"],
                        textract_resp["kvp"],
                        textract_resp["organized_table_data"],
                        textract_resp["layout_data"],
                        textract_resp["combined_layout_data"],
                    )
                else:
                    logger.info("---Expensive--- analyzing document")
                    (
                        pages,
                        document_name,
                        total_pages,
                        blocks,
                        kvp,
                        organized_table_data,
                        layout_data,
                        combined_layout_data,
                    ) = self._read_pdf(pdf_bucket, pdf_path, county, user_name)
                    logger.info(
                        f"Document read successfully: {document_name}, total pages: {total_pages}"
                    )

                    upload_obj_to_s3(
                        pdf_bucket,
                        pdf_path,
                        {
                            "pages": pages,
                            "document_name": document_name,
                            "total_pages": total_pages,
                            "blocks": blocks,
                            "kvp": kvp,
                            "organized_table_data": organized_table_data,
                            "layout_data": layout_data,
                            "combined_layout_data": combined_layout_data,
                        },
                    )

                file_data = {
                    "filePath": f"{pdf_bucket}/{pdf_path}",
                    "totalPages": total_pages,
                    "layout_data": layout_data,
                }
                if blocks:
                    file_data["blocks"] = blocks

                    try:
                        logger.info("Calling parse_textract_using_template with blocks")
                        extracted_fields = self.parse_textract_using_template(
                            textract_blocks=blocks,
                            template_name=template_name,
                            template_type=template_type,
                        )
                        file_data["extracted_fields"] = extracted_fields
                        logger.info(f"Extracted fields: {extracted_fields}")
                    except ValueError as e:
                        logger.warning(
                            f"Template matching failed: {str(e)}. Proceeding without template parsing."
                        )
                        file_data["extracted_fields"] = None

                try:
                    logger.info(
                        f"Calling _update_files_data with {pdf_bucket}, {pdf_path}, file_data"
                    )
                    self._update_files_data(
                        pdf_bucket,
                        pdf_path,
                        file_data,
                    )
                    logger.info("Successfully called _update_files_data")
                except Exception as e:
                    logger.error(f"Error updating file data: {str(e)}")

                try:
                    logger.info(
                        f"Calling _update_files_status with {pdf_bucket}, {pdf_path}, file_data"
                    )
                    self._update_files_status(
                        pdf_bucket,
                        pdf_path,
                        {
                            "status.ocr.status": 1,
                            "totalPages": total_pages,
                            "key_val_pair": kvp,
                            "table_data": organized_table_data,
                            "combined_layout_data": combined_layout_data,
                        },
                    )
                    logger.info("Successfully called _update_files_status")
                except Exception as e:
                    logger.error(f"Error updating file status: {str(e)}")

                if documents_id:
                    self.documents_repo.update_one_by_query_upsert(
                        {"_id": documents_id},
                        {
                            "status": "Creating the Knowledge Graph",
                            "upload_status": UploadStatus.KG,
                        },
                        self.db,
                    )
                    await send_progress_event(
                        upload_id=upload_id,
                        status=UploadStatus.KG,
                        progress=0,
                        message="Building the Knowledge Graph",
                    )
                logger.info(f"Called: {pdf_bucket}/{pdf_path}")
                orginal_layout_data = layout_data
                layout_data = self.extract_coa_headers(layout_data)
                rag = CustomRag()
                await rag.ainsert(
                    layout_data,
                    f"{pdf_bucket}/{pdf_path}",
                    additional_fields={
                        "extracted_fields": file_data.get("extracted_fields")
                    },
                    orginal_data=orginal_layout_data
                )
                logger.info("After insert")

                ###

                self._update_files_status(
                    pdf_bucket,
                    pdf_path,
                    {
                        "totalPages": total_pages,
                        "key_val_pair": kvp,
                        "table_data": organized_table_data,
                        "combined_layout_data": combined_layout_data,
                        "inserted_time": datetime.now(),
                        "status.ocr.status": 2,
                    },
                )
                logger.info(f"Successfully processed PDF {pdf_path}")

                if documents_id:
                    self.documents_repo.update_one_by_query_upsert(
                        {"_id": documents_id},
                        {
                            "status": "Ready to use",
                            "upload_status": UploadStatus.COMPLETED,
                        },
                        self.db,
                    )
                    await send_progress_event(
                        upload_id=upload_id,
                        status=UploadStatus.COMPLETED,
                        progress=100,
                        message="Ready to use",
                    )

                logger.info("Exiting process_pdf with success: True")
                return True

            else:
                logger.info(f"{pdf_bucket}/{pdf_path}: Not a MPUD document")
                if documents_id:
                    self.documents_repo.delete_one(
                        {"_id": documents_id},
                        self.db,
                    )
                logger.info("Exiting process_pdf with success: False")
                return False

        except Exception as e:
            logger.error(f"Error processing PDF: {str(e)}")
            if documents_id:
                self.documents_repo.update_one_by_query_upsert(
                    {"_id": documents_id},
                    {"status": str(e)},
                    self.db,
                )
            return json.dumps(
                {
                    "error": f"Error processing PDF: {str(e)}",
                    "upload_status": UploadStatus.ERROR,
                }
            )
        finally:
            logger.info("...////")
            logger.info("Closing connection...")
            self.driver.close()
            logger.info("Neo4j connection closed")
            return False

    def extract_coa_from_pdf(self, layout_data, pdf_bucket: str, pdf_path: str):
        """
        Extract conditions of approval from PDF layout data.
        Returns a list of condition objects with title, header, text, and metadata.
        """
        import re
        import uuid
        from collections import defaultdict

        # Safety check for input data
        if not layout_data or not isinstance(layout_data, list):
            print(f"Warning: Invalid layout_data provided to extract_coa_from_pdf: {type(layout_data)}")
            return []

        pattern = r"^\d+\."
        seen_titles = set()
        is_coa = False
        already_picked = set()
        total_len = len(layout_data)
        i = 0
        text_index = 0
        title_index = 0
        header_index = 0
        data_chunk = []
        non_header = ""
        prev_title = None

        try:
            while i < total_len:
                try:
                    if 'is_coa_data' in layout_data[i].keys():
                        pass

                    if "LAYOUT_TITLE" in layout_data[i].keys() or prev_title:
                        if (
                            "LAYOUT_TITLE" in layout_data[i].keys()
                            and layout_data[i].get("LAYOUT_TITLE", "") + str(i) not in already_picked
                        ):
                            prev_title = {
                                "name": layout_data[i].get("LAYOUT_TITLE", "").replace("\n", " "),
                                "data": layout_data[i].get("LAYOUT_TITLE", "").replace("\n", " "),
                                "coordinates": layout_data[i].get("Geometry", {}).get("BoundingBox", {}),
                                "sequence": int(title_index),
                                "page": int(layout_data[i].get("Page", 0)),
                            }
                            if self._is_conditions_of_approval_section(prev_title["data"]):
                                is_coa = True
                            text_index = 0
                            title_index += 1
                            header_index = 0
                            already_picked.add(layout_data[i].get("LAYOUT_TITLE", "") + str(i))
                        i += 1
                        header = None
                        while i < total_len and (
                            "LAYOUT_TITLE" not in layout_data[i].keys()
                            or layout_data[i].get("LAYOUT_TITLE", "") + str(i) in already_picked
                        ):
                            try:
                                data_to_build = {"title": {}, "file_name": pdf_path}
                                if "LAYOUT_SECTION_HEADER" in layout_data[i]:
                                    if re.match(pattern, layout_data[i]["LAYOUT_SECTION_HEADER"]) or "." in layout_data[i]["LAYOUT_SECTION_HEADER"]:
                                        non_header = layout_data[i]["LAYOUT_SECTION_HEADER"] + "\n"
                                    else:
                                        raw_header = layout_data[i].get("LAYOUT_SECTION_HEADER", "")
                                        cleaned_header = self._clean_section_header(raw_header.replace("\n", " "))
                                        header = {
                                            "name": cleaned_header,
                                            "data": cleaned_header.lower().strip(),
                                            "coordinates": layout_data[i].get("Geometry", {}).get("BoundingBox", {}),
                                            "sequence": int(header_index),
                                            "page": int(layout_data[i].get("Page", 0)),
                                        }
                                        header_index += 1
                                        text_index = 0
                                elif "LAYOUT_TEXT" in layout_data[i]:
                                    text = {
                                        "name": f'{uuid.uuid4()}-text-{text_index}-{layout_data[i].get("Page", 0)}',
                                        "data": non_header + layout_data[i].get("LAYOUT_TEXT", ""),
                                        "sequence": int(text_index),
                                        "coordinates": layout_data[i].get("Geometry", {}).get("BoundingBox", {}),
                                        "page": int(layout_data[i].get("Page", 0)),
                                    }
                                    non_header = ""
                                    is_new_condition = False
                                    if re.match(pattern, text["data"]) and is_coa:
                                        is_new_condition = True
                                    last_node = (
                                        data_chunk
                                        and data_chunk[-1]
                                        and data_chunk[-1].get("text", None)
                                    )
                                    if self._is_conditions_of_approval_section(prev_title.get("data", "")) and prev_title.get("data", "") + str(prev_title.get("page", "-")) not in seen_titles and is_new_condition:
                                        is_new_coa = True
                                        seen_titles.add(prev_title.get("data", "") + str(prev_title.get("page", "-")))
                                    else:
                                        is_new_coa = False
                                    if (
                                        header
                                        or not last_node
                                        or last_node["page"] == text["page"]
                                    ):
                                        if header:
                                            data_to_build.update(
                                                {
                                                    "title": prev_title or {},
                                                    "header": header,
                                                    "text": text,
                                                    "is_coa": is_coa,
                                                    "is_new_condition": is_new_condition,
                                                    "is_new_coa": is_new_coa
                                                }
                                            )
                                        else:
                                            data_to_build.update(
                                                {"title": prev_title or {}, "header": {}, "text": text, "is_coa": is_coa, "is_new_condition": is_new_condition, "is_new_coa": is_new_coa}
                                            )
                                        text_index += 1
                                    else:
                                        text_index = 0
                                        header_index = 0
                                        data_to_build.update(
                                            {"title": {}, "header": header or {}, "text": text, "is_coa": is_coa, "is_new_condition": is_new_condition, "is_new_coa": is_new_coa}
                                        )
                                        prev_title = {}

                                    data_chunk.append(data_to_build)
                            except Exception as e:
                                print(f"Error processing layout item at index {i}: {str(e)}")
                            i += 1
                    else:
                        i += 1
                except Exception as e:
                    print(f"Error in main extraction loop at index {i}: {str(e)}")
                    i += 1  # Move to next item on error

            # --- Replicate group_points logic ---
            points_dict = defaultdict(list)
            prev_header = ""
            start_fresh = True

            for layout in data_chunk:
                try:
                    header = layout.get("header", {}).get("data", prev_header)
                    if not header:  # Ensure header is not empty
                        header = prev_header if prev_header else "General"

                    if layout.get("is_new_condition", False) and start_fresh:
                        points_dict[header].append({
                            **layout,
                            "text": {
                                "data": re.sub(pattern, "", layout.get("text", {}).get("data", ""), flags=re.MULTILINE),
                                "page": layout.get("text", {}).get("page", 0),
                                "coordinates": [layout.get("text", {}).get("coordinates", {})]
                            }
                        })
                        prev_header = header
                    elif not start_fresh and layout.get("is_new_coa", False):
                        start_fresh = True
                        points_dict[header].append({
                            **layout,
                            "text": {
                                "data": re.sub(pattern, "", layout.get("text", {}).get("data", ""), flags=re.MULTILINE),
                                "page": layout.get("text", {}).get("page", 0),
                                "coordinates": [layout.get("text", {}).get("coordinates", {})]
                            }
                        })
                        prev_header = header
                    elif prev_header == "Procedures" and not layout.get("is_new_coa", False):
                        start_fresh = False
                        continue
                    elif layout.get("is_coa", False) and prev_header != "Procedures":
                        try:
                            # Check if there are any points for this header before accessing
                            if points_dict[header]:
                                points_dict[header][-1]["text"]["data"] += layout.get("text", {}).get("data", "")
                                points_dict[header][-1]["text"]["coordinates"].append(layout.get("text", {}).get("coordinates", {}))
                        except Exception as e:
                            print(f"Error appending text: {str(e)}")
                except Exception as e:
                    print(f"Error processing layout for grouping: {str(e)}")

            result_list = []
            for section in points_dict.values():
                result_list += section

            return result_list

        except Exception as e:
            print(f"Critical error in extract_coa_from_pdf: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return []  # Return empty list on critical error

    async def transform_coa_to_minimal_schema(self, flat_list, id_val=None, layout_name_val=None, description="", accela_token=None):
        """
        Transforms a flat list of COA items into the minimal grouped schema required by the user, merging actionable fields from extract_actionable_fields in rnd.py.
        Output format:
        {
          "mpud_info": {
            "conditions": [ ... only non-empty categories, original casing ... ]
          },
          "conditions": {
            ... all keys lowercased ...
          }
        }
        """
        from collections import defaultdict, OrderedDict
        import traceback

        result_conditions = defaultdict(list)
        section_casing_map = OrderedDict()  # Maps lowercase section -> original casing (first seen)

        # Gather all points to process in batch
        points = [item.get("text", {}).get("data", "").strip() for item in flat_list]

        print(f"Processing {len(points)} points for actionable fields extraction...")
        print(f"Accela token provided: {'Yes' if accela_token else 'No'}")

        # Try to get actionable results, but provide a fallback if it fails
        actionable_results = []
        try:
            actionable_results = await extract_actionable_fields(points, description, accela_token)
            print(f"Successfully extracted actionable fields for {len(actionable_results)} points")
        except Exception as e:
            print(f"Error extracting actionable fields: {str(e)}")
            print(traceback.format_exc())
            # Create default actionable results
            actionable_results = [
                {
                    "condition": point,
                    "is_actionable": True,
                    "extracted_data": {
                        "trigger": "Prior to construction",
                        "action_required": "Submit required documentation",
                        "responsible_party": "Developer",
                        "deliverable": "Required permits and documentation",
                        "enforcing_department": "Planning and Economic Growth Department",
                        "validating_department": "Development Review Division"
                    },
                    "is_valid_condition": True,
                    "accela_fields": []
                } for point in points
            ]

        for idx, item in enumerate(flat_list):
            try:
                section = item.get("header", {}).get("name", "General")
                section_lc = section.lower() if section else "general"
                if section_lc not in section_casing_map:
                    section_casing_map[section_lc] = section  # preserve first seen casing

                text_data = item.get("text", {})
                coordinates = text_data.get("coordinates", [{}])

                if isinstance(coordinates, list) and coordinates and any(coordinates[0].values()):
                    coord = coordinates[0]
                    meta_data = [{
                        "file_name": item.get("file_name"),
                        "page_no": text_data.get("page"),
                        "coordinates": {
                            "Width": coord.get("Width"),
                            "Height": coord.get("Height"),
                            "Left": coord.get("Left"),
                            "Top": coord.get("Top"),
                        }
                    }]
                else:
                    meta_data = []

                chunk_id = text_data.get("name") or f"{section_lc}_{text_data.get('page', '')}_{len(result_conditions[section_lc])}"

                # Merge actionable fields with safe fallbacks
                actionable = {}
                if idx < len(actionable_results):
                    actionable = actionable_results[idx]

                # Only keep the required keys in extracted_data with safe defaults
                extracted_data = actionable.get("extracted_data") or {}
                extracted_data_final = {
                    "trigger": extracted_data.get("trigger", "Prior to construction"),
                    "action_required": extracted_data.get("action_required", "Submit required documentation"),
                    "responsible_party": extracted_data.get("responsible_party", "Developer"),
                    "deliverable": extracted_data.get("deliverable", "Required permits and documentation"),
                    "enforcing_department": extracted_data.get("enforcing_department", "Planning and Economic Growth Department"),
                    "validating_department": extracted_data.get("validating_department", "Development Review Division"),
                }

                # Create the condition object with all required fields
                condition_obj = {
                    "point": text_data.get("data", "").strip(),
                    "chunk_id": chunk_id,
                    "meta_data": meta_data,
                    "is_actionable": actionable.get("is_actionable", True),
                    "extracted_data": extracted_data_final,
                    "record_id": actionable.get("record_id", "REC25-00000-0002C")
                }

                # Add accela_fields if available (can be None, empty list, or populated list)
                if "accela_fields" in actionable:
                    condition_obj["accela_fields"] = actionable["accela_fields"]
                else:
                    # If no accela_fields key, set to None
                    condition_obj["accela_fields"] = None

                result_conditions[section_lc].append(condition_obj)
            except Exception as e:
                print(f"Error processing item {idx}: {str(e)}")
                # Continue with the next item
                continue

        # Only include non-empty categories in mpud_info, using original casing
        result_conditions = dict(result_conditions)
        mpud_info_conditions = [section_casing_map[k] for k, v in result_conditions.items() if v]

        print(f"Successfully processed {len(result_conditions)} sections with conditions")

        return {
            "mpud_info": {
                "conditions": mpud_info_conditions
            },
            "conditions": result_conditions
        }


def transform_conditions_to_mpud_format(data):
    """
    Transforms a dict with a 'conditions' key into the new format:
    {
        "mpud_info": {
            "conditions": [list of non-empty categories]
        },
        "conditions": { ... }
    }
    Ignores any top-level keys except 'conditions'.
    """
    conditions = data.get("conditions", {})
    if not isinstance(conditions, dict):
        raise ValueError("Input data must have a 'conditions' key with a dictionary value.")
    non_empty_categories = [k for k, v in conditions.items() if v]
    return {
        "mpud_info": {
            "conditions": non_empty_categories
        },
        "conditions": conditions
    }
