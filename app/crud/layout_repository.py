from dataclasses import dataclass
from app.database.base_graph_db import BaseGraphDB
import uuid


@dataclass
class LayoutRepository:
    user: dict
    db: BaseGraphDB

    def _create_layout_node(self, data):
        layout_id = str(uuid.uuid4())
        layout = self.db.create_node("layout", {**data, "id": layout_id})
        return layout

    def create_layout(self, application_id, body):
        application_node = self.db.get_one_node(
            label="application",
            properties={"id": application_id},
        )
        if not application_node:
            raise ValueError("application node not found")
        layout_node = self._create_layout_node(body)
        self.db.create_edge(application_node["node_id"], layout_node["node_id"], "HAS")
        return layout_node

    def get_all_layout(self, application_id):
        layouts = self.db.get_nodes_with_parent(
            node_label="layout",
            parent_label="application",
            parent_properties={"id": application_id},
            relation="HAS",
        )
        return layouts
