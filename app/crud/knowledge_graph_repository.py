from app.crud.mongo.base_repository import BaseRepository
import json
from typing import Dict, List
import traceback
from app.crud.neo4j.Neo4jInterface import Neo4JInterface
from datetime import datetime
import time
from app.crud.DocumentAnalysis import BlocksProcessor
import boto3
from app.core.config import settings
from app.crud.neo4j.neo4j_crud import get_one_node_data, update_node_prop
from app.crud.graph_generator import convert_document_to_title_header_text
from app.crud.textract_s3_interface import upload_obj_to_s3, get_textract_obj_from_s3
import re

with open("app/crud/neo4j/desc.json") as file:
    desc_node_map = json.load(file)


class KnowledgeGraphRepository:
    def __init__(self, db):
        self.status_repo = BaseRepository("files_status")
        self.data_repo = BaseRepository("files_data")
        self.documents_repo = BaseRepository("documents")
        self.db = db
        self.neo4j_interface = Neo4JInterface(
            settings.NEO4J_URI, settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD,
        )
        self.textract_client = boto3.client(
            "textract",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID_S3_Textract,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY_S3_Textract,
            region_name=settings.AWS_REGION,
        )
        self.s3Client = boto3.client(
            "s3",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_REGION,
        )
        self.driver = self.neo4j_interface.get_driver()
        self.blocks_processor = BlocksProcessor([])

    def __create_county_node(self, county: str):
        print("Creating county node for: %s", county)
        docs_count = self.neo4j_interface.get_document_count_by_county(
            county.capitalize(),
        )
        docs_count += 1
        with self.driver.session() as session:
            session.run(
                """
                MERGE (root:PDF_ROOT {name: $root_name, description: $root_desc, level: $level})
                MERGE (c:COUNTY {name: $county_name,description: $county_desc, totalDocuments: toInteger($documents_count), info: $county_info, level: $county_level})
                MERGE (root)-[:HAS]->(c)
            """,
                root_name="PDF_ROOT",
                root_desc=desc_node_map["PDF_ROOT"]["desc"],
                level=desc_node_map["PDF_ROOT"]["level"],
                county_name=county.capitalize(),
                county_desc=desc_node_map["COUNTY"]["desc"],
                documents_count=docs_count,
                county_info=county,
                county_level=desc_node_map["COUNTY"]["level"],
            )
        root = get_one_node_data("PDF_ROOT")
        county_list = root.properties.get("countyList", [])
        if county.capitalize() not in county_list:
            update_node_prop(
                root,
                "countyList",
                root.properties.get("countyList", []) + [county.capitalize()],
            )
        print("County node created successfully!")

    def _update_files_status(
        self, pdf_bucket: str, pdf_path: str, update_data: dict,
    ) -> bool:
        try:
            found_doc = self.status_repo.get_one_by_filter(
                {
                    "bucket_name": pdf_bucket,
                    "file_name": pdf_path,
                    "status.ocr.status": 2,
                },
                self.db,
            )
            if found_doc:
                self.status_repo.update_one_by_query_upsert(
                    {"bucket_name": pdf_bucket, "file_name": pdf_path},
                    update_data,
                    self.db,
                )
                print(
                    "File already loaded: %s/%s in collection", pdf_bucket, pdf_path,
                )
                return (False, found_doc, [], [])
            else:
                self.status_repo.update_one_by_query_upsert(
                    {"bucket_name": pdf_bucket, "file_name": pdf_path},
                    update_data,
                    self.db,
                )
                print(
                    "Upserted record for file: %s/%s in collection %s",
                    pdf_bucket,
                    pdf_path,
                )

            return (True, {}, [], [])
        except Exception as e:
            print("mongo error" + e, "".join(traceback.format_exc()))
            return False

    def _update_files_data(
        self, pdf_bucket: str, pdf_path: str, update_data: dict,
    ) -> bool:
        try:

            self.data_repo.update_one_by_query_upsert(
                {"filePath": f"{pdf_bucket}/{pdf_path}"}, update_data, self.db)
            print(
                "Upserted record for file: %s/%s in collection %s",
                pdf_bucket,
                pdf_path,
            )

            return True
        except Exception as e:
            print("mongo error" + e, "".join(traceback.format_exc()))
            return False

    def __get_textract_job_status(self, job_id: str) -> tuple[str, Dict[str, any]]:
        response = self.textract_client.get_document_analysis(
            JobId=job_id,
        )
        return response["JobStatus"], response

    def __get_textract_tokens(self, token: str, job_id: str) -> dict:
        resp = self.textract_client.get_document_analysis(
            JobId=job_id,
            NextToken=token,
        )
        return resp

    def __create_document_node(self, pdf_path: str, total_pages: int, county: str, username: str):
        # 2 create document node
        print("Creating document node for: %s", pdf_path)
        filename = pdf_path.split("/")[-1]
        upload_datetime = datetime.now()

        with self.driver.session() as session:
            session.run(
                """
                MATCH (c:COUNTY {name: $county})
                MERGE (d:DOCUMENT {name: $filename, description: $document_desc, totalPages: toInteger($total_pages), s3Url: $pdf_path, uploadDatetime: datetime($upload_datetime), username: $username, level: $document_level})
                MERGE (c)-[:HAS]->(d)
            """,
                name=filename,
                document_desc=desc_node_map["DOCUMENT"]["desc"],
                county=county.capitalize(),
                filename=filename,
                total_pages=total_pages,
                pdf_path=pdf_path,
                upload_datetime=upload_datetime,
                username=username,
                document_level=desc_node_map["DOCUMENT"]["level"],
            )
        print("Document node created successfully!")
        return filename

    def __read_pdf(self, pdf_bucket: str, pdf_path: str, county: str, user_name: str) -> List[range]:
        try:
            print("Reading PDF: %s", pdf_path)

            pages = {}
            blocks = []
            response = self.textract_client.start_document_analysis(
                DocumentLocation={
                    "S3Object": {
                        "Bucket": pdf_bucket,
                        "Name": pdf_path,
                    },
                },
                FeatureTypes=["LAYOUT"],
            )  # 'FORMS', 'TABLES'
            job_id = response["JobId"]
            while True:
                status, response = self.__get_textract_job_status(job_id)
                if status in ["SUCCEEDED", "FAILED"]:
                    blocks.extend(response["Blocks"])
                    token = None
                    if "NextToken" in response and response["NextToken"]:
                        token = response["NextToken"]
                        while token:
                            resp = self.__get_textract_tokens(token, job_id)
                            token = resp.get("NextToken")
                            blocks.extend(resp["Blocks"])

                    break
                time.sleep(5)  # Wait for 5 seconds before polling again

            if status == "SUCCEEDED":
                print("PDF extracted successfully!")
                total_pages = 0
                context = ""
                current_page = None

                for block in blocks:
                    page_num = block["Page"]

                    if int(page_num) > total_pages:
                        total_pages = int(page_num)

                    if current_page is None or page_num != current_page:
                        context = ""
                        current_page = page_num

                    if block["BlockType"] == "LINE":
                        context += block["Text"]
                    pages[str(page_num)] = context
                    pages["fileName"] = pdf_path

                print("PDF has %d pages", total_pages)
                self.blocks_processor.update_blocks(blocks)

                key_map, value_map, block_map, table_blocks, layout_fig_map = (
                    self.blocks_processor.get_kv_map()
                )
                kvp = self.blocks_processor.get_kv_relationship()
                table_data = self.blocks_processor.extract_tables()

                layout_data = self.blocks_processor.layout_fig()
                combined_layout_data = self.blocks_processor.extract_layout(layout_data)

                document_name = self.__create_document_node(pdf_path, total_pages, county, user_name)
                return (
                    pages,
                    document_name,
                    total_pages,
                    blocks,
                    kvp,
                    table_data,
                    layout_data,
                    combined_layout_data,
                )
        except Exception as e:
            print('error __read_pdf ', e)

    def check_file_in_mongodb(self, file_key):
        try:
            querry = {}
            querry["file_name"] = file_key
            querry["status.ocr.status"] = 2
            resp = self.status_repo.get_one_by_projection(
                querry, {"file_name": 1}, self.db,
            )
            if resp:
                return True
            else:
                return False
        except BaseException:
            return False

    def loop_files_in_s3Bucket(self, s3Bucket: str, pdf_path: str, county: str, user_name: str, user_upload: bool, documents_id) :
        try :
            folder_prefix = 'MPUD Records/'
            response = self.s3Client.list_objects_v2(Bucket=s3Bucket, Prefix=folder_prefix)
            if 'Contents' in response :
                for obj in response['Contents'] :
                    file_key = obj['Key']
                    if not self.check_file_in_mongodb(file_key) :
                        self.process_pdf(s3Bucket, file_key, county, user_name, user_upload, documents_id)
        except BaseException:
            return {"message": "Error in fetching file from s3 Bucket"}

    def process_data(self, pdf_bucket: str, pdf_path: str, county: str, user_name: str, single_file: bool, user_upload: bool, documents_id) -> str:

        if documents_id:
            self.documents_repo.update_one_by_query_upsert({"_id": documents_id}, {'status': 'Picked for OCR'}, self.db)
        if not single_file :
            resp = self.loop_files_in_s3Bucket(pdf_bucket, pdf_path, county, user_name, user_upload, documents_id)
        else:
            resp = self.process_pdf(pdf_bucket, pdf_path, county, user_name, user_upload, documents_id)
        return resp

    def get_job_status(self, job_id):
        response = self.textract_client.get_document_text_detection(
            JobId=job_id,
        )
        return response['JobStatus'], response

    def tokens(self, token, job_id):
        resp = self.textract_client.get_document_text_detection(
            JobId=job_id,
            NextToken=token,
        )
        return resp

    def is_mpud(self, pdf_bucket, pdf_path):
        try:
            if self.status_repo.get_one_by_projection({"file_name": pdf_path, "bucket_name": pdf_bucket, 'mpudFile': False}, {'_id': 1}, self.db) :
                print(f'{pdf_bucket}/{pdf_path} File is not a MPUD document')
                return False
            data = self.data_repo.get_one_by_projection({"filePath": f'{pdf_bucket}/{pdf_path}', 'mpudFile': False}, {"_id": 0}, self.db)
            if not data :
                data = {}
                blocks = []
                response = self.textract_client.start_document_text_detection(
                    DocumentLocation={
                        'S3Object': {
                            'Bucket': pdf_bucket,
                            'Name': pdf_path,
                        },
                    })
                job_id = response['JobId']
                while True:
                    status, response = self.get_job_status(job_id)
                    if status in ['SUCCEEDED', 'FAILED']:
                        blocks.extend(response['Blocks'])
                        token = None
                        if "NextToken" in response and response["NextToken"]:
                            token = response["NextToken"]
                            while (token):
                                resp = self.tokens(token, job_id)
                                token = resp.get('NextToken')
                                blocks.extend(resp['Blocks'])

                        break
                    print('sleep for 10 seconds')
                    time.sleep(10)  # Wait for 10 seconds before polling again

                if status == 'SUCCEEDED':
                    context = ''
                    current_page = None

                    for block in blocks:
                        page_num = block['Page']

                        if current_page is None or page_num != current_page:
                            context = ''
                            current_page = page_num

                        if block['BlockType'] == 'LINE':
                            context += block['Text']
                        data[str(page_num)] = context
                        # data['fileName'] = document_name
                    data['blocks'] = blocks
                else:
                    raise Exception('Error in start_document_text_detection')
            if data :
                self.data_repo.update_one_by_query_upsert({"filePath": f'{pdf_bucket}/{pdf_path}'}, data, self.db)

            pattern_conditions = re.compile('Conditions of Approval', re.IGNORECASE)
            pattern_master_planned = re.compile('MASTER PLANNED UNIT DEVELOPMENT', re.IGNORECASE)
            combined_text = ' '.join(str(value) for key, value in data.items() if isinstance(value, str))
            if pattern_conditions.search(combined_text) and pattern_master_planned.search(combined_text):
                self.status_repo.update_one_by_query_upsert({"file_name": pdf_path, "bucket_name": pdf_bucket}, {"status.ocr.status": 2}, self.db)
                return True
            else:
                self.status_repo.update_one_by_query_upsert({"file_name": pdf_path, "bucket_name": pdf_bucket}, {'mpudFile': False, "status.ocr.status": 2}, self.db)
                print(f'{pdf_bucket}/{pdf_path} File is not a MPUD document')
                return False

        except Exception as e:
            print('error : is_mpud', e)
            print(''.join(traceback.format_exc()))
            return False

    def process_pdf(self, pdf_bucket: str, pdf_path: str, county: str, user_name: str, user_upload: bool, documents_id) -> str:
        print("Starting to process PDF: %s", pdf_path)
        try:
            if user_upload or self.is_mpud(pdf_bucket, pdf_path):
                self.__create_county_node(county)
                update_resp, found_rec, blocks, layout_data = self._update_files_status(
                    pdf_bucket, pdf_path, {"status.ocr.status": 0},
                )
                if documents_id:
                    self.documents_repo.update_one_by_query_upsert({"_id": documents_id}, {'status': 'OCR in progress. It may take a while'}, self.db)
                textract_resp = get_textract_obj_from_s3(pdf_bucket, pdf_path)
                if textract_resp:
                    print(
                        "File already processed: %s/%s in collection %s",
                        pdf_bucket,
                        pdf_path,
                    )
                    (
                        pages,
                        document_name,
                        total_pages,
                        blocks,
                        kvp,
                        organized_table_data,
                        layout_data,
                        combined_layout_data,
                    ) = (
                        textract_resp["pages"],
                        textract_resp["document_name"],
                        textract_resp["total_pages"],
                        textract_resp["kvp"],
                        textract_resp["blocks"],
                        textract_resp["organized_table_data"],
                        textract_resp["layout_data"],
                        textract_resp["combined_layout_data"],
                    )
                    document_name = self.__create_document_node(
                        pdf_path, total_pages, county, user_name,
                    )
                else:
                    (
                        pages,
                        document_name,
                        total_pages,
                        blocks,
                        kvp,
                        organized_table_data,
                        layout_data,
                        combined_layout_data,
                    ) = self.__read_pdf(pdf_bucket, pdf_path, county, user_name)
                    upload_obj_to_s3(
                        pdf_bucket,
                        pdf_path,
                        {
                            "pages": pages,
                            "document_name": document_name,
                            "total_pages": total_pages,
                            "blocks": blocks,
                            "kvp": kvp,
                            "organized_table_data": organized_table_data,
                            "layout_data": layout_data,
                            "combined_layout_data": combined_layout_data,
                        },
                    )
                file_data = {
                    "filePath": f"{pdf_bucket}/{pdf_path}",
                    "totalPages": total_pages,
                    "layout_data": layout_data,
                }
                if blocks:
                    file_data["blocks"] = blocks

                self._update_files_data(
                    pdf_bucket,
                    pdf_path,
                    file_data,
                )
                self._update_files_status(
                    pdf_bucket,
                    pdf_path,
                    {
                        "status.ocr.status": 1,
                        "totalPages": total_pages,
                        "key_val_pair": kvp,
                        "table_data": organized_table_data,
                        "combined_layout_data": combined_layout_data,
                    },
                )
                if documents_id :
                    self.documents_repo.update_one_by_query_upsert({"_id": documents_id}, {'status': 'Creating the Knowledge Graph'}, self.db)
                convert_document_to_title_header_text(document_name, layout_data, pdf_path)

                self._update_files_status(
                    pdf_bucket,
                    pdf_path,
                    {
                        "totalPages": total_pages,
                        "key_val_pair": kvp,
                        "table_data": organized_table_data,
                        "combined_layout_data": combined_layout_data,
                        "inserted_time": datetime.now(),
                        "status.ocr.status": 2,
                    },
                )
                print("PDF processing completed successfully")
                if documents_id:
                    self.documents_repo.update_one_by_query_upsert({"_id": documents_id}, {'status': 'Ready to use'}, self.db)

                return True

            else:
                print(f'{pdf_bucket}/{pdf_path} : Not a MPUD document')
                return False

        except Exception as e:
            print("Error processing PDF: %s", str(e))
            if documents_id:
                self.documents_repo.update_one_by_query_upsert({"_id": documents_id}, {'status': str(e)}, self.db)
            return json.dumps({"error": f"Error processing PDF: {str(e)}"})
        finally:
            print("Closing connection...")
            self.driver.close()
            print("Neo4j connection closed")
            return False
