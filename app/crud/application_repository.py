from dataclasses import dataclass
from datetime import datetime
from app.database.base_graph_db import BaseGraphDB
import uuid
from app.models.application import ApplicationView, ApplicationInsert
import json


@dataclass
class ApplicationRepository:
    user: dict
    db: BaseGraphDB

    def _get_user_node(self):
        user = self.db.has_node("user", {"id": self.user["cognito:username"]})
        # user = None
        if not user:
            user = self.db.create_node("user", {"id": self.user["cognito:username"]})
        return user

    def _create_application_node(self, data):
        application_id = str(uuid.uuid4())
        application = self.db.create_node("application", {**data, "id": application_id})
        return application

    def create_application(self, body):
        user = self._get_user_node()
        application = self._create_application_node(body)
        self.db.create_edge(user["node_id"], application["node_id"], "HAS")
        return application

    def create_application_with_discussion_assignment(
        self, body, chat_id, discussion_id
    ):
        application = self._create_application_node(
            ApplicationInsert(
                **{"_".join([e.lower() for e in k.split()]): v for k, v in body["mpud_info"].items()},
                chat_id=str(chat_id),
                user_name=self.user["name"],
            ).model_dump()
        )
        self.db.create_edge(discussion_id, application["node_id"], "ASSIGNED_TO")
        return application

    def update_application_with_discussion_assignment(
        self, body, chat_id
    ):
        properties = {"_".join([e.lower() for e in k.split()]): v for k, v in body["mpud_info"].items()}

        properties.update({'user_name': self.user["name"], 'chat_id': str(chat_id)})
        set_clause = ", ".join([f"m.{key} = ${key}" for key in properties.keys()])

        query = f"""MATCH (n:DiscussionRoot)-[r:ASSIGNED_TO]->(m:application)
        WHERE id(n) = {int(chat_id)}
        SET {set_clause}
        RETURN m"""

        result = self.db.execute_query(
            query, properties=properties
        )

        if result:
            application = result[0].get('m', {})
            application['node_id'] = chat_id
            return application

        return None

    def get_all_applications(self):
        # query = """
        # MATCH (u:user {id: $user_id})
        # MATCH (u)-[:HAS_CHILD]->(:DiscussionRoot)-[:HAS_CHILD]->(:Discussion)-[:ASSIGNED_TO]->(app:application)
        # RETURN app
        # """

        query = """
        MATCH (u:user {id: $user_id})
        MATCH (u)-[:HAS_CHILD]->(:DiscussionRoot)-[:ASSIGNED_TO]->(app:application)
        RETURN app
        """
        applications = self.db.execute_query(
            query, properties={"user_id": self.user["cognito:username"]}
        )

        return [ApplicationView(**app["app"]) for app in applications]

    def extract_title(self, mpud_info):
        """Extract the title from the mpud_info field."""
        try:
            info = json.loads(mpud_info)
            return info.get("Title", "Untitled Application")
        except Exception:
            return "Untitled Application"

    def extract_description(self, mpud_info):
        """Extract the description from the mpud_info field."""
        try:
            info = json.loads(mpud_info)
            return info.get("Description", "No description available")
        except Exception:
            return "No description available"

    def update_application_status(self, application_id, status):
        query = """
        MATCH (app:application {id: $application_id})
        SET
            app.status = $status,
            app.last_modified = $timestamp
        RETURN app
        """
        timestamp = datetime.now().isoformat()
        updatedApplication = self.db.execute_query(query,
                                                   properties={
                                                       "application_id": application_id,
                                                       "status": status,
                                                       "timestamp": timestamp}
                                                   )

        return ApplicationView(**updatedApplication[0]["app"])
