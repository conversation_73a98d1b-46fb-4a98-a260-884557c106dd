import boto3
from io import BytesIO
from fastapi import HTTPException
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import Paragraph, Spacer, Frame, Table, TableStyle, KeepTogether
from reportlab.lib.enums import TA_JUSTIFY, TA_LEFT, TA_CENTER, TA_RIGHT
from reportlab.platypus.doctemplate import BaseDocTemplate, PageTemplate
from reportlab.pdfgen import canvas
from reportlab.lib.utils import ImageReader
from textwrap import wrap
from datetime import datetime
from PyPDF2 import PdfMerger
from app.core.config import Settings
from app.core.telemetry import get_logger

settings = Settings()
logger = get_logger("pasco.ai.connector")


class MPUDDocTemplate(BaseDocTemplate):
    def __init__(self, filename, **kwargs):
        # Extract custom parameters
        self.petition_no = kwargs.pop("petition_no", None)
        self.bcc_date = kwargs.pop("bcc_date", None)
        self.drc_date = kwargs.pop("drc_date", None)
        self.revision_date = kwargs.pop("revision_date", None)
        self.total_pages = kwargs.pop("total_pages", 0)

        # Ensure adequate space for footer
        if "bottomMargin" in kwargs:
            kwargs["bottomMargin"] = max(kwargs["bottomMargin"], 1.5 * inch)
        else:
            kwargs["bottomMargin"] = 1.5 * inch

        # Initialize the parent class
        BaseDocTemplate.__init__(self, filename, **kwargs)
        self.addPageTemplates(self._create_page_template())

    def _create_page_template(self):
        # Create a frame with adequate space for the footer
        frame = Frame(
            self.leftMargin,
            self.bottomMargin + 0.5 * inch,  # Raise the bottom of the frame
            self.width,
            self.height - 1.0 * inch,  # Reduce frame height for footer
            id="normal",
        )
        template = PageTemplate(
            id="footer_page_template", frames=frame, onPage=self._add_page_number
        )
        return template

    def _add_page_number(self, canvas, doc):
        canvas.saveState()
        canvas.setFont("Helvetica", 9)

        # Draw a white rectangle in the footer area to ensure visibility
        canvas.setFillColor("white")
        canvas.rect(0.5 * inch, 0.5 * inch, 7.5 * inch, 0.7 * inch, fill=1, stroke=0)
        canvas.setFillColor("black")

        # Page number (centered) - add 1 to account for the first page
        page_num = doc.page + 1  # Add 1 because the first page is created separately
        canvas.drawCentredString(
            4.25 * inch, 0.75 * inch, f"Page {page_num} of {self.total_pages}",
        )

        # Revision date (right-aligned)
        if self.revision_date and not self.bcc_date and not self.drc_date:
            canvas.drawRightString(8 * inch, 0.75 * inch, f"Rev {self.revision_date}")

        if self.petition_no:
            # Petition number (centered below page number, if provided)
            canvas.drawCentredString(
                4.25 * inch, 0.55 * inch, f"Petition No. {self.petition_no}",
            )

        # BCC and DRC dates (only if needed, positioned below revision date)
        y_position = 1.0 * inch
        if self.bcc_date:
            canvas.drawRightString(8 * inch, y_position, f"BCC {self.bcc_date}")
            y_position -= 0.2 * inch
        if self.drc_date:
            canvas.drawRightString(8 * inch, y_position, f"DRC {self.drc_date}")
            y_position -= 0.2 * inch
        if self.revision_date and (self.bcc_date or self.drc_date):
            canvas.drawRightString(8 * inch, y_position, f"Rev {self.revision_date}")

        canvas.restoreState()


def create_first_page(mpud_info, chat_id):
    """Create first page of the MPUD document with logo and metadata"""
    buffer = BytesIO()
    c = canvas.Canvas(buffer, pagesize=A4)
    width, height = A4

    # Define common measurements
    top_margin = 25
    logo_height = 60
    logo_width = 60

    # Add logo and title in a compact layout
    logo_path = "app/assets/Logo_Sky.png"  # Adjust path as needed

    try:
        logo = ImageReader(logo_path)
        logo_y = height - top_margin - logo_height
        c.drawImage(logo, 60, logo_y, width=logo_width, height=logo_height, mask="auto")
    except Exception as e:
        logger.warning(f"Could not load logo: {e}")

    # Title section
    title_y = height - top_margin - 20
    c.setFont("Helvetica-Bold", 12)
    c.drawCentredString(width / 2, title_y, "PLANNING AND DEVELOPMENT")
    c.drawCentredString(width / 2, title_y - 20, "DEPARTMENT")
    c.drawCentredString(width / 2, title_y - 40, "INTEROFFICE MEMORANDUM")

    # First horizontal line
    first_line_y = title_y - 50
    c.setLineWidth(1)
    c.line(50, first_line_y, width - 50, first_line_y)

    # Metadata section
    metadata_y = first_line_y - 25
    c.setFont("Helvetica-Bold", 10)

    # Commission District (if available in mpud_info)
    c.drawString(50, metadata_y, "COMMISSION DISTRICT:")
    c.setFont("Helvetica", 10)
    c.drawString(175, metadata_y, str(mpud_info.get("commission_district", "_")))

    # File No.
    c.setFont("Helvetica-Bold", 10)
    c.drawString(275, metadata_y, "FILE NO.:")
    c.setFont("Helvetica", 10)
    current_year = datetime.now().year % 100  # Get last 2 digits of current year
    file_no = f"PDD{current_year:02d}_{str(mpud_info.get('chat_id', chat_id))}"
    c.drawString(325, metadata_y, file_no)

    # Date
    c.setFont("Helvetica-Bold", 10)
    c.drawString(475, metadata_y, "DATE:")
    c.setFont("Helvetica", 10)
    current_date = datetime.now().strftime("%m/%d/%y")
    c.drawString(510, metadata_y, current_date)

    def draw_wrapped_text(text, x, y, width, font_name, font_size):
        """Helper function to draw wrapped text"""
        c.setFont(font_name, font_size)
        wrapped_text = wrap(text, width=80)
        y_offset = y
        for line in wrapped_text:
            c.drawString(x, y_offset, line)
            y_offset -= 15
        return y_offset + 15

    # Subject section with multiline support
    y_position = metadata_y - 40
    c.setFont("Helvetica-Bold", 10)
    c.drawString(50, y_position, "SUBJECT:")

    # Build subject text from components
    subject_components = [
        mpud_info.get("mpud category", ""),
        mpud_info.get("title", ""),
        mpud_info.get("developer", ""),
        mpud_info.get("request", ""),
    ]
    # Filter out empty components and join with dashes
    subject_text = " - ".join(filter(None, subject_components))
    y_position = draw_wrapped_text(subject_text, 120, y_position, 80, "Helvetica", 10)

    # References section
    y_position -= 25
    c.setFont("Helvetica-Bold", 10)
    c.drawString(50, y_position, "REFERENCE:")

    references_text = mpud_info.get("reference", "")
    y_position = draw_wrapped_text(
        references_text, 125, y_position, 80, "Helvetica", 10
    )

    # THRU/FROM section
    y_position -= 25
    c.setFont("Helvetica-Bold", 10)
    c.drawString(50, y_position, "THRU:")
    c.setFont("Helvetica", 10)
    c.drawString(100, y_position, "To be filled by the officials later.")

    y_position -= 25
    c.setFont("Helvetica-Bold", 10)
    c.drawString(50, y_position, "FROM:")
    c.setFont("Helvetica", 10)
    c.drawString(100, y_position, "To be filled by the officials later.")

    # Horizontal line
    c.line(50, y_position - 15, width - 50, y_position - 15)

    # Recommended Board Action
    y_position -= 40
    c.setFont("Helvetica-Bold", 10)
    c.drawString(50, y_position, "RECOMMENDED DEPARTMENT ACTION:")
    y_position -= 20
    c.setFont("Helvetica", 10)
    c.drawString(
        50,
        y_position,
        "Contents for this section will have to be filled by the user later.",
    )

    # Background Summary
    y_position -= 30
    c.setFont("Helvetica-Bold", 10)
    c.drawString(50, y_position, "BACKGROUND SUMMARY/ALTERNATIVE ANALYSIS:")
    y_position -= 20
    c.setFont("Helvetica", 10)
    c.drawString(
        50,
        y_position,
        "Contents for this section will have to be filled by the user later.",
    )

    # Add page number footer
    c.saveState()
    c.setFont("Helvetica", 9)

    # Draw a white rectangle in the footer area to ensure visibility
    c.setFillColor("white")
    c.rect(0.5 * inch, 0.5 * inch, 7.5 * inch, 0.7 * inch, fill=1, stroke=0)
    c.setFillColor("black")

    # Get petition number if available
    petition_no = mpud_info.get("petition_no")
    bcc_date = mpud_info.get("bcc_date")
    drc_date = mpud_info.get("drc_date")
    revision_date = datetime.now().strftime("%m/%d/%Y")
    total_pages = mpud_info.get("total_pages", "1+")  # Get total pages if available

    # Page number (centered) - first page is always page 1
    c.drawCentredString(4.25 * inch, 0.75 * inch, f"Page 1 of {total_pages}")

    # Revision date (right-aligned)
    if revision_date and not bcc_date and not drc_date:
        c.drawRightString(8 * inch, 0.75 * inch, f"Rev {revision_date}")

    # Petition number (centered below page number, if provided)
    if petition_no:
        c.drawCentredString(4.25 * inch, 0.55 * inch, f"Petition No. {petition_no}")

    # BCC and DRC dates (only if needed, positioned below revision date)
    y_position = 1.0 * inch
    if bcc_date:
        c.drawRightString(8 * inch, y_position, f"BCC {bcc_date}")
        y_position -= 0.2 * inch
    if drc_date:
        c.drawRightString(8 * inch, y_position, f"DRC {drc_date}")
        y_position -= 0.2 * inch
    if revision_date and (bcc_date or drc_date):
        c.drawRightString(8 * inch, y_position, f"Rev {revision_date}")

    c.restoreState()

    c.showPage()
    c.save()

    buffer.seek(0)
    return buffer


def create_pdf(data, chat_id=None, application_id=None):
    """
    Create a PDF document with MPUD conditions of approval.

    Args:
        data: Dictionary containing document content and MPUD information
             in the format {'mpud_info': {...}, 'conditions': {...}}
        chat_id: Optional chat ID for file naming
        application_id: Optional application ID for updating S3 location

    Returns:
        BytesIO: Buffer containing the generated PDF
    """
    # Create a BytesIO object to hold the PDF content
    main_content_buffer = BytesIO()

    current_date = datetime.now().strftime("%m/%d/%Y")

    # Extract MPUD info
    mpud_info = data.get("mpud_info", {})
    petition_no = mpud_info.get("petition_no")
    bcc_date = mpud_info.get("bcc_date")
    drc_date = mpud_info.get("drc_date")

    # First pass to get total page count of main content
    dummy_buffer = BytesIO()
    doc = MPUDDocTemplate(
        dummy_buffer,
        pagesize=A4,
        rightMargin=0.5 * inch,
        leftMargin=0.5 * inch,
        topMargin=0.5 * inch,
        bottomMargin=1.5 * inch,
    )

    story = create_story(data)
    doc.build(story)

    # Get the main content page count
    main_content_pages = doc.page
    # Total pages includes the first page
    total_pages = main_content_pages + 1

    # Update mpud_info with total page count for the first page
    mpud_info["total_pages"] = total_pages

    # Create first page with total page count
    first_page_buffer = create_first_page(mpud_info, chat_id)

    # Second pass to build the main content with correct page numbers
    main_content_buffer.seek(0)
    main_content_buffer.truncate(0)

    # Build final document with correct page numbers
    doc = MPUDDocTemplate(
        main_content_buffer,
        pagesize=A4,
        rightMargin=0.5 * inch,
        leftMargin=0.5 * inch,
        topMargin=0.5 * inch,
        bottomMargin=1.5 * inch,
        petition_no=petition_no,
        bcc_date=bcc_date,
        drc_date=drc_date,
        revision_date=current_date,
        total_pages=total_pages,
    )

    story = create_story(data)
    doc.build(story)

    # Reset buffer to the beginning
    first_page_buffer.seek(0)
    main_content_buffer.seek(0)

    # Merge PDFs
    output = BytesIO()
    merger = PdfMerger()

    # Add first page
    merger.append(first_page_buffer)

    # Add main content
    merger.append(main_content_buffer)

    # Write to output
    merger.write(output)
    merger.close()
    output.seek(0)

    # Create a copy for S3 upload
    if chat_id:
        s3_buffer = BytesIO(output.getvalue())
        s3_client = boto3.client("s3")
        bucket_name = f"pasco-ocr-files-{settings.STAGE}"
        file_key = f"MPUD-version_control_files/{chat_id}/{datetime.now().strftime('%m-%d-%Y-%H:%M')}.pdf"

        try:
            s3_client.upload_fileobj(s3_buffer, bucket_name, file_key)
            s3_buffer.close()
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

        if application_id:
            from app.utils.application_utils import update_application_pdf_s3_location

            update_application_pdf_s3_location(
                application_id, f"s3://{bucket_name}/{file_key}"
            )

    # Return the PDF buffer
    output.seek(0)
    return output


def create_story(data):
    """
    Create the content for the PDF document based on the provided JSON data structure.

    Args:
        data: Dictionary with 'mpud_info' and 'conditions' keys

    Returns:
        list: List of flowable objects to build the PDF
    """
    story = []

    styles = getSampleStyleSheet()
    styles.add(ParagraphStyle(name="Justify", alignment=TA_JUSTIFY))
    styles.add(ParagraphStyle(name="Left", alignment=TA_LEFT))
    styles.add(ParagraphStyle(name="Center", alignment=TA_CENTER))

    # Title
    title_style = ParagraphStyle(
        "Title", parent=styles["Heading2"], alignment=TA_CENTER
    )

    mpud_info = data.get("mpud_info", {})
    title = f"{mpud_info.get('title', '').upper() if mpud_info.get('title') else ''} MASTER PLANNED UNIT DEVELOPMENT<br/>CONDITIONS OF APPROVAL"

    if mpud_info.get("petition_no"):
        title += f"<br/>PETITION NO. {mpud_info['petition_no']}"

    story.append(Paragraph(title, title_style))
    story.append(Spacer(1, 0.3 * inch))  # Increased spacing after title

    # Process conditions
    conditions_with_points = data.get("conditions", {})
    conditions_of_mpud = mpud_info.get("conditions", [])
    item_number = 1

    sorted_conditions = sort_conditions(conditions_with_points, conditions_of_mpud)

    # Sort headings for consistent order
    for heading, items in sorted_conditions.items():
        if not items:  # Skip empty sections
            continue
        # print("Heading------------->", heading)

        heading_style = ParagraphStyle(
            "SectionHeading",
            fontSize=12,
            parent=styles["Heading2"],
            alignment=TA_CENTER,
        )

        # Format heading (replace underscores, capitalize words)
        formatted_heading = heading.replace("_", " ").title()

        # Handle headings with parentheses
        if formatted_heading.startswith("(") and ")" in formatted_heading:
            close_index = formatted_heading.find(")")
            if close_index < len(formatted_heading) - 1:
                formatted_heading = formatted_heading[close_index + 1 :].strip()
            else:
                formatted_heading = formatted_heading[1:close_index].strip()

        # print("Formatted Heading------------->", formatted_heading)

        # Use HTML-style underlining for the heading
        underlined_heading = f"<u>{formatted_heading}</u>"
        heading_paragraph = Paragraph(underlined_heading, heading_style)
        heading_spacer = Spacer(1, 0.1 * inch)

        # Process items in this section
        section_items = []
        for item in items:
            if not isinstance(item, dict) or "point" not in item:
                continue  # Skip malformed items

            number_style = ParagraphStyle(
                "Number",
                parent=styles["Normal"],
                alignment=TA_RIGHT,
                fontName="Helvetica-Bold",
            )

            content_style = ParagraphStyle(
                "Content",
                parent=styles["Normal"],
                alignment=TA_JUSTIFY,
                leftIndent=0.3 * inch,
            )

            # Get the content from the 'point' field
            content = item["point"].replace(
                '"Unexpired Approvals"', '<b>"Unexpired Approvals"</b>'
            )

            # Create a table with two columns: number and content
            table_data = [
                [
                    Paragraph(f"{item_number}.", number_style),
                    Paragraph(content, content_style),
                ]
            ]

            # Adjust these widths as needed
            t = Table(table_data, colWidths=[0.3 * inch, 6.8 * inch])
            t.setStyle(
                TableStyle(
                    [
                        ("VALIGN", (0, 0), (-1, -1), "TOP"),
                        ("LEFTPADDING", (0, 0), (0, 0), 0),
                        ("RIGHTPADDING", (0, 0), (0, 0), 5),
                        ("LEFTPADDING", (1, 0), (1, 0), 0),
                    ]
                )
            )

            section_items.append(t)
            section_items.append(
                Spacer(1, 0.15 * inch)
            )  # Increased spacing between items
            item_number += 1  # Increment the item number

        # If there are items in this section
        if section_items:
            # Keep heading with at least the first item (if there is one)
            if len(section_items) >= 1:
                # Create a group of heading and first item that will stay together
                first_group = [heading_paragraph, heading_spacer, section_items[0]]
                story.append(KeepTogether(first_group))

                # Add remaining items individually
                for item in section_items[1:]:
                    story.append(item)
            else:
                # No items, just add the heading
                story.append(heading_paragraph)
                story.append(heading_spacer)

    return story


def sort_conditions(conditions_with_points, conditions_of_mpud):
    # Create a lookup mapping from lower-case keys of the dictionary to the original keys.
    lookup = {key.lower(): key for key in conditions_with_points}

    # Build a new dictionary following the order specified in conditions_of_mpud.
    sorted_dict = {}
    for condition in conditions_of_mpud:
        lower_condition = condition.lower()
        if lower_condition in lookup:
            sorted_dict[condition] = conditions_with_points[lookup[lower_condition]]
    return sorted_dict
