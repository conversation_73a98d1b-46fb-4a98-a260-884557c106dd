from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from PIL import Image
import io
import boto3
from botocore.exceptions import ClientError
from app.core.config import Settings
from app.crud.ocr.config import AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION

settings = Settings()

STAGE = settings.STAGE

s3_client = boto3.client(
    "s3",
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    region_name=AWS_REGION,
)

S3_BUCKET_NAME = f'pasco-ocr-files-{STAGE}'
ALLOWED_SIZES = [64, 100, 128, 200, 512]


def get_optimized_image(username: str, size: int):
    # Construct the S3 object key
    object_key = f"profile-images/{username}.webp"

    try:
        # Get the image from S3
        response = s3_client.get_object(Bucket=S3_BUCKET_NAME, Key=object_key)
        image_content = response['Body'].read()
        img = Image.open(io.BytesIO(image_content))
        if size in ALLOWED_SIZES:
            img = img.resize((size, size))
        img_byte_arr = io.BytesIO()
        img.save(img_byte_arr, format='WEBP')
        img_byte_arr.seek(0)

        return img_byte_arr

    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            raise HTTPException(status_code=404, detail="Image not found")
        else:
            raise HTTPException(status_code=500, detail=str(e))


def save_image_to_s3(username: str, image_data: bytes):
    object_key = f"profile-images/{username}.webp"

    try:
        # Open the uploaded image
        img = Image.open(io.BytesIO(image_data))
        img_byte_arr = io.BytesIO()
        img.save(img_byte_arr, format='WEBP', quality=90)
        img_byte_arr.seek(0)
        s3_client.upload_fileobj(img_byte_arr, S3_BUCKET_NAME, object_key)

        return True
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save image: {str(e)}")
