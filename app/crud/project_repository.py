from dataclasses import dataclass
from datetime import datetime
from app.database.base_graph_db import BaseGraphDB
import uuid


@dataclass
class ProjectRepository:
    user: dict
    db: BaseGraphDB

    def _get_user_node(self):
        user = self.db.has_node("user", {"id": self.user["cognito:username"]})
        if not user:
            user = self.db.create_node("user", {"id": self.user["cognito:username"]})
        return user

    def _create_project_node(self, data):
        project_id = str(uuid.uuid4())
        project = self.db.create_node("Project", {**data, "id": project_id})
        return project

    def create_project(self, title: str, creator_name: str):
        user = self._get_user_node()
        project = self._create_project_node(
            {
                "title": title,
                "creator_name": creator_name,
                "created_at": datetime.now().isoformat(),
                "last_modified": datetime.now().isoformat(),
            }
        )
        self.db.create_edge(user["node_id"], project["node_id"], "OWNS")
        return project

    def get_all_projects(self):
        query = """
        MATCH (u:user {id: $user_id})-[:OWNS]->(p:Project)
        RETURN p, ID(p) as project_id
        """
        projects = self.db.execute_query(
            query, properties={"user_id": self.user["cognito:username"]}
        )
        # Add nodeid to each project's properties
        for project in projects:
            project["p"]["project_id"] = project["project_id"]
            del project["project_id"]
        return [projects[i]["p"] for i in range(len(projects))]

    def get_project_by_id(self, project_id: str):
        query = """
        MATCH (p:Project)
        WHERE ID(p) = toInteger($project_id)
        RETURN p, ID(p) as nodeid
        """
        project = self.db.execute_query(query, properties={"project_id": project_id})
        if not project:
            return None
        # Add nodeid to project properties
        project[0]["p"]["nodeid"] = project[0]["nodeid"]
        return project[0]["p"]

    def update_project(self, project_id, data=None):
        """
        Update project using node ID (integer)
        data can be a dictionary containing title, description, assets, etc.
        """
        query = """
        MATCH (p:Project)
        WHERE ID(p) = toInteger($project_id)
        SET p.last_modified = $timestamp
        """
        properties = {"project_id": project_id, "timestamp": datetime.now().isoformat()}

        # Handle dynamic property updates
        if isinstance(data, dict):
            for key, value in data.items():
                query += f", p.{key} = ${key}"
                properties[key] = value
        # Backward compatibility for title/description params
        elif isinstance(data, str):
            query += ", p.title = $title"
            properties["title"] = data

        query += " RETURN p, ID(p) as nodeid"

        updated_project = self.db.execute_query(query, properties=properties)
        if not updated_project:
            return None

        # Add nodeid to project properties
        updated_project[0]["p"]["nodeid"] = updated_project[0]["nodeid"]
        return updated_project[0]["p"]

    def delete_project(self, project_id: str):
        query = """
        MATCH (p:Project)
        WHERE ID(p) = toInteger($project_id)
        DETACH DELETE p
        RETURN count(p) as deleted
        """
        result = self.db.execute_query(query, properties={"project_id": project_id})
        return result[0]["deleted"] > 0

    def get_assets(self, project_id: str):
        query = """
        MATCH (p:Project)
        WHERE ID(p) = toInteger($project_id)
        RETURN p.assets, p.document_ids
        """
        result = self.db.execute_query(query, properties={"project_id": project_id})
        return result[0]["assets"], result[0]["document_ids"]

    def get_chatid(self, project_id: str):
        query = """
        MATCH (p:Project)-[:HAS_DISCUSSION]->(d:DiscussionRoot)
        WHERE ID(p) = toInteger($project_id)
        RETURN id(d) as chat_id
        """
        result = self.db.execute_query(query, properties={"project_id": project_id})
        return result[0] if len(result) > 0 else {}

    def get_project_by_chatid(self, chat_id: str):
        query = """
        MATCH (p:Project)-[:HAS_DISCUSSION]->(d:DiscussionRoot)
        WHERE ID(d) = toInteger($chat_id)
        RETURN p
        """
        result = self.db.execute_query(query, properties={"chat_id": chat_id})
        return result[0]["p"] if result else None
