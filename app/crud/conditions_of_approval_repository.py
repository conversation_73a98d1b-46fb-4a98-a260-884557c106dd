from dataclasses import dataclass
from app.database.base_graph_db import BaseGraphDB
import uuid


@dataclass
class ConditionsOfApprovalRepository:
    user: dict
    db: BaseGraphDB

    def _create_conditions_of_approval_node(self, data):
        application_id = str(uuid.uuid4())
        application = self.db.create_node(
            "conditions_of_approval", {**data, "id": application_id}
        )
        return application

    def create_conditions_of_approval(self, layout_id, body):
        layout_node = self.db.get_one_node("layout", {"id": layout_id})
        if not layout_node:
            raise ValueError("layout node not found")
        condition = self._create_conditions_of_approval_node(body)
        self.db.create_edge(layout_node["node_id"], condition["node_id"], "HAS")
        return body

    def get_all_conditions_of_approval(self, layout_id):
        conditions = self.db.get_nodes_with_parent(
            node_label="conditions_of_approval",
            parent_label="layout",
            parent_properties={"id": layout_id},
            relation="HAS",
        )
        return conditions
