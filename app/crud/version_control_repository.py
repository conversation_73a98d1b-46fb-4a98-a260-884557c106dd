import boto3
from app.crud.mongo.base_repository import BaseRepository as MongoRepo
import io
import uuid
import ast
from app.utils.base_utils import get_embedding_function
from fpdf import FPDF
from datetime import datetime
from bson import ObjectId
from app.crud.ocr.config import AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION
from bs4 import BeautifulSoup, NavigableString
import unicodedata
from app.core.config import Settings

settings = Settings()

s3_client = boto3.client(
    "s3",
    aws_access_key_id=AWS_ACCESS_KEY_ID,
    aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    region_name=AWS_REGION,
)

MPUD_versions_Collection = MongoRepo("mpud_versions")
_chat_history_Collection = MongoRepo("chat_history")


def unicode_to_latin1(text):
    return unicodedata.normalize('NFKD', text).encode('latin-1', 'ignore').decode('latin-1')


class PDF(FPDF):
    def __init__(self):
        super().__init__()
        self.show_header = True

    def header(self):
        if self.page_no() == 1:
            self.set_font("Arial", "B", 14)
            self.cell(0, 10, "MPUD History Report", 0, 1, "C")
            line_y = self.get_y()
            self.set_y(line_y)
            self.set_x(10)
            self.cell(0, 0, "", 0, 1, "L")
            self.line(10, line_y + 5, 200, line_y + 5)
            self.ln(10)
        else:
            self.ln(10)

    def add_rule(self, title, content):
        self.set_font("Arial", "B", 12)
        self.cell(0, 10, unicode_to_latin1(title), 0, 1)
        self.ln(5)
        self.parse_and_write_content(content)
        self.ln(10)

    def parse_and_write_content(self, content):
        soup = BeautifulSoup(content, 'html.parser')
        indent_level = 0
        for element in soup:
            if isinstance(element, NavigableString):
                self.write_styled_text(element, indent_level)
            elif element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                self.write_heading(element)
            elif element.name == 'p':
                self.write_styled_text(element, indent_level)
                self.ln()
            elif element.name in ['ul', 'ol']:
                indent_level += 1
                for idx, li in enumerate(element.find_all('li', recursive=False), start=1):
                    bullet = "-" if element.name == 'ul' else f"{idx}."
                    self.set_x(self.get_x() + indent_level * 4)
                    self.cell(5, 5, bullet)
                    self.write_styled_text(li, indent_level + 1)
                    self.ln()
                indent_level -= 1
            else:
                self.write_styled_text(element, indent_level)

    def write_styled_text(self, element, indent_level, parent_style=""):
        if isinstance(element, NavigableString):
            text = unicode_to_latin1(str(element).strip())
            if text:
                self.set_font("Arial", parent_style, 12)
                indent = " " * (indent_level * 4)
                self.write(5, indent + text)
        else:
            style = parent_style
            if element.name in ['strong', 'b']:
                style += "B" if "B" not in style else ""
            if element.name in ['em', 'i']:
                style += "I" if "I" not in style else ""
            if element.name == 'u':
                style += "U" if "U" not in style else ""
            for child in element.children:
                self.write_styled_text(child, indent_level, style)

    def write_heading(self, element):
        heading_sizes = {
            'h1': 24,
            'h2': 22,
            'h3': 20,
            'h4': 18,
            'h5': 16,
            'h6': 14,
        }
        size = heading_sizes.get(element.name, 12)
        self.set_font("Arial", "B", size)
        self.ln(5)
        self.write(6, unicode_to_latin1(element.get_text()))
        self.ln(10)


pdf = PDF()


def generate_pdf_and_upload_to_s3_task(
    doc_ver_data, os_stage, file_name, county_name, user_id, dis_versions, db,
):
    try:
        pdf_data = generate_pdf(doc_ver_data)
        bucket_name = f"pasco-ocr-files-{os_stage}"
        if dis_versions == []:
            newVersion = 1
        else:
            versionLis = []
            for ver in dis_versions:
                versionLis.append(int(ver.split("_")[-1]))
            versionLis.sort()
            last_version = versionLis[-1]
            newVersion = last_version + 1

        # pdf_data = base64.b64decode(request_dict['fileData'])
        uuidVersion = uuid.uuid4()
        file_key = (
            "MPUD-version_control_files/" + user_id + "/" + county_name + "_county"
            "/" + f"mpud_{county_name}_{str(uuidVersion)}" + ".pdf"
        )
        respS3Put = s3_client.put_object(
            Bucket=bucket_name, Key=file_key, Body=pdf_data,
        )  # ContentType='application/pdf'
        insertedToDB = False

        if (
            "ResponseMetadata" in respS3Put
            and "HTTPStatusCode" in respS3Put["ResponseMetadata"]
            and respS3Put["ResponseMetadata"]["HTTPStatusCode"] == 200
        ):
            resp = MPUD_versions_Collection.create(
                {
                    "county_name": county_name,
                    "user_id": user_id,
                    "version": f"mpud_{file_name}_{str(newVersion)}",
                    "input_file_name": file_name,
                    "S3Location": f"{bucket_name}/{file_key}",
                    "file_name": f"mpud_{file_name}_{str(newVersion)}" + ".pdf",
                    "created_dateTime": datetime.now(),
                },
                db,
            )
            if resp:
                inserted_id = str(resp.inserted_id)
                insertedToDB = True

        if insertedToDB:
            # return {"message" : f"Created version {newVersion}","s3_location":f'{bucket_name}/{file_key}'}
            resp = get_file_data(inserted_id, user_id, db)
            return resp
        else:
            return {"message": "Error in file upload to S3"}
    except Exception as e:
        return {"message": f"Error - {e}"}


def generate_pdf(data):
    try:
        if data :
            for entry in data:
                mpud_history = entry.get("mpud_history", {})
                for history_key, history_list in mpud_history.items():
                    pdf.add_page()
                    for record in history_list:
                        title = record.get("title", "")
                        content = record.get("content", "")
                        pdf.add_rule(title, content)
            pdf_bytes = pdf.output(dest="S").encode("latin-1")
            return pdf_bytes
        else :
            pdf_bytes = pdf.output(dest="S").encode("latin-1")
            return pdf_bytes

    except Exception as e:
        return f"Error occurred while generating PDF {e}"  # Return


def get_mpud_versions(county_name: str, user_id: str, db):
    try:
        resp = MPUD_versions_Collection.get_all_by_projection(
            {"county_name": county_name, "user_id": user_id},
            {"version": 1, "S3Location": 1, "created_dateTime": 1, "_id": 1},
            db,
        )

        for r in resp:
            r["_id"] = str(r["_id"])

        if resp is None:
            return {
                "message": "no versions available for the county and user specified",
            }
        else:
            return {"versions": resp}
    except BaseException:
        return {"message": "Error in getting MPUD versions"}


def get_mpud_document_preview_data(chat_id, user_id, db):
    try:
        resp = _chat_history_Collection.get_all_by_projection(
            {"chat_id": chat_id}, {"document": 1, "timestamp": 1, "_id": 1}, db, sort_key='_id', sort_direction=-1,
        )
        resp_list = []

        for rec in resp:
            for recd in rec["document"]:
                recd["_id"] = str(rec["_id"])
                resp_list.append(recd)

        return resp_list

    except Exception as e:
        return {"message": f"Error:{e}"}


def get_file_data(mpud_versions_id: str, user_id: str, db):
    try:
        is_file_available = MPUD_versions_Collection.get_one_by_projection(
            {"_id": ObjectId(mpud_versions_id), "user_id": user_id},
            {"_id": 1, "S3Location": 1},
            db,
        )

        if is_file_available:
            bucket_name = is_file_available["S3Location"].split("/")[0]
            key_name = "/".join(is_file_available["S3Location"].split("/")[1:])
            try:
                s3_object = s3_client.get_object(Bucket=bucket_name, Key=key_name)
                pdf_binary = s3_object["Body"].read()
                pdf_file_buffer = io.BytesIO(pdf_binary)

                return {
                    "fileData": pdf_file_buffer,
                    "fileName": key_name.split("/")[-1],
                }

            except BaseException:
                return {
                    "message": f"The specific file does not exist in {is_file_available['S3Location']}",
                }
        else:
            return {"message": f"The specific file does not exist with user {user_id}"}

    except BaseException:
        return {"message": "Error in getting MPUD file data"}


def create_files_version(
    county_name: str,
    user_id: str,
    file_name: str,
    chat_history_id,
    _MILVUS_Version_Coll,
    db,
    milvusDB,
):  # request_dict:dict
    try:
        doc_ver_data = get_mpud_version_data(
            chat_history_id, user_id, county_name, _MILVUS_Version_Coll, db, milvusDB,
        )
        os_stage = settings.STAGE
        dis_versions = MPUD_versions_Collection.distinct_key_by_query(
            "version",
            {
                "input_file_name": file_name,
                "county_name": county_name,
                "user_id": user_id,
            },
            db,
        )
        task = generate_pdf_and_upload_to_s3_task.delay(
            doc_ver_data, os_stage, file_name, county_name, user_id, dis_versions,
        )

        return {"message": "Worker running in background", "task_id": task.id}

    except Exception as e:
        return {"message": f"Error in getting MPUD file data: {e}"}


def create_files_version_and_return_blob(
    county_name: str,
    user_id: str,
    file_name: str,
    chat_id,
    _MILVUS_Version_Coll,
    db,
    milvusDB,
):  # request_dict:dict
    try:
        doc_ver_data = get_mpud_version_data(
            chat_id, user_id, county_name, _MILVUS_Version_Coll, db, milvusDB,
        )
        os_stage = settings.STAGE
        dis_versions = MPUD_versions_Collection.distinct_key_by_query(
            "version",
            {
                "input_file_name": file_name,
                "county_name": county_name,
                "user_id": user_id,
            },
            db,
        )
        resp = generate_pdf_and_upload_to_s3_task(
            doc_ver_data, os_stage, file_name, county_name, user_id, dis_versions, db,
        )

        return resp

    except Exception as e:
        return {"message": f"Error in getting MPUD file data: {e}"}


def get_mpud_version_data(
    chat_id, user_id, county_name, _MILVUS_Version_Coll, db, milvusDb,
):
    try:
        resp = _MILVUS_Version_Coll.query(
            ["id", "mpud_history_id", "mpud_history", "user_id", "county_name"],
            f"mpud_history_id == '{chat_id}' && user_id == '{user_id}' && county_name == '{county_name}'",
            milvusDb,
        )
        return resp
    except Exception as e:
        return {"message": f"Error in getting MPUD file data: {e}"}


def delete_rec_in_mpud_version_data(
    chat_id, _MILVUS_Version_Coll, rule_ids, milvusDB, db,
):
    try:
        rec_frm_milvus = _MILVUS_Version_Coll.query(
            ["id", "mpud_history_id", "mpud_history", "user_id", "county_name"],
            f"mpud_history_id == '{chat_id}'",
            milvusDB,
        )

        resp = _chat_history_Collection.update_many_by_querry_upsert({"document.id": rule_ids}, {"document.$.status": 'deleted'}, db)

        rec_frm_milvus = rec_frm_milvus[0]
        mpud_history_frm_milvus = rec_frm_milvus["mpud_history"]
        history_id = list(mpud_history_frm_milvus.keys())[0]

        todel = ""
        for pos, rec in enumerate(mpud_history_frm_milvus[history_id]):
            if rec["id"] == rule_ids:
                todel = pos

        mpud_history_frm_milvus[history_id].pop(todel)

        rec_frm_milvus["mpud_history"] = mpud_history_frm_milvus

        texts_to_embed = convert_mpud_data_to_text(mpud_history_frm_milvus)
        embeddings = get_embedding_function()
        vector_result = embeddings.embed_query(str(texts_to_embed))
        rec_frm_milvus["vector"] = vector_result
        res = _MILVUS_Version_Coll.upsert(rec_frm_milvus, milvusDB)
        if res and resp.acknowledged:
            return {"message": f"Deleted MPUD file {rule_ids} data"}
        else:
            return {"message": f"Error in Deleting MPUD file {rule_ids} data"}
    except Exception as e:
        return {"message": f"Error in deleting MPUD file data: {e}"}


def reject_rec_in_mpud_version_data(rule_ids, db):
    try:
        rule_ids = ast.literal_eval(rule_ids)
        # mongo_ids_to_reject = [ObjectId(id) for id in ast.literal_eval(rule_ids)]
        # resp = _chat_history_Collection.update_many_by_querry_upsert({"document.id": {"$in": rule_ids}}, {"document.$.status": 'rejected'}, db)
        resp = _chat_history_Collection.update_many_by_querry_upsert_array_filters({"document.id": {"$in": rule_ids}}, {"document.$[elem].status": 'rejected'}, [{"elem.id": {"$in": rule_ids}}], db)

        if resp.acknowledged:
            return {"message": f"Successfully rejected the records {rule_ids}"}
        else:
            return {"message": f"Failed to reject records {rule_ids}"}

    except Exception as e:
        return {"message": f"Error in rejected records : {e}"}


def update_rec_in_mpud_version_data(
    chat_id, update_data, county_name, user_id, _MILVUS_Version_Coll, milvusDB, db, rule_id,
):
    try:
        rec_frm_milvus = _MILVUS_Version_Coll.query(
            ["id", "mpud_history_id", "mpud_history", "user_id", "county_name"],
            f"mpud_history_id == '{chat_id}'",
            milvusDB,
        )

        upsert_resp = _chat_history_Collection.update_one_by_query_upsert({"chat_id": chat_id}, {"document": {"content": update_data, "status": 'updated'}}, db)

        if not rec_frm_milvus :
            resp, title_rules = insert_mpud_version_data(chat_id, {'version_data': []}, user_id, county_name, [rule_id], _MILVUS_Version_Coll, db, milvusDB)
            _chat_history_Collection.update_one_by_query_upsert({"document.id": rule_id}, {"document.$.content": update_data, "document.$.status": 'accepted'}, db)
            return {"message": f"Updated MPUD file {rule_id} data"}

        rec_frm_milvus = rec_frm_milvus[0]
        mpud_history_frm_milvus = rec_frm_milvus["mpud_history"]
        history_id = list(mpud_history_frm_milvus.keys())[0]

        fndRecToUpdate = False
        for pos, rec in enumerate(mpud_history_frm_milvus[history_id]):
            if rec["id"] == rule_id:
                rec["content"] = update_data
                fndRecToUpdate = True

        if not fndRecToUpdate :
            resp, title_rules = insert_mpud_version_data(chat_id, {'version_data': []}, user_id, county_name, [rule_id], _MILVUS_Version_Coll, db, milvusDB)
            _chat_history_Collection.update_one_by_query_upsert({"document.id": rule_id}, {"document.$.content": update_data, "document.$.status": 'accepted'}, db)
            return {"message": f"Updated MPUD file {rule_id} data"}

        rec_frm_milvus["mpud_history"] = mpud_history_frm_milvus

        # texts_to_embed = convert_mpud_data_to_text(mpud_history_frm_milvus)
        embeddings = get_embedding_function()
        vector_result = embeddings.embed_query(str(mpud_history_frm_milvus))
        rec_frm_milvus["vector"] = vector_result
        res = _MILVUS_Version_Coll.upsert(rec_frm_milvus, milvusDB)
        if rec :
            _chat_history_Collection.update_one_by_query_upsert({"document.id": rule_id}, {"document.$.content": update_data, "document.$.status": 'accepted'}, db)
            _chat_history_Collection.update_one_by_query_upsert({"document.id": rule_id}, {"document.$.content": update_data, "document.$.status": 'accepted'}, db)
        if res and upsert_resp.acknowledged :
            return {"message": f"Updated MPUD file {rule_id} data"}
        else:
            return {"message": f"Error in updating MPUD file {rule_id} data"}
    except Exception as e:
        return {"message": f"Error in Updating MPUD file data: {e}"}


def convert_mpud_data_to_text(version_data):
    try:
        texts_to_embed = []
        for chat_id, rules in version_data.items():
            for rule_info in rules:
                for rule_name, q_a in rule_info.items():
                    if rule_name != "id":
                        for question, answer in q_a.items():
                            # Structure the text for embedding
                            text = f"question: {question}; answer: {answer}; rule: {rule_name}"
                            texts_to_embed.append(text)
        return texts_to_embed

    except Exception as e:
        return [e]


def insert_mpud_version_data(
    chat_id,
    version_data,
    user_id,
    county_name,
    rule_ids,
    _MILVUS_Version_Coll,
    db,
    milvusDb,
):
    try:
        # mongo_ids_to_accept = [ObjectId(id) for id in rule_ids]
        resp = _chat_history_Collection.update_many_by_querry_upsert_array_filters({"document.id": {"$in": rule_ids}}, {"document.$[elem].status": 'accepted'}, [{"elem.id": {"$in": rule_ids}}], db)

        rec_frm_milvus = get_mpud_version_data(
            chat_id, user_id, county_name, _MILVUS_Version_Coll, db, milvusDb,
        )
        title_rules = []

        if not version_data["version_data"]:
            ver_data_list = []

            # get_all_resp = _chat_history_Collection.get_all_by_projection({"document.id":{"$in":rule_ids}},{ "document": { "$elemMatch": { "id": { "$in": rule_ids } } } },db)

            match = {"$match": {"document.id": {"$in": rule_ids}}}
            group = {"$group": {"_id": "$_id", "document": {"$push": "$document"}}}
            unwind = {"$unwind": "$document"}
            get_all_resp = _chat_history_Collection.aggregate_with_unwind(
                match, unwind, group, db,
            )

            for rec in get_all_resp:
                for recd in rec["document"]:
                    dic = {}
                    dic["title"] = recd["title"]
                    dic["content"] = recd["content"]
                    dic["id"] = recd["id"]
                    ver_data_list.append(dic)
                    title_rules.append(recd["title"])
            version_data["version_data"] = ver_data_list

        if not rec_frm_milvus:
            version_data_rules = version_data["version_data"]

            if isinstance(version_data_rules, dict) and version_data_rules:
                version_data_rules = [version_data["version_data"]]
                version_data["version_data"] = version_data_rules

            data_to_post_milvus = {}
            # texts_to_embed = convert_mpud_data_to_text(version_data)

            embeddings = get_embedding_function()
            vector_result = embeddings.embed_query(str(version_data_rules))

            if isinstance(version_data_rules, list):
                for pos, dat in enumerate(version_data_rules):
                    # dat['id'] = str(uuid.uuid4())
                    version_data_rules[pos] = dat

            data_to_post_milvus["vector"] = vector_result
            data_to_post_milvus["id"] = str(uuid.uuid4())
            data_to_post_milvus["mpud_history"] = version_data
            data_to_post_milvus["user_id"] = user_id
            data_to_post_milvus["county_name"] = county_name
            data_to_post_milvus["mpud_history_id"] = chat_id

            if not get_mpud_version_data(chat_id, user_id, county_name, _MILVUS_Version_Coll, db, milvusDb):
                resp = _MILVUS_Version_Coll.create(data_to_post_milvus, milvusDb)
            else :
                return {"insert_count": int(0), "ids": ast.literal_eval(str(resp['ids']))}
            return {"insert_count": int(resp['insert_count']), "ids": ast.literal_eval(str(resp['ids']))}

        else:
            version_data = version_data["version_data"]
            rec_frm_milvus = rec_frm_milvus[0]
            mpud_history_frm_milvus = rec_frm_milvus["mpud_history"]

            if isinstance(version_data, list):
                for pos, dat in enumerate(version_data):
                    # dat['id'] = str(uuid.uuid4())
                    title_rules.append(dat["title"])
                    version_data[pos] = dat
                mpud_history_frm_milvus["version_data"].append(dat)

            elif isinstance(version_data, dict):
                # version_data['id'] = str(uuid.uuid4())
                title_rules.append(version_data["title"])
                mpud_history_frm_milvus["version_data"].append(version_data)

            rec_frm_milvus["mpud_history"] = mpud_history_frm_milvus

            # texts_to_embed = convert_mpud_data_to_text(mpud_history_frm_milvus)
            embeddings = get_embedding_function()
            vector_result = embeddings.embed_query(str(mpud_history_frm_milvus))

            rec_frm_milvus["vector"] = vector_result
            res = _MILVUS_Version_Coll.upsert(rec_frm_milvus, milvusDb)
            return res, title_rules

    except Exception as e:
        return {"message": f"Error in accepting MPUD file data: {e}"}, []
