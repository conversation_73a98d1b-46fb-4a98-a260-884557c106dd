from neo4j import GraphDatabase
from app.core.config import settings
import json


class COAEdit:
    @staticmethod
    def get_driver():
        driver = GraphDatabase.driver(
            settings.NEO4J_URI,
            auth=(settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD)
        )
        return driver

    @staticmethod
    def update_points_array(points_array, target_chunk_id, chat_message):
        """Helper function to update point content in a Points array"""
        for p in points_array:
            if p.get('chunk_id') == target_chunk_id:
                p['point'] = chat_message
                return True
        return False

    @classmethod
    def run_query(cls, chat_id, discussion_type, target_chunk_id, chat_message):
        """
        Update point content for a specific chunk in a discussion.

        Args:
            chat_id (int): ID of the discussion root node
            discussion_type (str): Type of discussion
            target_chunk_id (str): Chunk ID to be updated
            chat_message (str): New message content for the chunk
        """
        driver = cls.get_driver()

        with driver.session() as session:
            query = """
            MATCH(n:DiscussionRoot)-[HAS_CHILD]->(d:Discussion)
            WHERE id(n)=$chat_id and d.discussion_type=$discussion_type
            RETURN d
            """
            result = session.run(query, {
                'chat_id': chat_id,
                'discussion_type': discussion_type
            })

            for record in result:
                node = record["d"]

                common_points = json.loads(node['common_points']) if node.get('common_points') else []
                modifications = json.loads(node['modifications']) if node.get('modifications') else {}

                updated = False

                # Update common_points
                for point in common_points:
                    message = point.get('message', {})
                    point_modifications = message.get('modifications', [])
                    for mod in point_modifications:
                        modified_node = mod.get('modified_node', {})
                        points = modified_node.get('Points', [])
                        if cls.update_points_array(points, target_chunk_id, chat_message):
                            updated = True

                # Update modifications
                modified_node = modifications.get('modified_node', {})
                points = modified_node.get('Points', [])
                if cls.update_points_array(points, target_chunk_id, chat_message):
                    updated = True

                if updated:
                    update_query = """
                    MATCH (d:Discussion)
                    WHERE id(d) = $node_id
                    SET d.common_points = $common_points,
                        d.modifications = $modifications
                    """
                    session.run(update_query, {
                        'node_id': node.id,
                        'common_points': json.dumps(common_points),
                        'modifications': json.dumps(modifications)
                    })
                    return {"status": "success", "message": "Successfully updated the point content"}

            return {"status": "error", "message": "Chunk ID not found"}
