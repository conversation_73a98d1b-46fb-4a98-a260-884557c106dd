CHECK_FUNCTIONS = {
    "local": ["kg_query", "_find_most_related_text_unit_from_entities"],
    "global": ["kg_query", "_get_edge_data", "_find_related_text_unit_from_relationships", "_find_most_related_entities_from_relationships", "truncate_list_by_token_size", "list_of_list_to_csv"],
    "naive": ["naive_query", "query", "get_by_ids", "truncate_list_by_token_size"],
    "hybrid": ["kg_query", "combine_contexts", "get_by_ids", "truncate_list_by_token_size"],
}

STOP_FUNCTIONS = {
    "local": ["_find_most_related_text_unit_from_entities"],
    "global": ["truncate_list_by_token_size"],
    "naive": ["truncate_list_by_token_size"],
    "hybrid": ["truncate_list_by_token_size"],
}

process_states = {
    "local": {
        'kg_query': [
            {'status': 'pending', 'message': 'Processing local query', 'order': 1},
            {'status': 'returned', 'message': 'Local query processed', 'order': 4},
        ],
        '_build_query_context': [
            {'status': 'pending', 'message': 'Building Query Context', 'order': 3},
            {'status': 'returned', 'message': 'Successfully Returned', 'order': 2},
        ],
        '_build_local_query_context': [
            {'status': 'pending', 'message': 'Processing local query context', 'order': 2},
            {'status': 'returned', 'message': 'Local query context processed', 'order': 3},
        ],
        '_find_most_related_text_unit_from_entities': [
            {'status': 'pending', 'message': 'Processing find most related text unit from entities', 'order': 11},
            {'status': 'returned', 'message': 'Find most related text unit from entities processed', 'order': 12},
        ],
        '_find_most_related_edges_from_entities': [
            {'status': 'pending', 'message': 'Processing find most related edges from entities', 'order': 13},
            {'status': 'returned', 'message': 'Find most related edges from entities processed', 'order': 14},
        ],
    },
    "global": {
        'kg_query': [
            {'status': 'pending', 'message': 'Processing global query', 'order': 5},
            {'status': 'returned', 'message': 'Global query processed', 'order': 6},
        ],
        '_get_edge_data': [
            {'status': 'pending', 'message': 'Getting Edge Data', 'order': 5},
            {'status': 'returned', 'message': 'Received Edge Data', 'order': 6},
        ],
        '_find_most_related_entities_from_relationships': [
            {'status': 'pending', 'message': 'Finding most related entities', 'order': 5},
            {'status': 'returned', 'message': 'Finding most related entities completed', 'order': 6},
        ],
        '_find_related_text_unit_from_relationships': [
            {'status': 'pending', 'message': 'Finding related texts from relationships', 'order': 5},
            {'status': 'returned', 'message': 'Finding related texts from relationships Completed', 'order': 6},
        ],
    },
    "naive": {
        'naive_query': [
            {'status': 'pending', 'message': 'Processing naive query', 'order': 9},
            {'status': 'returned', 'message': 'Naive query processed', 'order': 10},
        ],
        'query': [
            {'status': 'pending', 'message': 'Querying', 'order': 9},
            {'status': 'returned', 'message': 'Querying completed', 'order': 10},
        ],
        'get_by_ids': [
            {'status': 'pending', 'message': 'Fetching chunk ids', 'order': 9},
            {'status': 'returned', 'message': 'Chunk ids processed', 'order': 10},
        ],
    },
    "hybrid": {
        'combine_contexts': [
            {'status': 'pending', 'message': 'Combining Contexts', 'order': 3},
            {'status': 'returned', 'message': 'Contexts are combined successfully', 'order': 2},
        ],
        'process_combine_contexts': [
            {'status': 'pending', 'message': 'Processing Combining Contexts', 'order': 3},
            {'status': 'returned', 'message': 'Combining Process Completed', 'order': 2},
        ],
        'kg_query': [
            {'status': 'pending', 'message': 'Processing hybrid query', 'order': 7},
            {'status': 'returned', 'message': 'Hybrid query processed', 'order': 8},
        ],
    },
}
