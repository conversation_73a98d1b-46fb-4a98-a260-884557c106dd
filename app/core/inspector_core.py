import json
from app.utils.base_utils import (
    get_latest_n_messages_from_chat,
    create_new_message,
)
from neo4j.exceptions import ConfigurationError
from app.crud.inspector_repository import InspectorRepository
import time

"""
LLM Response structure
{
  "answer": "answer answer",
  "structured_content": [
    {
      "id": "title_id",
      "title": "title | Header",
      "conditions": [
        {
          "text": "originaltext",
          "id": "textid",
          "meta_data": {
            "file_name": "filename",
            "page_no": "pageno"
          }
        }
      ]
    }
  ]
}


output structure response

{
  "_id": {
    "$oid": "66f3c62a9f947b893626d859"
  },
  "chat_id": "f73ee576-58f2-44f9-90b0-19b0df3fe957",
  "content": {
    "human": {
      "message": "Hello"
    },
    "ai": {
      "message": "Not much context available."
    }
  },
  "document": [],
  "meta_data": [],
  "timestamp": {
    "$date": "2024-09-25T08:13:30.198Z"
  }
}

"""

# Inspector part
inspector = InspectorRepository()


def transform_chat_data(data):
    result = []

    for item in data:
        transformed_item = {
            "human": item["content"]["human"]["message"],
            "ai": {
                "answer": item["content"]["ai"]["message"],
                "structured_content": [],
            },
        }

        for doc in item.get("document", []):
            structured_doc = {
                "id": doc.get("id", ""),
                "title": doc.get("title", ""),
                "conditions": [],
            }

            for condition in doc.get("conditions", []):
                structured_condition = {
                    "text": condition.get("text", ""),
                    "id": condition.get("id", ""),
                    "meta_data": {
                        "file_name": condition.get("meta_data", {}).get(
                            "file_name",
                            "",
                        ),
                        "page_no": condition.get("meta_data", {}).get("page_no", ""),
                    },
                }
                structured_doc["conditions"].append(structured_condition)

            transformed_item["ai"]["structured_content"].append(structured_doc)

        result.append(transformed_item)
    print("transform_chat_data", result)
    return result


def process_coordinates(data):
    if isinstance(data, dict):
        for key, value in data.items():
            if key == "coordinates" and isinstance(value, str):
                try:
                    data[key] = json.loads(value.replace("'", '"'))
                except json.JSONDecodeError:
                    print(f"Warning: Could not parse coordinates: {value}")
            elif isinstance(value, (dict, list)):
                process_coordinates(value)
    elif isinstance(data, list):
        for item in data:
            process_coordinates(item)
    return data


def chat(messages, message):
    try:
        # messages = []  # remove after correcting inspector
        output = inspector.inspector(message, messages)
        print("chat-output", output)
        if isinstance(output["answer"], str):
            return process_coordinates(output)
        return process_coordinates(output["answer"])
    except ConfigurationError as e:
        return {"error": f"Neo4j configuration error: {str(e)}"}


def inspect(chat_id, message, model_name, user_id: str, application_id: str):
    if model_name not in ["gpt-4o-mini", "gpt-4o"]:
        msg = "Please use gpt-4o-mini or gpt-4o model"
        yield f"data: {json.dumps({'content': msg})}\n\n"
        return

    # Retrieve conversation history and generate response
    history, chat_id, chat_title = get_latest_n_messages_from_chat(
        chat_id,
        user_id,
        application_id=application_id,
    )

    # Simulate LLM response (full text obtained in a single call)
    llm_response = chat(transform_chat_data(history), message)
    answer = llm_response["answer"]
    content = llm_response["structured_content"]

    # Word-by-word streaming simulation
    words = answer.split()
    for word in words:
        yield f"data: {json.dumps({'content': word})}\n\n"
        time.sleep(0.05)  # Optional delay for a realistic streaming pace

    # Yield final structured content if required at the end
    final_response = {
        "answer": answer,
        "structured_content": content,
        "chat_id": chat_id,
        "chat_title": chat_title,
    }
    yield f"data: {json.dumps({'content': json.dumps(final_response)})}\n\n"

    # Save full conversation message to chat history
    create_new_message(chat_id, answer, message, content, [])
