from contextvars import Context<PERSON><PERSON>
from typing import Optional
from .base_logger import BaseLogger


# Context variable to store chat_id
chat_id_context = ContextVar('chat_id', default='unknown')


class ChatLogger(BaseLogger):
    """
    A logger class that includes chat_id in messages
    """

    def set_chat_id(self, chat_id: str):
        """Set the chat ID for the current context"""
        chat_id_context.set(chat_id)

    def get_chat_id(self) -> str:
        """Get the current chat ID"""
        return chat_id_context.get()

    def _get_extra_fields(self, extra: Optional[dict] = None) -> dict:
        """Combine chat_id with any existing extra fields"""
        fields = super()._get_extra_fields(extra)
        fields['chat.id'] = self.get_chat_id()
        return fields


def get_chat_logger(name: str) -> ChatLogger:
    """
    Get a logger instance that includes chat_id in messages
    """
    return ChatLogger(name)
