import logging
from typing import Optional
import inspect


class BaseLogger:
    """
    A base logger class that includes common functionality for all loggers
    """

    def __init__(self, name: str):
        self._logger = logging.getLogger(name)
        self._name = name

    def _get_extra_fields(self, extra: Optional[dict] = None) -> dict:
        """Get extra fields including caller information"""
        fields = {}
        if extra:
            fields.update(extra)

        # Get caller's file and line number
        frame = inspect.currentframe()
        # Go up 3 frames to get to the actual caller:
        # 1. _get_extra_fields
        # 2. debug/info/warning/error method
        # 3. actual caller
        caller_frame = inspect.getouterframes(frame)[3]
        caller_file = caller_frame.filename
        caller_func = caller_frame.function
        caller_line = caller_frame.lineno

        fields['code_filepath'] = caller_file
        fields['code_lineno'] = caller_line
        fields['code_func'] = caller_func
        return fields

    def debug(self, msg: str, *args, **kwargs):
        kwargs['extra'] = self._get_extra_fields(kwargs.get('extra'))
        self._logger.debug(msg, *args, **kwargs)

    def info(self, msg: str, *args, **kwargs):
        kwargs['extra'] = self._get_extra_fields(kwargs.get('extra'))
        self._logger.info(msg, *args, **kwargs)

    def warning(self, msg: str, *args, **kwargs):
        kwargs['extra'] = self._get_extra_fields(kwargs.get('extra'))
        self._logger.warning(msg, *args, **kwargs)

    def error(self, msg: str, *args, **kwargs):
        kwargs['extra'] = self._get_extra_fields(kwargs.get('extra'))
        self._logger.error(msg, *args, **kwargs)


def get_logger(name: str) -> BaseLogger:
    """
    Get a base logger instance
    """
    return BaseLogger(name)
