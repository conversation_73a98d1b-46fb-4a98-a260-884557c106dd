from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.resources import Resource
from opentelemetry._logs import set_logger_provider
from opentelemetry.sdk._logs import LoggerProvider
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
from opentelemetry.exporter.otlp.proto.grpc._log_exporter import OTLPLogExporter
from opentelemetry.sdk._logs import LoggingHandler
import logging
from app.core.config import settings
import os
from .base_logger import get_logger
from .chat_logger import get_chat_logger

# Global variables to track process ID and loggers
_pid = None
_loggers = {}


def setup_telemetry() -> dict[str, logging.Logger]:
    """
    Initialize telemetry setup with process awareness for Uvicorn workers
    """
    global _pid, _loggers   # noqa: F824
    current_pid = os.getpid()

    # Only setup if it's a new process or loggers haven't been initialized
    if _pid != current_pid or not _loggers:
        _pid = current_pid

        # Clear any existing loggers
        for logger_name in ["pasco.ai.connector", "pasco.ai.chat"]:
            logger = logging.getLogger(logger_name)
            logger.handlers.clear()

        # Create resources with explicit log types
        pasco_resource = Resource.create(
            {
                "service.name": settings.OTEL_SERVICE_NAME,
                "service.instance.id": f"{settings.INSTANCE_ID}-{current_pid}",
                "deployment.environment": settings.STAGE,
                "log.type": "connector",
                "process.id": str(current_pid),
            }
        )

        pasco_chat_resource = Resource.create(
            {
                "service.name": settings.OTEL_SERVICE_NAME,
                "service.instance.id": f"{settings.INSTANCE_ID}-{current_pid}",
                "deployment.environment": settings.STAGE,
                "log.type": "chat",
                "process.id": str(current_pid),
            }
        )

        # Setup tracing
        trace_provider = TracerProvider(resource=pasco_resource)
        otlp_span_exporter = OTLPSpanExporter(
            endpoint=settings.OTEL_EXPORTER_OTLP_ENDPOINT, insecure=True
        )
        trace_provider.add_span_processor(BatchSpanProcessor(otlp_span_exporter))
        trace.set_tracer_provider(trace_provider)

        # Setup Pasco logger
        pasco_provider = LoggerProvider(resource=pasco_resource)
        set_logger_provider(pasco_provider)
        pasco_exporter = OTLPLogExporter(
            endpoint=settings.OTEL_EXPORTER_OTLP_ENDPOINT, insecure=True
        )
        pasco_provider.add_log_record_processor(BatchLogRecordProcessor(pasco_exporter))
        pasco_handler = LoggingHandler(
            level=logging.NOTSET, logger_provider=pasco_provider
        )
        pasco_logger = get_logger("pasco.ai.connector")
        pasco_logger._logger.addHandler(pasco_handler)
        pasco_logger._logger.setLevel(logging.DEBUG)
        _loggers["pasco"] = pasco_logger

        # Setup Pasco Chat logger
        pasco_chat_provider = LoggerProvider(resource=pasco_chat_resource)
        pasco_chat_exporter = OTLPLogExporter(
            endpoint=settings.OTEL_EXPORTER_OTLP_ENDPOINT, insecure=True
        )
        pasco_chat_provider.add_log_record_processor(
            BatchLogRecordProcessor(pasco_chat_exporter)
        )
        pasco_chat_handler = LoggingHandler(
            level=logging.NOTSET, logger_provider=pasco_chat_provider
        )
        pasco_chat_logger = get_chat_logger("pasco.ai.chat")
        pasco_chat_logger._logger.addHandler(pasco_chat_handler)
        pasco_chat_logger._logger.setLevel(logging.INFO)
        _loggers["pasco.ai.chat"] = pasco_chat_logger

    return _loggers
