from dataclasses import dataclass, field
from typing import List, Dict, Literal, AsyncGenerator
from pymilvus import Collection, connections, utility
import asyncio
import logging
from lightrag.llm import gpt_4o_mini_complete, openai_embedding
from lightrag.prompt import PROMPTS
import json
import os
from app.core.config import settings
from openai import AsyncOpenAI
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class SearchResult:
    id: str
    score: float
    content: str
    source: str
    metadata: Dict
    meta: Dict


@dataclass
class SearchMode:
    mode: Literal["naive", "local", "global", "hybrid"]
    description: str
    vector_weight: float = 0.6
    top_k: int = 10
    similarity_threshold: float = 0.3

    @classmethod
    def get_available_modes(cls) -> List["SearchMode"]:
        return [
            cls("naive", "Direct chunk search using query vector", 1.0),
            cls("local", "Entity-centric search using low-level keywords", 0.7),
            cls("global", "Relationship-centric search using high-level keywords", 0.7),
            cls("hybrid", "Combined search using both high and low level keywords", 0.6),
        ]


@dataclass
class VectorSearchEngine:
    mode: SearchMode
    collections: Dict[str, Collection] = field(default_factory=dict)

    def __post_init__(self):
        self.validate_env_variables()
        self.initialize_connections()

    def validate_env_variables(self):
        """Validate required environment variables"""
        required_vars = {
            'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
            'MILVUS_URI': os.getenv('MILVUS_URI'),
            'MILVUS_TOKEN': os.getenv('MILVUS_TOKEN'),
            'MILVUS_DEFAULT_DATABASE': os.getenv('MILVUS_DEFAULT_DATABASE', 'default'),
        }

        missing_vars = [var for var, value in required_vars.items() if not value]
        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

        self.env_config = required_vars

    def initialize_connections(self):
        """Initialize connections to Milvus"""
        try:
            connections.connect(
                alias="default",
                uri=settings.MILVUS_URI,
                token=settings.MILVUS_TOKEN,
                db_name=self.env_config['MILVUS_DEFAULT_DATABASE'],
            )
            logger.info("Successfully connected to Milvus")
            self._init_collections()
        except Exception as e:
            logger.error(f"Failed to initialize connections: {str(e)}")
            raise

    def _init_collections(self):
        """Initialize Milvus collections"""
        required_collections = {'chunks', 'entity', 'relation'}
        existing_collections = utility.list_collections()
        missing_collections = required_collections - set(existing_collections)

        if missing_collections:
            raise ValueError(f"Missing required collections: {missing_collections}")

        for coll_name in required_collections:
            self.collections[coll_name] = Collection(coll_name)
            self.collections[coll_name].load()
            logger.info(f"Loaded collection: {coll_name} with {self.collections[coll_name].num_entities} entities")

    async def analyze_query(self, query: str) -> Dict:
        """Analyze query to extract keywords"""
        try:
            prompt = PROMPTS["keywords_extraction"].format(query=query)
            analysis_result = await gpt_4o_mini_complete(prompt)
            return json.loads(analysis_result)
        except Exception as e:
            logger.error(f"Query analysis failed: {str(e)}")
            return {"high_level_keywords": [], "low_level_keywords": [query]}

    async def vector_search(self, query: str, collection: Collection) -> List[SearchResult]:
        """Perform vector similarity search in Milvus"""
        try:
            query_embedding = await openai_embedding([query])
            logger.info(f"Searching in collection {collection.name} with embedding shape: {query_embedding.shape}")

            search_params = {
                "metric_type": "COSINE",
                "params": {"nprobe": 10},
            }

            results = collection.search(
                data=[query_embedding[0]],
                anns_field="embedding",
                param=search_params,
                limit=self.mode.top_k,
                output_fields=["*"],
            )

            search_results = []
            for hits in results:
                for hit in hits:
                    try:
                        if hit.distance > self.mode.similarity_threshold:
                            content = hit.entity.meta.get("content", "")
                            metadata = {}

                            if collection.name == 'chunks':
                                metadata.update({
                                    'type': 'chunk',
                                    'full_doc_id': hit.entity.meta.get("full_doc_id", ""),
                                })
                            elif collection.name == 'entity':
                                metadata.update({
                                    'type': 'entity',
                                    'entity_name': hit.entity.meta.get("entity_name", ""),
                                })
                            elif collection.name == 'relation':
                                metadata.update({
                                    'type': 'relation',
                                    'src_id': hit.entity.meta.get("src_id", ""),
                                    'tgt_id': hit.entity.meta.get("tgt_id", ""),
                                })

                            search_results.append(
                                SearchResult(
                                    id=str(hit.id),
                                    score=float(hit.distance),
                                    content=content,
                                    source=collection.name,
                                    metadata=metadata,
                                    meta=hit.entity.meta,
                                ),
                            )

                    except Exception as e:
                        logger.error(f"Error processing hit in {collection.name}: {str(e)}")
                        continue

            return search_results

        except Exception as e:
            logger.error(f"Vector search failed for collection {collection.name}: {str(e)}")
            return []

    async def naive_search(self, query: str) -> List[SearchResult]:
        """Direct chunk search using query vector"""
        logger.info("Performing naive search")
        results = await self.vector_search(query, self.collections['chunks'])
        return results

    async def local_search(self, keywords: List[str]) -> List[SearchResult]:
        """Entity-centric search using low-level keywords"""
        logger.info("Performing local search")
        all_results = []

        for keyword in keywords:
            chunk_results = await self.vector_search(keyword, self.collections['chunks'])
            all_results.extend(chunk_results)

            entity_results = await self.vector_search(keyword, self.collections['entity'])
            all_results.extend(entity_results)

            for result in entity_results:
                entity_name = result.metadata.get('entity_name', '')
                if entity_name:
                    related_chunks = await self.vector_search(
                        f"Information about {entity_name}",
                        self.collections['chunks'],
                    )
                    all_results.extend(related_chunks)

        return all_results

    async def global_search(self, keywords: List[str]) -> List[SearchResult]:
        """Relationship-centric search using high-level keywords"""
        logger.info("Performing global search")
        all_results = []

        for keyword in keywords:
            relation_results = await self.vector_search(keyword, self.collections['relation'])
            all_results.extend(relation_results)

            entity_results = await self.vector_search(keyword, self.collections['entity'])
            all_results.extend(entity_results)

            chunk_results = await self.vector_search(keyword, self.collections['chunks'])
            all_results.extend(chunk_results)

        return all_results

    async def hybrid_search(self, query: str, analysis: Dict) -> List[SearchResult]:
        """Combined search using both high and low level keywords"""
        logger.info("Performing hybrid search")

        tasks = [
            self.naive_search(query),
            self.local_search(analysis['low_level_keywords']),
            self.global_search(analysis['high_level_keywords']),
        ]

        results = await asyncio.gather(*tasks)
        all_results = [item for sublist in results for item in sublist]

        if all_results:
            max_score = max(r.score for r in all_results)
            min_score = min(r.score for r in all_results)
            score_range = max_score - min_score

            if score_range > 0:
                for r in all_results:
                    r.score = (r.score - min_score) / score_range

        seen_ids = set()
        unique_results = []
        for result in sorted(all_results, key=lambda x: x.score, reverse=True):
            if result.id not in seen_ids:
                unique_results.append(result)
                seen_ids.add(result.id)

        return unique_results[:self.mode.top_k]

    async def search(self, query: str) -> List[SearchResult]:
        """Execute search based on selected mode"""
        try:
            logger.info(f"Executing search in {self.mode.mode} mode for query: {query}")
            analysis = await self.analyze_query(query)
            logger.info(f"Query analysis: {analysis}")

            if self.mode.mode == "naive":
                results = await self.naive_search(query)
            elif self.mode.mode == "local":
                results = await self.local_search(analysis['low_level_keywords'])
            elif self.mode.mode == "global":
                results = await self.global_search(analysis['high_level_keywords'])
            else:
                results = await self.hybrid_search(query, analysis)

            return results
        except Exception as e:
            logger.error(f"Search failed: {str(e)}")
            return []


async def summarize_results(results: List[SearchResult]) -> str:
    """Create a summary of search results"""
    if not results:
        return "No results to summarize."

    try:
        all_content = "\n".join([f"{r.source}: {r.content}" for r in results])
        summary_prompt = f"""Please summarize the following search results:
        {all_content}
        Provide a summary that includes:
        1. Main points
        2. Key relationships
        3. Important details
        """
        return await gpt_4o_mini_complete(summary_prompt)
    except Exception as e:
        logger.error(f"Summarization failed: {str(e)}")
        return "Summary generation failed."


async def summarize_results_streaming(
    message,
    results: List[SearchResult],
    client: AsyncOpenAI,
) -> AsyncGenerator[str, None]:
    """
    Create a streaming summary of search results using OpenAI's API.
    Yields chunks of the summary as they're generated.
    """
    if not results:
        yield "No results to summarize."
        return

    try:
        all_content = "\n".join([f"{r.source}: {r.content}\nConfidence: {r.score}\n\n" for r in results])
        summary_prompt = f"""
        Please summarize the following search results:
        {all_content}
        Provide a summary that includes:
        1. Important details

        ## Note
        - Use Markdown

        ### Examples
        Query: "Hi" → Response: "Hello! I can help you with information about [topic1] and [topic2]. What would you like to know?"
        Query: "What is [context_topic]?" → [Provide relevant information from context]
        """
#         summary_prompt = f"""
#   # Query Analysis: {message}

# ## Available Context
# {all_content}

# ## Response Instructions

# ### Check Query Type
# 1. Is it a greeting/casual message? (hi, hello, hey, etc.)
# 2. Is it a specific question about available topics?
# 3. Does it clearly relate to the context?

# ### Response Rules

# IF GREETING/UNCLEAR:
# Ask for clarification like:
# "I can help you with information about: [list_available_topics]. What would you like to know?"

# IF SPECIFIC AND RELEVANT:
# Provide a clear, concise answer:
# - Keep under 200 words
# - Use bullet points
# - Include confidence level
# - Reference specific sources

# ### Examples
# Query: "Hi" → Response: "Hello! I can help you with information about [topic1] and [topic2]. What would you like to know?"
# Query: "What is [context_topic]?" → [Provide relevant information from context]

# Remember: Only answer questions directly related to the provided context.
#         """
        print(summary_prompt)

        stream = await client.chat.completions.create(
            model="gpt-4",  # or your preferred model
            messages=[{
                "role": "user",
                "content": summary_prompt,
            }],
            stream=True,
        )

        async for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                yield chunk.choices[0].delta.content

    except Exception as e:
        logger.error(f"Streaming summarization failed: {str(e)}")
        yield "\nSummary generation failed."


def print_results(results: List[SearchResult]):
    """Print search results with detailed information"""
    if not results:
        print("No results found")
        return

    for i, result in enumerate(results, 1):
        print(f"\nResult {i}:")
        print("=" * 50)
        print(f"Content: {result.content}")
        print(f"Score: {result.score:.3f}")
        print(f"Source: {result.source}")

        if result.metadata:
            print("\nMetadata:")
            for key, value in result.metadata.items():
                print(f"  {key}: {value}")
        print("=" * 50)


def format_metadata(metadata, score):
    # Determine title - check header first, then title
    header = metadata.get("header", {}).get("data", "")
    doc_title = metadata.get("title", {}).get("data", "")

    combined_title = header if header else doc_title

    # Get coordinates based on if header exists
    coords = metadata.get("header", {}).get("coordinates", {}) if header else metadata.get("title", {}).get("coordinates", {})

    formatted_data = {
        "title": combined_title,
        "score": score,
        "meta_data": {
            "file_name": metadata.get("file_name", ""),
            "coordinates": {
                "Width": coords.get("Width", 0),
                "Height": coords.get("Height", 0),
                "Left": coords.get("Left", 0),
                "Top": coords.get("Top", 0),
            },
            "page_no": metadata.get("header", {}).get("page", "") if header else metadata.get("title", {}).get("page", ""),
        },
        "conditions": [
            {
                "text": metadata.get("text", {}).get("data", ""),
                "meta_data": {
                    "file_name": metadata.get("file_name", ""),
                    "coordinates": {
                        "Width": metadata.get("text", {}).get("coordinates", {}).get("Width", 0),
                        "Height": metadata.get("text", {}).get("coordinates", {}).get("Height", 0),
                        "Left": metadata.get("text", {}).get("coordinates", {}).get("Left", 0),
                        "Top": metadata.get("text", {}).get("coordinates", {}).get("Top", 0),
                    },
                    "page_no": metadata.get("text", {}).get("page", ""),
                },
            },
        ],
    }

    return formatted_data


class ErrorType(Enum):
    SEARCH_ERROR = "search_error"
    API_ERROR = "api_error"
    RATE_LIMIT = "rate_limit"
    CONNECTION_ERROR = "connection_error"
    GENERAL_ERROR = "general_error"


ERROR_MESSAGES = {
    ErrorType.SEARCH_ERROR: {
        "message": "I'm having trouble searching through the information. Please try again in a moment.",
        "suggestion": "You might want to rephrase your question or break it into smaller parts.",
    },
    ErrorType.API_ERROR: {
        "message": "I'm experiencing a temporary issue with processing your request.",
        "suggestion": "Please try again in a few moments.",
    },
    ErrorType.RATE_LIMIT: {
        "message": "I'm receiving too many requests right now.",
        "suggestion": "Please wait a minute before trying again.",
    },
    ErrorType.CONNECTION_ERROR: {
        "message": "I'm having trouble connecting to the server.",
        "suggestion": "Please check your internet connection and try again.",
    },
    ErrorType.GENERAL_ERROR: {
        "message": "Something unexpected happened while processing your request.",
        "suggestion": "Please try again or rephrase your question.",
    },
}


async def chat(query):
    try:
        search_modes = SearchMode.get_available_modes()
        engine = VectorSearchEngine(mode=search_modes[0])  # Naive mode
        client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)

        # Perform search
        results = await engine.search(query)

        # Display results
        response = {}
        response['structured_content'] = []
        for result in results:
            response['structured_content'].append(format_metadata(result.meta, result.score))

        # Get summary
        # summary = await summarize_results(results)
        content = ''
        async for chunk in summarize_results_streaming(query, results, client):
            # Print chunks as they arrive
            content += chunk
            yield {'answer': content, 'structured_content': []}
            print(chunk, end="", flush=True)
            await asyncio.sleep(0)

        final_response = {
            'answer': content,
        }
        final_response.update(response)
        yield final_response
    except Exception:
        yield {'answer': 'Please try after sometimes'}
