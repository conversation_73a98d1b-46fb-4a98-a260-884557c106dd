from app.core.interactive_discussion.llm.core.llm_factory import LLMFactory, BaseLLM
from dataclasses import dataclass, field
from app.core.lightragv2.mongo_impl import BaseKVStorage
import re
import json
from app.core.config import settings

question_pattern = r"Question:\s*(.*?\?)"
word_pattern = r"Word:\s*(\w+)"
patterns = {
    "question": r"Question:\s*(.*?\?)",
    "word": r"Word:\s*(\w+)",
    "trigger": r"Trigger:\s*(.*?)(?:\n|$)",
    "action_required": r"Action Required:\s*(.*)",
    "responsible_party": r"Responsible Party:\s*(.*)",
    "deliverable": r"Deliverable:\s*(.*)",
    "enforcing_department": r"Enforcing Department:\s*(.*)",
    "validating_department": r"Validating Department:\s*(.*)",
    "question_generated_pattern": r"Question_already_generated:\s*(\w+)",
    "point": r"Point:\s*(.*)",
    "is_related": r"Is_related:\s*(\w+)",
}

system_prompt = """

There are 2 main steps (go through them and start the process to get better results):
    1. Question Generation (## CRITICAL REQUIREMENTS FOR GENERATING GENERIC QUESTIONS)
    2. Extracting Actionable Conditions (## Flow for Extracting Actionable Conditions)

## CRITICAL REQUIREMENTS FOR GENERATING GENERIC QUESTIONS:

    ALL questions MUST be 100% GENERIC with NO EXCEPTIONS:

    STEP 1: GENERALIZATION PRE-PROCESSING
    Before generating any question, transform the input by:
    1. Removing all specific identifiers and context
    2. Identifying only the core requirement/action
    3. Converting to a generic template

    Example transformation:
    Input: "Submit detailed drainage calculations for Phase 2A of Project Sunview by March 2024"
    Generic: "Submit the required calculations for the designated project phase"

    STEP 2: QUESTION STRUCTURE VALIDATION
    Every question MUST:
    1. Start with "Has" or "Have" or "Will" to ensure yes/no format
    2. Use ONLY these generic prefixes for ALL nouns for example:
       - "the required"
       - "the designated"
       - "the applicable"
       - "the specified"
       - "the necessary"
    3. Focus on the action/requirement, not the documentation

    STEP 3: FORBIDDEN ELEMENTS (AUTOMATIC REJECTION)
    Immediately reject and regenerate if the question contains:
    1. Any numbers, dates, or measurements
    2. Any proper nouns or specific names
    3. Any project-specific terminology
    4. Any location references
    5. Any specific department names
    6. Any specific document types without generic prefixes

    STEP 4: MANDATORY QUESTION REVIEW
    Before finalizing, verify:
    1. Could this question apply to ANY similar requirement?
    2. Have ALL specific terms been replaced with generic alternatives?
    3. Is every noun properly prefixed with a generic term?
    4. Is the question completely context-independent?

    If ANY of these checks fail, regenerate the question.

    STEP 5: QUESTION RELEVANCE AND DUPLICATION CHECK
    1. The question MUST directly relate to the core intent of the input description
    2. If the question is not related to the content of the description, answer 'No' to 'Is_related'
    3. If a similar question already exists in 'already_generated_questions', answer 'Yes' to 'Question_already_generated'

    STEP 6: DYNAMIC QUESTION MERGING PROTOCOL
    1. Before generating a new question:
       - Remove ALL modifiers from nouns (eg. "landscape buffer" → "buffer")
       - Replace location references with "designated area"
       - Standardize verbs to root form (eg. "constructed" → "construct")

    2. Consider questions duplicate if they share ALL:
       - Same core noun (after modifier removal)
       - Same root verb
       - Same validation phase ("prior to X"/"during Y")

    3. When duplicates detected:
       - Reuse existing question structure
       - Append new specifics to Deliverable (separated by ";")
       - Update Point field with merged generalization
       - Set Question_already_generated=Yes

    STEP 7: MERGEABLE TERM EQUIVALENCIES
    Treat these terms as identical:
    - "buffer" = "landscape buffer" = "boundary barrier"
    - "plan" = "utility plan" = "drainage blueprint"
    - "infrastructure" = "sidewalk" = "roadway"
    - "approval" = "permit" = "clearance"

## Flow for Extracting Actionable Conditions:

    STEP 1: First, scan each condition for a specific timing trigger. Only proceed to Step 2 if the condition contains explicit timing language such as:
    - "Prior to..."
    - "Before..."
    - "At the time of..."
    - "Concurrent with..."
    - "Within X days of..."
    - "No later than..."

    STEP 2: For conditions that passed Step 1, verify they require a concrete deliverable or measurable action, such as:
    - Documents to be submitted
    - Infrastructure to be constructed
    - Studies to be performed
    - Payments to be made
    - Specific physical improvements

    STEP 3: For conditions that passed Steps 1-2, make sure that the condition has an identifiable Pasco County department responsible for enforcement and validation (either explicitly stated or clearly inferable):
    - An enforcing department responsible for ensuring the condition is met
    - A validating department responsible for reviewing/approving the deliverable
    - Distinguish between the department that enforces compliance versus the one that validates completion
    - ** Strictly the enforcing department and validating department should not be same.**
    Use these Pasco County departments:
        - Planning and Economic Growth Department (PEG)
        - Development Review Division
        - Engineering Services Department
        - Environmental Biologist/Natural Resources
        - Building Department
        - Utilities Services Branch
        - Parks and Recreation Department
        - Emergency Services Department
        - Permits and Development Information Services (PDIS)
        - Stormwater Management Division
        - Public Transportation Department (PCPT)
        - Real Estate Division
        - Mobility Fee Administration
        - Fire Rescue/Fire Marshal
    - The department should be a Pasco County department and not a state or federal department.

    STEP 4: For conditions that passed Steps 1-3, confirm they require a specific one-time action, not an ongoing standard or rule.

    STEP 5:**Rules for Extracting the Responsible Party:**
            - The **Responsible Party** is the entity responsible for taking action on the requirement in the sentence.
            - The Responsible Party **must be explicitly mentioned in the input text**.
            - Skip this Step only if Responsible Party is not mentioned in the input text:
                -- If Responsible Party is not mentioned, then STRICTLY provide the developer's name from the description.
                -- **If the Responsible Party is "Developer", replace it with the actual developer's name from the description.[It should not be shown as Developer unless developer's name is not specified]**

        STEP 5.1: MERGEABLE CONDITION CRITERIA:
        Combine conditions if they:
        - Share same enforcing/validating departments
        - Require same type of deliverable (documents/construction)
        - Have similar triggers ("before X" vs "prior to X")
        Output format: "Action1 and Action2"

    **If any condition that fails any of the four steps, return only the Question, Question_already_generated, Word, and Point. All other fields should be "none"**

**Output Format:**
Question: <generic yes/no question>
Word: <key noun>
Trigger: <trigger condition>
Action Required: <action to be performed>
Responsible Party: <Use the Developer name from the description if available, otherwise "Developer">
Deliverable: <expected output>
Enforcing Department: <department ensuring compliance>
Validating Department: <different department reviewing submission>
Question_already_generated: <boolean> Yes or No
Is_related: <boolean> Yes or No
Point: <point> Modified version of the input point, with any specific elements generalized.
"""

user_prompt = """
    1. If the point is already present in already_populated_points, then the Question_already_generated must have 'yes' in output.
    2. Even if any point similar to the 'point' is present in 'already_populated_points', then the Question_already_generated must have 'yes' in output.
    3. If the point is already present in already_generated_questions then the Question_already_generated should have 'yes' in output.
    4. If the question you are going to generate is available in the already_generated_questions then the Question_already_generated should have 'yes' in output.
    Even if you are 20 to 30 percent sure that the points or question to be generated will be similar, then Question_already_generated should have 'yes' in output.

    Input:
        point: {point}
        already_generated_questions : {questions_already_generated}
        already_populated_points: {common_points}
        description: {description}

    Output:
"""


@dataclass
class QuestionGenerator:
    database: BaseKVStorage
    llm: BaseLLM = field(
        default_factory=lambda: LLMFactory.get_client(
            settings.ANTHROPIC_API_KEY, type="anthropic"
        )
    )
    system_prompt: str = field(default=system_prompt)
    user_prompt: str = field(default=user_prompt)
    batch: int = field(default=50)

    async def _generate(self, condition) -> tuple:
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": self.user_prompt.format(point=condition)},
        ]
        completion_result = self.llm.llm_chat_completions(
            messages=messages, model="claude-3-haiku-20240307"
        )
        content = (
            completion_result.choices[0].message.content
            if completion_result.choices
            else ""
        )

        extracted_data = {}
        for key, pattern in patterns.items():
            matches = re.findall(pattern, content)
            extracted_data[key] = matches[-1].strip() if matches else None

        # Validate trigger format
        if extracted_data.get("trigger"):
            trigger = extracted_data.get("trigger")
            # Check if trigger starts with a timing phrase
            timing_phrases = [
                "prior to",
                "before",
                "at time of",
                "during",
                "after",
                "upon",
                "when",
            ]
            has_timing_phrase = any(
                trigger.lower().startswith(phrase) for phrase in timing_phrases
            )

            # If trigger doesn't start with a timing phrase, try to fix it
            if not has_timing_phrase and ":" in trigger:
                # Sometimes the format comes back wrong with "Trigger: Prior to..." instead of just "Prior to..."
                trigger = trigger.split(":", 1)[1].strip()
                extracted_data["trigger"] = trigger

        # Checking whether the condition is actionable or not
        enforcing_department = extracted_data.get("enforcing_department", "").lower()
        validating_department = extracted_data.get("validating_department", "").lower()

        # Check for trigger phrase in condition
        trigger_pattern = r"\b(prior to|before|at(?: the)? time of)\b"
        trigger_availability = bool(
            re.search(trigger_pattern, extracted_data.get("trigger", ""), re.IGNORECASE)
        )

        # Determine if actionable based on departments and trigger
        is_actionable = (
            enforcing_department not in ["none", "none specified", "n/a"]
            or validating_department not in ["none", "none specified", "n/a"]
        ) and trigger_availability

        # Separate question and word
        question = extracted_data.pop("question", None)
        word = extracted_data.pop("word", None)

        return question, word, is_actionable, extracted_data

    def _generate_sync(
        self, condition, common_points, previous_generated_conditions, description
    ) -> type:

        already_generated_questions = []
        for qu in previous_generated_conditions:
            temp = {}
            temp["question"] = qu.get("question", "")
            temp["point"] = qu.get("point", "")
            temp["accepted"] = qu.get("accepted", False)
            already_generated_questions.append(temp)

        questions_generated = json.dumps(already_generated_questions, indent=4)

        messages = [
            {"role": "system", "content": self.system_prompt},
            {
                "role": "user",
                "content": self.user_prompt.format(
                    point=condition,
                    questions_already_generated=questions_generated,
                    common_points=common_points,
                    description=description,
                ),
            },
        ]
        print("######### QUESTIONS GENERATION PROMPT: ", messages)
        try:
            completion_result = self.llm.llm_chat_completions(
                messages=messages, model="claude-3-haiku-20240307"
            )
            content = (
                completion_result.choices[0].message.content
                if completion_result.choices
                else ""
            )
        except Exception as e:
            print(f"Error calling LLM: {e}")
            return None

        extracted_data = {}
        for key, pattern in patterns.items():
            matches = re.findall(pattern, content)
            extracted_data[key] = matches[-1].strip() if matches else None

        # Validate trigger format
        if extracted_data.get("trigger"):
            trigger = extracted_data.get("trigger")
            # Check if trigger starts with a timing phrase
            timing_phrases = [
                "prior to",
                "before",
                "at time of",
                "during",
                "after",
                "upon",
                "when",
            ]
            has_timing_phrase = any(
                trigger.lower().startswith(phrase) for phrase in timing_phrases
            )

            # If trigger doesn't start with a timing phrase, try to fix it
            if not has_timing_phrase and ":" in trigger:
                # Sometimes the format comes back wrong with "Trigger: Prior to..." instead of just "Prior to..."
                trigger = trigger.split(":", 1)[1].strip()
                extracted_data["trigger"] = trigger

        question_bool = extracted_data.get("question_generated_pattern", "").lower()
        if question_bool and "yes" in question_bool:
            question_bool = True
        else:
            question_bool = False

        # Checking whether the condition is actionable or not
        enforcing_department = extracted_data.get("enforcing_department", "").lower()
        validating_department = extracted_data.get("validating_department", "").lower()

        # Check for trigger phrase in condition
        trigger_pattern = r"\b(prior to|before|at(?: the)? time of)\b"
        trigger_availability = bool(
            re.search(trigger_pattern, extracted_data.get("trigger", ""), re.IGNORECASE)
        )

        # Determine if actionable based on departments and trigger
        is_actionable = (
            enforcing_department not in ["none", "none specified", "n/a"]
            or validating_department not in ["none", "none specified", "n/a"]
        ) and trigger_availability

        # Separate question and word
        question = extracted_data.pop("question", None)
        word = extracted_data.pop("word", None)
        point = extracted_data.pop("point", None)

        return question, word, is_actionable, extracted_data, question_bool, point

    async def _store(self, condition) -> None:
        await self.database.upsert(data=condition)

    async def generate_and_store(self, conditions):
        idx = 0
        question_batch = {}
        for layout_id, condition in conditions.items():
            question, word, is_actionable, extracted_data = await self._generate(
                condition["data"]
            )
            if question is not None and word is not None:
                idx += 1
                if idx % self.batch == 0:
                    await self._store(question_batch)
                    question_batch = {}
                else:
                    question_batch[layout_id] = {
                        "question": question,
                        "word": word,
                        "header": condition["header"].lower(),
                        "point": condition["data"],
                    }
        else:
            if len(question_batch) > 0:
                await self._store(question_batch)
