from dataclasses import dataclass, field
from collections import defaultdict, Counter
from app.prompts.kg.lightrag_prompts import extract_entities
from lightrag.prompt import GRAPH_FIELD_SEP, PROMPTS
import traceback
import asyncio
from datetime import datetime
import copy
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
import re
from lightrag.utils import (
    clean_str,
    compute_mdhash_id,
    is_float_regex,
    split_string_by_multi_markers,
)
from enum import Enum


class ProcessStatus(int, Enum):
    IN_PROGRESS = 0
    COMPLETED = 2
    ERROR = 4


class LengthMismatchError(Exception):
    pass


class EntityExtractionError(Exception):
    def __init__(self, stage, message="Error during entity extraction process"):
        self.stage = stage
        self.message = message
        super().__init__(f"{self.message} at stage: {self.stage}")


@dataclass
class EntityExtrator:
    global_config: dict
    knowledge_graph_inst: object
    entity_vdb: object
    relationships_vdb: object
    lightrag_batch: object
    full_docs: object
    file_name: str
    extraction_batch_size: int = field(default=3)
    graphdb_batch_size: int = field(default=100)
    chunks_list = []

    async def _merge_nodes_then_upsert(
        self,
        nodes_data: list[dict],
    ):
        try:
            already_done = {}
            already_keys = {
                "already_entitiy_types": [],
                "already_source_ids": [],
                "already_description": [],
            }

            already_node = await self.knowledge_graph_inst.get_nodes_by_labels(
                nodes_data.keys()
            )
            if already_node is not None:
                for node in already_node:
                    already_done.update({node["entity_type"]: already_keys})
                    already_done[node["entity_type"]]["already_entitiy_types"].append(
                        node["entity_type"]
                    )
                    already_done[node["entity_type"]]["already_source_ids"].extend(
                        split_string_by_multi_markers(
                            node["source_id"], [GRAPH_FIELD_SEP]
                        )
                    )
                    already_done[node["entity_type"]]["already_description"].append(
                        already_node["description"]
                    )
            chunk_header_map = {}
            print("Storing chunk_id and header['name'] in a dictionary")
            for i in self.chunks_list:
                for chunk_id, chunk_data in i:
                    if 'header' in chunk_data and chunk_data['header'] is not None:
                        name = chunk_data["header"].get("name", "")
                        chunk_header_map[chunk_id] = name
                        print(f"Stored: {chunk_id} -> {chunk_header_map[chunk_id]}")

            final_nodes = []
            for each_nodes in nodes_data.keys():
                entity_type = sorted(
                    Counter(
                        [dp["entity_type"] for dp in nodes_data[each_nodes]]
                        + already_done.get(each_nodes, {}).get(
                            "already_entitiy_types", []
                        )
                    ).items(),
                    key=lambda x: x[1],
                    reverse=True,
                )[0][0]
                description = GRAPH_FIELD_SEP.join(
                    sorted(
                        set(
                            [dp["description"] for dp in nodes_data[each_nodes]]
                            + already_done.get(each_nodes, {}).get(
                                "already_description", []
                            )
                        )
                    )
                )
                source_id = GRAPH_FIELD_SEP.join(
                    set(
                        [dp["source_id"] for dp in nodes_data[each_nodes]]
                        + already_done.get(each_nodes, {}).get("already_source_ids", [])
                    )
                )
                # Split source_id into individual chunk_ids
                source_ids = source_id.split(GRAPH_FIELD_SEP)
                print("SOURCE_IDS", source_ids)
                print("CHUNK_HEDAER MAP", chunk_header_map)

                # Initialize the section
                sections = set()

                for sid in source_ids:
                    print(sid, sid in chunk_header_map, chunk_header_map[sid])
                    if sid in chunk_header_map:
                        sections.add(chunk_header_map[sid])

                section = GRAPH_FIELD_SEP.join(list(sections))

                node_data = {
                    "entity_type": entity_type,
                    "description": description,
                    "source_id": source_id,
                    "entity_name": nodes_data[each_nodes][0]["entity_name"],
                    "source_sections": section,
                }
                final_nodes.append({"node_id": each_nodes, "node_data": node_data})

            final_nodes_batches = [
                final_nodes[i : i + self.graphdb_batch_size]
                for i in range(0, len(final_nodes), self.graphdb_batch_size)
            ]
            for each_batch in final_nodes_batches:
                await self.knowledge_graph_inst.upsert_nodes(each_batch)
            return final_nodes
        except Exception as e:
            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.entity_graph_creation.status": ProcessStatus.ERROR,
                    "status.entity_graph_creation.error": f"Error - {traceback.format_exc()}",
                    "status.entity_graph_creation.end_at": datetime.now(),
                },
            )
            raise EntityExtractionError(stage="merge_nodes_then_upsert") from e

    async def _merge_edges_then_upsert(
        self,
        edges_data: list[dict],
    ):
        try:
            already_done = {}
            already_keys = {
                "already_weights": [],
                "already_source_ids": [],
                "already_description": [],
                "already_keywords": [],
            }

            already_edges = await self.knowledge_graph_inst.has_edges(
                [(k[0], k[1]) for k in edges_data.keys()]
            )
            if already_edges:
                for edge in already_edges:
                    already_done.update(
                        {(edge["source"], edge["target"]): already_keys}
                    )
                    already_done[(edge["source"], edge["target"])][
                        "already_weights"
                    ].append(edge["edge_properties"]["weight"])
                    already_done[(edge["source"], edge["target"])][
                        "already_source_ids"
                    ].extend(
                        split_string_by_multi_markers(
                            edge["edge_properties"]["source_id"], [GRAPH_FIELD_SEP]
                        )
                    )
                    already_done[(edge["source"], edge["target"])][
                        "already_description"
                    ].append(edge["edge_properties"]["description"])
                    already_done[(edge["source"], edge["target"])][
                        "already_keywords"
                    ].extend(
                        split_string_by_multi_markers(
                            edge["edge_properties"]["keywords"], [GRAPH_FIELD_SEP]
                        )
                    )

            final_relations = []
            for each_relation in edges_data.keys():
                cur_key = (each_relation[0], each_relation[1])
                weight = [
                    dp["weight"] for dp in edges_data[each_relation]
                ] + already_done.get(cur_key, {}).get("already_weights", [])
                weight = sum(weight) / len(weight)

                description = GRAPH_FIELD_SEP.join(
                    sorted(
                        set(
                            [dp["description"] for dp in edges_data[each_relation]]
                            + already_done.get(cur_key, {}).get(
                                "already_description", []
                            )
                        )
                    )
                )
                keywords = GRAPH_FIELD_SEP.join(
                    sorted(
                        set(
                            [dp["keywords"] for dp in edges_data[each_relation]]
                            + already_done.get(cur_key, {}).get("already_keywords", [])
                        )
                    )
                )
                source_id = GRAPH_FIELD_SEP.join(
                    set(
                        [dp["source_id"] for dp in edges_data[each_relation]]
                        + already_done.get(cur_key, {}).get("already_source_ids", [])
                    )
                )

                for need_insert_id in [cur_key[0], cur_key[1]]:
                    if not (await self.knowledge_graph_inst.has_node(need_insert_id)):
                        await self.knowledge_graph_inst.upsert_node(
                            need_insert_id,
                            node_data={
                                "source_id": source_id,
                                "description": description,
                                "entity_type": '"UNKNOWN"',
                            },
                        )
                edge_data = {
                    "src_id": cur_key[0],
                    "tgt_id": cur_key[1],
                    "description": description,
                    "keywords": keywords,
                    "weight": weight,
                    "source_id": source_id,
                    "src_entity_name": cur_key[0],
                    "tgt_entity_name": cur_key[1],
                }

                final_relations.append(edge_data)
            upsert_data = copy.deepcopy(final_relations)
            batch_size = 100
            upsert_data_batches = [
                upsert_data[i : i + batch_size]
                for i in range(0, len(upsert_data), batch_size)
            ]
            for each_batch in upsert_data_batches:
                await self.knowledge_graph_inst.upsert_edges(each_batch)

            if self.relationships_vdb is not None:
                data_for_vdb = {
                    compute_mdhash_id(dp["src_id"] + dp["tgt_id"], prefix="rel-"): {
                        "src_id": dp["src_id"],
                        "tgt_id": dp["tgt_id"],
                        "content": dp["keywords"]
                        + dp["src_id"]
                        + dp["tgt_id"]
                        + dp["description"],
                        "src_entity_name": dp["src_entity_name"],
                        "tgt_entity_name": dp["tgt_entity_name"],
                    }
                    for dp in final_relations
                }
                await self.relationships_vdb.upsert(data_for_vdb)

            return final_relations
        except Exception as e:
            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.relationship_graph_creation.status": ProcessStatus.ERROR,
                    "status.relationship_graph_creation.error": f"Error - {traceback.format_exc()}",
                    "status.relationship_graph_creation.end_at": datetime.now(),
                },
            )
            raise EntityExtractionError(stage="merge_edges_then_upsert") from e

    async def extract_entities(self, chunks):
        try:
            ordered_chunks = list(chunks.items())
            self.already_processed = 0
            self.already_entities = 0
            self.already_relations = 0
            await self.prepare_prompt()

            batches = [
                ordered_chunks[i : i + self.extraction_batch_size]
                for i in range(0, len(ordered_chunks), self.extraction_batch_size)
            ]
            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.process_batch.status": ProcessStatus.IN_PROGRESS,
                    "status.process_batch.start_at": datetime.now(),
                },
            )

            results = await asyncio.gather(
                *[
                    self._process_batch_content(c, batch_id)
                    for batch_id, c in enumerate(batches)
                ],
            )
            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.process_batch.status": ProcessStatus.COMPLETED,
                    "status.process_batch.end_at": datetime.now(),
                },
            )

            print()  # clear the progress bar
            maybe_nodes = defaultdict(list)
            maybe_edges = defaultdict(list)
            for m_nodes, m_edges in results:
                for k, v in m_nodes.items():
                    maybe_nodes[k].extend(v)
                for k, v in m_edges.items():
                    maybe_edges[tuple(sorted(k))].extend(v)

            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.entity_graph_creation.status": ProcessStatus.IN_PROGRESS,
                    "status.entity_graph_creation.start_at": datetime.now(),
                },
            )

            all_entities_data = await self._merge_nodes_then_upsert(maybe_nodes)
            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.entity_graph_creation.status": ProcessStatus.COMPLETED,
                    "status.entity_graph_creation.end_at": datetime.now(),
                    "status.relationship_graph_creation.status": ProcessStatus.IN_PROGRESS,
                    "status.relationship_graph_creation.start_at": datetime.now(),
                },
            )
            all_relationships_data = await self._merge_edges_then_upsert(maybe_edges)

            if not len(all_entities_data):
                print("Didn't extract any entities, maybe your LLM is not working")
                return None
            if not len(all_relationships_data):
                print("Didn't extract any relationships, maybe your LLM is not working")
                return None

            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.relationship_graph_creation.status": ProcessStatus.COMPLETED,
                    "status.relationship_graph_creation.end_at": datetime.now(),
                    "status.entity_vector_creation.status": ProcessStatus.IN_PROGRESS,
                    "status.entity_vector_creation.start_at": datetime.now(),
                },
            )

            if self.entity_vdb is not None:
                print("Preparing entity data for vector storage...")
                data_for_vdb = {
                    compute_mdhash_id(dp["node_id"], prefix="ent-"): {
                        "content": dp["node_id"] + dp["node_data"]["description"],
                        "entity_name": dp["node_id"],
                    }
                    for dp in all_entities_data
                }
                print(f"Inserting {len(data_for_vdb)} entities into vector storage")
                print("Sample entity data:", next(iter(data_for_vdb.values())) if data_for_vdb else "No entities")
                try:
                    await self.entity_vdb.upsert(data_for_vdb)
                    print("Entity insertion complete")
                except Exception as e:
                    print(f"Error inserting entities: {str(e)}")
                    raise

            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.entity_vector_creation.status": ProcessStatus.COMPLETED,
                    "status.entity_vector_creation.end_at": datetime.now(),
                    "status.relationship_vector_creation.status": ProcessStatus.IN_PROGRESS,
                    "status.relationship_vector_creation.start_at": datetime.now(),
                },
            )

            if self.relationships_vdb is not None:
                print("Preparing relationship data for vector storage...")
                data_for_vdb = {
                    compute_mdhash_id(dp["src_id"] + dp["tgt_id"], prefix="rel-"): {
                        "src_id": dp["src_id"],
                        "tgt_id": dp["tgt_id"],
                        "content": dp["keywords"] + dp["src_id"] + dp["tgt_id"] + dp["description"],
                        "src_entity_name": dp["src_entity_name"],
                        "tgt_entity_name": dp["tgt_entity_name"],
                    }
                    for dp in all_relationships_data
                }
                print(f"Inserting {len(data_for_vdb)} relationships into vector storage")
                print("Sample relationship data:", next(iter(data_for_vdb.values())) if data_for_vdb else "No relationships")
                try:
                    await self.relationships_vdb.upsert(data_for_vdb)
                    print("Relationship insertion complete")
                except Exception as e:
                    print(f"Error inserting relationships: {str(e)}")
                    raise

            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.relationship_vector_creation.status": ProcessStatus.COMPLETED,
                    "status.relationship_vector_creation.end_at": datetime.now(),
                    "status.entity_extraction.status": ProcessStatus.COMPLETED,
                    "status.entity_extraction.end_at": datetime.now(),
                },
            )

            return self.knowledge_graph_inst
        except Exception as e:
            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.entity_extraction.status": ProcessStatus.ERROR,
                    "status.entity_extraction.end_at": datetime.now(),
                    "status.entity_extraction.error": str(e),
                },
            )
            raise EntityExtractionError(stage="extract_entities") from e

    async def prepare_prompt(self):
        self.entity_extract_prompt = extract_entities
        self.context_base = {
            "tuple_delimiter": PROMPTS["DEFAULT_TUPLE_DELIMITER"],
            "record_delimiter": PROMPTS["DEFAULT_RECORD_DELIMITER"],
            "completion_delimiter": PROMPTS["DEFAULT_COMPLETION_DELIMITER"],
        }

    async def _handle_entity_extraction(
        self,
        record_attributes: list[str],
        chunk_key: str,
    ):
        def _process_entity(record):
            if len(record) < 4 or record[0] != "entity":
                return None
            entity_name = clean_str(record[1].upper())
            if not entity_name.strip():
                return None
            entity_type = clean_str(record[2].upper())
            entity_description = clean_str(record[3])
            entity_source_id = chunk_key
            return {
                "entity_name": entity_name,
                "entity_type": entity_type,
                "description": entity_description,
                "source_id": entity_source_id,
            }

        return [
            entity
            for entity in map(_process_entity, record_attributes)
            if entity is not None
        ]

    async def _handle_relationship_extraction(
        self,
        record_attributes: list[str],
        chunk_key: str,
    ):
        def _process_relationship(record):
            if len(record) < 5 or record[0] != "relationship":
                return None
            source = clean_str(record[1].upper())
            target = clean_str(record[2].upper())
            edge_description = clean_str(record[3])
            edge_keywords = clean_str(record[4])
            edge_source_id = chunk_key
            weight = float(record[-1]) if is_float_regex(record[-1]) else 1.0
            return {
                "src_id": source,
                "tgt_id": target,
                "weight": weight,
                "description": edge_description,
                "keywords": edge_keywords,
                "source_id": edge_source_id,
            }

        return [
            relationship
            for relationship in map(_process_relationship, record_attributes)
            if relationship is not None
        ]

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(LengthMismatchError),
    )
    async def get_result_from_llm(self, hint_prompt: list, batch_content: list):
        try:
            final_result = await self.global_config["llm_model_func"](hint_prompt)
            records = final_result.split(self.context_base["completion_delimiter"])
            print(len(records), len(batch_content), "len records")
            if len(records) - 1 != len(batch_content):
                print(
                    f"Length mismatch: {len(records) - 1} != {len(batch_content)}. "
                    "Raising error to trigger retry."
                )
                raise LengthMismatchError(
                    "Length mismatch between records and batch_content."
                )
            return records
        except LengthMismatchError as e:
            raise e
        except Exception as e:
            raise EntityExtractionError(stage="get_result_from_llm") from e

    async def _parse_llm_output(self, records):
        try:
            records = [
                rec.split(self.context_base["record_delimiter"]) for rec in records[:-1]
            ]
            parse_record = []
            for each_record in records:
                chunk_list = []
                for each_chunk in each_record:
                    record = re.search(r"\((.*)\)", each_chunk.replace('"', ""))
                    if record is None:
                        continue
                    record = record.group(1)
                    record_attributes = split_string_by_multi_markers(
                        record, [self.context_base["tuple_delimiter"]]
                    )
                    chunk_list.append(record_attributes)
                parse_record.append(chunk_list)
            return parse_record
        except Exception as e:
            raise EntityExtractionError(stage="_parse_llm_output") from e

    async def _process_entity_and_relationships(self, parse_record, chunks):
        try:
            maybe_nodes = defaultdict(list)
            maybe_edges = defaultdict(list)
            self.chunks_list.append(chunks)
            self.chunks = chunks
            for key, chk_record in zip(
                [chunk_key for chunk_key, chunk_dp in chunks], parse_record
            ):
                record_attributes = chk_record
                entities = [
                    record for record in record_attributes if "entity" in record
                ]
                relationships = [
                    record for record in record_attributes if "relationship" in record
                ]

                if entities:
                    if_entities = await self._handle_entity_extraction(
                        entities,
                        key,
                    )

                    if if_entities is not None:
                        for entity in if_entities:
                            maybe_nodes[entity["entity_name"]].append(entity)

                if relationships:
                    if_relation = await self._handle_relationship_extraction(
                        relationships,
                        key,
                    )
                    if if_relation is not None:
                        for relation in if_relation:
                            maybe_edges[
                                (relation["src_id"], relation["tgt_id"])
                            ].append(relation)
            return maybe_nodes, maybe_edges
        except Exception as e:
            raise EntityExtractionError(
                stage="_process_entity_and_relationships"
            ) from e

    async def _process_batch_content(self, chunks, batch_id):
        """
        Processes a batch of chunks in a single API call and returns the combined results.

        Args:
            chunks (list[tuple[str, TextChunkSchema]]): List of chunks to process.

        Returns:
            tuple[dict, dict]: Combined results containing nodes and edges from all chunks.
        """
        try:
            # Combine all content into a single input
            batch_content = [
                {
                    "text": chunk_dp["content"],
                    "entity_types": ",".join(PROMPTS["DEFAULT_ENTITY_TYPES"]),
                }
                for chunk_key, chunk_dp in chunks
            ]

            # Format the prompt for the entire batch
            hint_prompt = self.entity_extract_prompt.format(
                **self.context_base, inputs=batch_content, count=len(batch_content),
            )

            records = await self.get_result_from_llm(hint_prompt, batch_content)
            parse_record = await self._parse_llm_output(records)

            maybe_nodes, maybe_edges = await self._process_entity_and_relationships(
                parse_record=parse_record, chunks=chunks
            )

            self.already_processed += len(chunks)
            self.already_entities += len(maybe_nodes)
            self.already_relations += len(maybe_edges)

            print(
                f"Processed {self.already_processed} chunks, {self.already_entities} entities(duplicated), {self.already_relations} relations(duplicated)"
            )

            return dict(maybe_nodes), dict(maybe_edges)
        except Exception as e:
            await self.full_docs.update_one(
                {"file_name": self.file_name},
                {
                    "status.process_batch.status": ProcessStatus.ERROR,
                    "status.process_batch.error": f"Error - {traceback.format_exc()}",
                    "status.process_batch.end_at": datetime.now(),
                },
            )
            raise EntityExtractionError(stage="_process_batch_content") from e
