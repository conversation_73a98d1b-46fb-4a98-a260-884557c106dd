from pymilvus import (
    Collection,
    connections,
    utility,
    CollectionSchema,
    FieldSchema,
    DataType,
)
import numpy as np
import os
from dataclasses import dataclass
from lightrag.base import BaseVectorStorage
from typing import Dict
from pymongo import MongoClient
from app.core.config import settings


@dataclass
class SearchResult:
    id: str
    score: float
    content: str
    source: str
    metadata: Dict
    meta: Dict


@dataclass
class MilvusVectorDBStorage(BaseVectorStorage):
    cosine_better_than_threshold: float = 0.25
    mongo_client: MongoClient = MongoClient(settings.MONGO_URL)
    mongo_db_name: str = "lightrag"
    mongo_collection_name: str = "text_chunks"

    def __post_init__(self):
        # Initialize Milvus connection
        self.collection_name: str = self.namespace
        connections.connect(
            alias="default",
            token=settings.MILVUS_TOKEN,
            uri=settings.MILVUS_URI,
            db_name=settings.MILVUS_DEFAULT_DATABASE,
        )

        # Set the collection name and configure other parameters
        self._client_file_name = os.path.join("temp", f"vdb_{self.namespace}.json")
        self._max_batch_size = 32
        self.cosine_better_than_threshold = self.cosine_better_than_threshold

        # Create collection if it doesn't exist
        if not utility.has_collection(self.collection_name):
            self.create_collection()
        self.collection = Collection(self.collection_name)
        self.mongo_chunks_collection = self.mongo_client[self.mongo_db_name][self.mongo_collection_name]
        # Load the collection into memory
        self.collection.load()

    def create_collection(self):
        """Create a Milvus collection with embedding dimension and schema"""
        # Base fields that all collections have
        fields = [
            FieldSchema(
                name="id",
                dtype=DataType.VARCHAR,
                is_primary=True,
                auto_id=False,
                max_length=256,
            ),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=1536),
        ]

        # Add specific fields based on collection type
        if self.collection_name == "entities":
            fields.append(
                FieldSchema(
                    name="entity_name",
                    dtype=DataType.VARCHAR,
                    max_length=256,
                )
            )
        elif self.collection_name == "relationships":
            fields.extend([
                FieldSchema(
                    name="src_id",
                    dtype=DataType.VARCHAR,
                    max_length=256,
                ),
                FieldSchema(
                    name="tgt_id",
                    dtype=DataType.VARCHAR,
                    max_length=256,
                ),
                # Add fields for entity names in relationships
                FieldSchema(
                    name="src_entity_name",
                    dtype=DataType.VARCHAR,
                    max_length=256,
                ),
                FieldSchema(
                    name="tgt_entity_name",
                    dtype=DataType.VARCHAR,
                    max_length=256,
                ),
            ])

        schema = CollectionSchema(fields)
        self.collection = Collection(name=self.collection_name, schema=schema)

        # Create an index
        index_params = {
            "index_type": "IVF_FLAT",
            "metric_type": "COSINE",
            "params": {"nlist": 100},
        }
        self.collection.create_index(field_name="embedding", index_params=index_params)

    async def get_by_id(self, id):
        results = self.collection.query(
            expr=f"id == '{id}'",
            output_fields=["meta"],
        )
        return results[0]["meta"]

    async def upsert(self, data: dict):
        ids = []
        embeddings = []
        entity_names = [] if self.collection_name == "entities" else None
        src_ids = [] if self.collection_name == "relationships" else None
        tgt_ids = [] if self.collection_name == "relationships" else None
        # Initialize entity name lists for relationships
        src_entity_names = [] if self.collection_name == "relationships" else None
        tgt_entity_names = [] if self.collection_name == "relationships" else None
        batch_size = 30
        async_tasks = []
        i = 0

        for k, v in data.items():
            print(i)
            i += 1
            async_tasks.append((k, v))

            if len(async_tasks) == batch_size or k == list(data.keys())[-1]:
                # Process the batch
                await self.process_batch(
                    async_tasks,
                    ids,
                    embeddings,
                    entity_names,
                    src_ids,
                    tgt_ids,
                    src_entity_names,  # Pass the new parameters
                    tgt_entity_names
                )

                # Clear for next batch
                async_tasks.clear()
                ids.clear()
                embeddings.clear()
                if entity_names is not None:
                    entity_names.clear()
                if src_ids is not None:
                    src_ids.clear()
                if tgt_ids is not None:
                    tgt_ids.clear()
                if src_entity_names is not None:
                    src_entity_names.clear()
                if tgt_entity_names is not None:
                    tgt_entity_names.clear()

            i += 1

        # Process any remaining tasks
        if async_tasks:
            await self.process_batch(
                async_tasks,
                ids,
                embeddings,
                entity_names,
                src_ids,
                tgt_ids,
                src_entity_names,  # Pass the new parameters
                tgt_entity_names
            )

        print("Finished inserting all records into the collection.")
        return {"status": "success", "inserted_count": len(data)}

    async def process_batch(
        self,
        async_tasks,
        ids,
        embeddings,
        entity_names=None,
        src_ids=None,
        tgt_ids=None,
        src_entity_names=None,
        tgt_entity_names=None
    ):
        """Process batch of async tasks and insert into Milvus."""
        print(f"Processing batch for collection: {self.collection_name}")
        contents = [task[1]["content"] for task in async_tasks]
        results = await self.embedding_func(contents)
        for (k, v), embedding in zip(async_tasks, results):
            ids.append(k)
            embeddings.append(np.array(embedding))
            # Handle fields based on collection type
            if self.collection_name == "entities" and entity_names is not None:
                entity_name = v.get("entity_name", "")
                print(f"Adding entity: {entity_name}")
                entity_names.append(entity_name)
            elif self.collection_name == "relationships":
                if src_ids is not None:
                    src_id = v.get("src_id", "")
                    print(f"Adding source ID: {src_id}")
                    src_ids.append(src_id)
                if tgt_ids is not None:
                    tgt_id = v.get("tgt_id", "")
                    print(f"Adding target ID: {tgt_id}")
                    tgt_ids.append(tgt_id)
                if src_entity_names is not None:
                    src_entity_name = v.get("src_entity_name", "")
                    print(f"Adding source entity name: {src_entity_name}")
                    src_entity_names.append(src_entity_name)
                if tgt_entity_names is not None:
                    tgt_entity_name = v.get("tgt_entity_name", "")
                    print(f"Adding target entity name: {tgt_entity_name}")
                    tgt_entity_names.append(tgt_entity_name)

        # Prepare insert data based on collection type
        insert_data = [ids, embeddings]
        if self.collection_name == "entities" and entity_names is not None:
            insert_data.append(entity_names)
        elif self.collection_name == "relationships":
            if src_ids is not None:
                insert_data.append(src_ids)
            if tgt_ids is not None:
                insert_data.append(tgt_ids)
            if src_entity_names is not None:
                insert_data.append(src_entity_names)
            if tgt_entity_names is not None:
                insert_data.append(tgt_entity_names)

        print(f"Inserting batch with {len(ids)} records")
        print("Insert data structure:", [len(d) for d in insert_data])
        # Insert batch with appropriate fields
        try:
            self.collection.insert(insert_data)
            print("Batch insertion successful")
        except Exception as e:
            print(f"Error inserting batch: {str(e)}")
            raise

    async def mongo_search(self, chunk_ids: list):
        chunks = {}
        for chunk in self.mongo_chunks_collection.find({'_id': {"$in": chunk_ids}}):
            chunks[chunk['_id']] = chunk
        return chunks

    async def query(self, query: str, top_k=5):
        print(f"Querying for {top_k} nearest neighbors.")
        embedding = await self.embedding_func(query)
        embedding = embedding[0]

        # Determine output fields based on collection type
        output_fields = ["id"]
        if self.collection_name == "entities":
            output_fields.extend(["entity_name"])
        elif self.collection_name == "relationships":
            output_fields.extend([
                "src_id",
                "tgt_id",
                "src_entity_name",
                "tgt_entity_name"
            ])

        print(f"Querying {self.collection_name} with fields: {output_fields}")

        # Perform similarity search in Milvus
        results = self.collection.search(
            [embedding],
            "embedding",
            param={"metric_type": "COSINE", "params": {"nprobe": 10}},
            limit=top_k,
            expr=None,
            output_fields=output_fields,
        )
        search_results = []
        chunk_ids = []

        for hits in results:
            for hit in hits:
                if hit.distance > 0.3:
                    chunk_ids.append(hit.id)
        mongo_recds = await self.mongo_search(chunk_ids)
        for hits in results:
            for hit in hits:
                if hit.distance > self.cosine_better_than_threshold:
                    mongo_recd = mongo_recds.get(hit.id, {})
                    # Create base result structure
                    search_result = {
                        "id": str(hit.id),
                        "score": float(hit.distance),
                        "file_name": mongo_recd.get("file_name", ""),
                        "text": {
                            "data": mongo_recd.get("content", ""),
                            "coordinates": mongo_recd.get("coordinates", {}),
                            "page": mongo_recd.get("page", "")
                        }
                    }

                    # Add entity-specific fields based on collection type
                    if self.collection_name == "entities":
                        # Access entity_name directly from hit
                        search_result["entity_name"] = hit.entity_name
                    elif self.collection_name == "relationships":
                        # Access relationship fields directly from hit
                        search_result.update({
                            "entity_name": hit.src_entity_name,  # Use source entity name as primary
                            "src_entity_name": hit.src_entity_name,
                            "tgt_entity_name": hit.tgt_entity_name,
                            "src_id": hit.src_id,
                            "tgt_id": hit.tgt_id
                        })

                    print(f"Search result for hit {hit.id}:", search_result)
                    search_results.append(search_result)

        print(f"Returning {len(search_results)} results")
        return search_results

    async def index_done_callback(self):
        # Milvus handles indexing asynchronously, but you can use `flush` or other techniques
        # to ensure indexing is done.
        print("Indexing completed. Flushing collection to persist data.")
        self.collection.flush()  # Ensure all data is written to the storage backend
