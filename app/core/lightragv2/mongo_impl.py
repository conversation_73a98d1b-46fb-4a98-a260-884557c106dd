from lightrag.base import BaseKVStorage
from pymongo import MongoClient
from pymongo.operations import UpdateOne
from dataclasses import dataclass
from typing import List, Dict, Optional
import os

print("MONGO_DATABASE", os.environ.get("MONGO_DATABASE"))


@dataclass
class MongoKVStorage(BaseKVStorage):
    client: MongoClient = MongoClient(os.environ.get("MONGO_URI"))
    db_name: str = os.environ.get("MONGO_DATABASE", "dummy")

    def __post_init__(self):
        # Initialize MongoDB client and access the specific database and collection
        self.collection_name: str = self.namespace
        self.db = self.client[self.db_name]
        self.collection = self.db[self.collection_name]
        print(
            f"Connected to MongoDB database: {self.db_name}, collection: {self.collection_name}",
        )

    async def all_keys(self) -> List[str]:
        # Retrieve all keys from the MongoDB collection
        keys = self.collection.distinct("_id")
        return keys

    async def index_done_callback(self):
        # In MongoDB, we don't need to explicitly save the data as we do in JSON file storage,
        # since MongoDB handles automatic persistence. This can be used to trigger additional operations if needed.
        print("Indexing complete.")

    async def get_by_id(self, id: str) -> Optional[Dict]:
        # Fetch a document by its '_id' from the MongoDB collection
        result = self.collection.find_one({"_id": id})
        return result

    async def get_one(self, filter: dict, project: dict) -> Optional[Dict]:
        # Fetch a document by its '_id' from the MongoDB collection
        result = self.collection.find_one(filter, project)
        return result

    async def get_by_ids(
        self, ids: List[str], fields: Optional[List[str]] = None,
    ) -> List[Optional[Dict]]:
        # Fetch multiple documents by their '_id' from MongoDB
        projection = {field: 1 for field in fields} if fields else None
        documents = list(self.collection.find({"_id": {"$in": ids}}, projection))
        documents = sorted(documents, key=lambda doc: ids.index(doc["_id"]))
        return documents

    async def filter_keys(self, data: List[str]) -> set:
        # Check which of the provided keys don't exist in the MongoDB collection
        existing_keys = self.collection.distinct("_id")
        return set(data) - set(existing_keys)

    async def update_one(self, filter: dict, data: Dict) -> Dict:
        # Update a document in the MongoDB collection
        result = self.collection.update_one(filter, {"$set": data}, upsert=True)
        print(f"Updated {result.modified_count} document.")
        return

    async def upsert(self, data: Dict[str, Dict]) -> Dict[str, Dict]:
        # Perform an upsert operation: insert new documents or update existing ones
        bulk_operations = []
        for key, value in data.items():
            bulk_operations.append(
                UpdateOne({"_id": key}, {"$set": value}, upsert=True),
            )
        if bulk_operations:
            result = self.collection.bulk_write(bulk_operations)
            print(f"Upserted {result.upserted_count} documents.")
        return data

    async def drop(self):
        # Drop the entire collection from MongoDB (similar to clearing the data)
        self.collection.drop()
        print(f"Collection {self.collection_name} dropped.")
