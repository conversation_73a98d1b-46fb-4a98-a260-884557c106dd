from app.core.decorators import traced
import uuid
import json
import asyncio
from typing import Type
from collections import defaultdict
from dataclasses import asdict
from lightrag import LightRAG
from lightrag.base import BaseGraphStorage, QueryParam
# from app.core.lightragv2.entity_extractor import EntityExtrator
from lightrag.utils import logger, compute_mdhash_id
from app.core.lightragv2.neo4j_impl import CustomNeo4JStorage
from app.core.lightragv2.milvus_impl import (
    MilvusVectorDBStorage,
)  # Should from the modified lightragv2
from app.core.lightragv2.mongo_impl import (
    MongoKVStorage,
)  # Should from the modified lightragv2
from app.core.function_values import CHECK_FUNCTIONS, STOP_FUNCTIONS, process_states
from app.core.function_tracer import FunctionTracer, Function, Step
from enum import Enum
import traceback
import re
from datetime import datetime
from app.crud.milvus.milvus_store import MilvusStore
from app.core.embedding import OpenAIEmbeddings
from app.core.config import settings
from typing import List, Dict
from pymongo import MongoClient
from pymongo.operations import UpdateOne
from app.core.lightragv2.question_generator import QuestionGenerator
from app.core.detail_extractor import extract_first_page, analyze_mpud_document, convert_to_markdown
from app.crud.neo4j.mpud_toolkit import init_predictor


class ProcessStatus(int, Enum):
    IN_PROGRESS = 0
    COMPLETED = 2
    ERROR = 4


def always_get_an_event_loop() -> asyncio.AbstractEventLoop:
    try:
        return asyncio.get_event_loop()

    except RuntimeError:
        logger.info("Creating a new event loop in main thread.")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        return loop


def create_scoring_prompt(inputs: str, query: str) -> str:
    prompt = f"""Task: Generate a one-line summary and calculate relevancy score for how well the input answers the query.

Input: {inputs}
Query: {query}

Requirements:
1. Summary must:
   - Be exactly one line
   - Maximum 15 words
   - Capture core meaning
   - Use active voice
   - No redundant words

2. Score must be 0-100 based on:
   - 90-100: Directly and completely answers the question
   - 70-89: Partially answers the question with relevant details
   - 50-69: Related to the question but doesn't provide a clear answer
   - 30-49: Mentions topics related to the question without answering it
   - 0-29: Does not attempt to answer the question at all

Scoring Guidelines:
- Focus on whether the input ANSWERS the question, not just mentions related terms
- Ask yourself: "If someone asked me this question, would this input satisfy them?"
- High scores (70+) should only be given if the input provides actual answers
- Medium scores (30-69) for content that's related but doesn't answer
- Low scores (0-29) if the input doesn't help answer the question at all

Examples:
Q: "What is X?"
A: "X is a tool for..." = 90+ (direct answer)
A: "X can be used for..." = 70-89 (partial answer)
A: "X was mentioned in..." = 30-49 (related but no answer)
A: "Find X at website..." = 0-29 (no answer provided)

Return only this JSON:
{{
    "data": {{
        "oneline": "your one-line summary here",
        "score": calculated_score_number
    }}
}}

"""
    return prompt


def create_grouping_prompt(temporary_structure: str) -> str:
    prompt = f"""
You are an intelligent assistant specialized in semantic text analysis. Your task is to analyze and group text entries based SOLELY on their content and meaning, completely ignoring any metadata.

Semantic Grouping Rules:
1. Analyze ONLY the content within the "text" field
2. Group texts that:
   - Share the same main topic or subject matter
   - Are part of the same logical context
   - Reference the same specific events, decisions, or processes
   - Form part of the same administrative or legal point
   - Contain complementary information about the same matter

Strict Requirements:
- IGNORE ALL METADATA (file names, page numbers, coordinates)
- Focus only on the actual meaning and context of the text
- Do not group texts just because they appear in the same document
- Each group must have a clear thematic connection
- Keep texts separate if their connection is only superficial
- Create single-item groups if a text has no clear semantic relationship to others

For example, looking at the sample input:
- Texts about specific MPUD amendments would group together
- Texts about compliance with Pasco County regulations would group together
- Address/company information would group together
- Funding statements would be separate if not contextually linked

Output Format:
[
    [
        {{
            "text": "First text about a specific topic",
            "meta_data": {{...}}
        }},
        {{
            "text": "Second text about the same specific topic",
            "meta_data": {{...}}
        }}
    ],
    [
        {{
            "text": "Standalone text with unique topic",
            "meta_data": {{...}}
        }}
    ]
]

Input Dataset:
{json.dumps(temporary_structure)}

User Question: {{query}}

Return ONLY the JSON result in the specified format above.
"""
    return prompt


def format_md(data, file_name):
    res = ""
    if data["title"].get("data", ""):
        res += f'# {data["title"].get("data", "")}'
        res = res and res + "\n"
    if data["header"].get("data", ""):
        res += f'## {data["header"].get("data", "")}'
        res = res and res + "\n"
    res += data["text"]["data"]

    multi_line_string = "\n".join(res.splitlines())
    return multi_line_string


def parse_json_content(text):
    """
    Extracts and parses JSON content from between triple backticks.

    Args:
        text (str): Input text containing JSON wrapped in backticks

    Returns:
        dict/list: Parsed JSON object, or None if parsing fails
    """
    import re
    import json

    # Pattern to match content between triple backticks
    pattern = r"```json\s*([\s\S]*?)\s*```"

    try:
        # Find the match
        match = re.search(pattern, text)
        if match:
            # Extract the JSON content
            json_str = match.group(1).strip()
            # Parse the JSON string into a Python object
            return json.loads(json_str)
        return None
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None


pattern = r"^(\d+\.\n)+"


class CustomRag(LightRAG):
    def __init__(
        self,
        working_dir="./temp",
        vector_storage="MilvusVectorDBStorage",
        graph_storage="Neo4JStorage",
    ):
        super().__init__(
            working_dir=working_dir,
            vector_storage=vector_storage,
            graph_storage=graph_storage,
            kv_storage="MongoKVStorage",

        )
        self.super_aquery = super().aquery
        self.lightrag_batch = self.key_string_value_json_storage_cls(
            namespace="lightrag_batch",
            global_config=asdict(self),
            embedding_func=self.embedding_func,
        )
        self.questions_db = self.key_string_value_json_storage_cls(
            namespace="questions",
            global_config=asdict(self),
            embedding_func=self.embedding_func,
        )
        self.question_generator = QuestionGenerator(self.questions_db)

    def _format_data(self, results, response):
        resp = []
        for idx, each in enumerate(results):
            formatted_data = {
                "title": response["oneline"][idx],
                "score": response["score"][idx],
                "conditions": [
                    {
                        "text": each.get("text", {}).get("data", ""),
                        "meta_data": {
                            "file_name": each.get("file_name", ""),
                            "coordinates": {
                                "Width": each.get("text", {})
                                .get("coordinates", {})
                                .get("Width", 0),
                                "Height": each.get("text", {})
                                .get("coordinates", {})
                                .get("Height", 0),
                                "Left": each.get("text", {})
                                .get("coordinates", {})
                                .get("Left", 0),
                                "Top": each.get("text", {})
                                .get("coordinates", {})
                                .get("Top", 0),
                            },
                            "page_no": each.get("text", {}).get("page", ""),
                        },
                    },
                ],
            }
            resp.append(formatted_data)
        return resp

    def _format_data_results(self, results):
        resp = []
        grouped_data = {}
        for result in results:
            # Ensure we have all required fields
            if not isinstance(result, dict):
                continue
            if "data" in result:
                result = result["data"]
            file_name = result.get("file_name", "")
            text_data = result.get("text", {})
            page_no = text_data.get("page", "")
            coordinates = text_data.get("coordinates", {})
            key = (file_name, page_no)
            # Check if the key already exists
            if key not in grouped_data:
                grouped_data[key] = {
                    "title": "",
                    "score": result.get("score", ""),
                    "conditions": []
                }
                resp.append(grouped_data[key])

            # Append the current condition
            grouped_data[key]["conditions"].append({
                "text": text_data.get("data", ""),
                "meta_data": {
                    "file_name": file_name,
                    "coordinates": coordinates,
                    "page_no": page_no,
                }
            })

        return resp

    def _format_data_results_ungrouped(self, results):
        resp = []
        for result in results:
            if not isinstance(result, dict):
                continue
            text_data = result.get("text", {})
            formatted_data = {
                "title": "",
                "score": result.get("score", ""),
                "conditions": [
                    {
                        "text": text_data.get("data", ""),
                        "meta_data": {
                            "file_name": result.get("file_name", ""),
                            "coordinates": text_data.get("coordinates", {}),
                            "page_no": text_data.get("page", ""),
                        },
                    },
                ],
            }
            resp.append(formatted_data)
        return resp

    # override the base function
    def _get_storage_class(self) -> Type[BaseGraphStorage]:
        return {
            # vector storage
            "MilvusVectorDBStorage": MilvusVectorDBStorage,
            # graph storage
            "Neo4JStorage": CustomNeo4JStorage,
            "JsonKVStorage": MongoKVStorage,
            "MongoKVStorage": MongoKVStorage,
        }

    @traced(name="lightrag_stream_query", attributes={"operation": "stream_query", "trace.type": "lightrag"})
    async def stream_query(self, query: str, param: QueryParam = QueryParam()):
        ANSWER_MESSAGE = ""
        ERROR_MESSAGE = "Sorry, I couldn’t find the information you're looking for. Could you please rephrase your question or provide more details?"
        NO_CONDITIONS_MESSAGE = "No conditions found!"
        mode = param.mode
        output_structure = {"answer": ANSWER_MESSAGE, "structured_content": [], "steps": []}
        try:
            tracer = FunctionTracer()
            trace_task = tracer.start_trace(
                function_name=self.super_aquery,
                kargs={"query": query, "param": param},
                check_functions=CHECK_FUNCTIONS[mode],
            )
            processed_functions = set()
            while not trace_task.done():
                for function in tracer.functions:
                    if (function.name, function.status) not in processed_functions:
                        processed_functions.add((function.name, function.status))
                        try:
                            message_section = process_states[mode]
                            if len(message_section[function.name]) == 2:
                                message = (
                                    message_section[function.name][0]["message"]
                                    if function.status == "pending"
                                    else message_section[function.name][1]["message"]
                                )
                            else:
                                message = "Processing"
                            output_structure["steps"].append(
                                Step(
                                    id=function.id,
                                    name=function.name,
                                    message=message,
                                    timestamp=function.timestamp,
                                ).model_dump()
                            )
                            yield output_structure
                        except Exception as e:
                            print(e)
                await asyncio.sleep(0.1)
            result: Function = next((f for f in reversed(tracer.functions) if f.status == "returned" and f.name in STOP_FUNCTIONS[mode]), None)
            if result is None:  # Dont remove this code even its unreached, which leads to AttributeError: 'NoneType' object has no attribute 'output'
                output_structure["answer"] = NO_CONDITIONS_MESSAGE
                yield output_structure
                return
            results = result.output.value
            if len(results) == 0:
                output_structure["answer"] = NO_CONDITIONS_MESSAGE
                yield output_structure
            else:
                if len(results) > 10:
                    results = results[:10]
                contents = ""  # To store the text to summarize finally
                structured_contents = self._format_data_results(results)
                temporary_structure = [condition["conditions"] for condition in structured_contents]
                temporary_structure = [condition for sublist in temporary_structure for condition in sublist]
                formatted_prompt = create_grouping_prompt(temporary_structure)
                response = await self.llm_model_func(
                    formatted_prompt,
                    system_prompt="You are a helpful assistant for data transformation and grouping.",
                )
                response = parse_json_content(response)
                structured_contents = []
                for conditions in response:
                    structured_contents.append({
                        "title": "",
                        "score": "",
                        "conditions": conditions
                    })

                """
                "structured_content":
                    [
                        "title": should generated from
                        "score":
                        "conditions":
                    ]

                    conditions:
                        [
                            "text": - available in result
                            "meta_data": - available in result
                        ]

                    "meta_data":
                        {
                            "file_name": available in result
                            "coordinates": available in result
                        }
                """
                for structure in structured_contents:
                    inputs = ""
                    for condition in structure["conditions"]:
                        inputs += f"Condition: {condition.get('text', '')}\n"
                    contents += inputs
                    _query = create_scoring_prompt(inputs, query)
                    response = await self.llm_model_func(
                        _query,
                        system_prompt="You are an expert at creating concise one-liners and relevancy scoring.",
                    )
                    response = json.loads(response)
                    response = response.get("data", {})
                    structure["title"] = response.get("oneline", "")
                    structure["score"] = int(response["score"])
                    if structure["score"] < 5:
                        continue
                    output_structure["structured_content"].append(structure)
                    yield output_structure
                system_prompt_summarizer = """
                You are an expert at creating summary.
                Task
                - Provide overall summary (max 5 lines) covering all input texts

                """
                output_structure["answer"] = ""
                response = await self.llm_model_func(
                    f"""{contents}""",
                    system_prompt=system_prompt_summarizer,
                    stream=True,
                )
                async for chunk in response:
                    output_structure["answer"] += chunk
                    yield output_structure
        except Exception:
            output_structure["answer"] = ERROR_MESSAGE
            yield output_structure

    @traced(name="lightrag_query", attributes={"operation": "query", "trace.type": "lightrag"})
    async def aquery(self, query: str, param: QueryParam = QueryParam()):
        yield {"answer": "processing"}
        results = await super().aquery(query, param)
        if len(results) > 10:
            results = results[:10]
        sys_prompt_temp = """
        You are an expert in creating concise one-liners and scoring the relevance of content.

        Task:
        - For each text in the provided list, create a concise one-liner (maximum 5 words).
        - Provide a brief overall summary (maximum 5 lines) that covers the main themes of all texts.
        - Assign a relevancy score (from 0 to 100) to each text, indicating how relevant it is to the following question.

        Format of your output:
        {
            "data": {
                "oneline": [
                    "<one-liner for text 1>",
                    "<one-liner for text 2>",
                    ...
                ],
                "summary": "<overall summary of all texts>",
                "score": [
                    <score for text 1>,
                    <score for text 2>,
                    ...
                ]
            }
        }

        Important:
        - The number of one-liners must match the number of input texts.
        - The number of relevancy scores must match the number of input texts.
        - Focus on providing relevance to the content based on its context, i.e., development, zoning, infrastructure, and other project-related aspects.
        - Provide clear and concise information.

        Ensure the structure in your output strictly adheres to the JSON format shown above.
        """
        sys_prompt = sys_prompt_temp
        context = [i.get("text", {}).get("data", "") for i in results]
        query = f"""
        Question: Create one-liners and scores for each text in the following list.
        Number of texts: {len(context)}

        Input texts:
        {context}
        """
        response = await self.llm_model_func(
            query,
            system_prompt=sys_prompt,
        )

        response = json.loads(response)
        response = response["data"]
        structured_content = self._format_data(results, response)

        yield {"answer": response["summary"], "structured_content": structured_content}

    # def insert(self, textract_data, file_name, additional_fields):
    #     print(f"DEBUG: Starting synchronous insert for file: {file_name}, with additional fields: {additional_fields}")
    #     loop = always_get_an_event_loop()
    #     return loop.run_until_complete(self.ainsert(textract_data, file_name, additional_fields))

    async def update_chunks_relevancy(self, chunk_updates: List[Dict], mongo_uri: str = settings.MONGO_URL) -> None:
        client = MongoClient(mongo_uri)
        db = client[settings.MONGO_DATABASE_LIGHTRAG]
        collection = db["text_chunks"]

        try:
            bulk_operations = []
            for update in chunk_updates:
                revelancy_count = update.get('revelancy_count', 0)
                chunks = update.get('chunks', [])

                for chunk_id in chunks:
                    bulk_operations.append(
                        UpdateOne(
                            {'_id': chunk_id},
                            {
                                '$set': {'revelancy_count': revelancy_count, 'last_accessed': datetime.utcnow()},
                            }
                        )
                    )

            if bulk_operations:
                result = collection.bulk_write(bulk_operations)
                print(f"Bulk Updated {result.modified_count} chunks")
        except Exception as e:
            print("ERROR", e)
        finally:
            client.close()
        return None

    def process_result_data(self, data):
        result = []

        for point_data in data:
            item_dict = {
                'revelancy_count': len(set([item[1] for item in point_data])) + 1,
                'chunks': [item[0] for item in point_data]
            }
            result.append(item_dict)

        return result

    def catagorize_coa(self, data, file_id=None):
        openai_embedding = OpenAIEmbeddings(
            api_key=settings.OPENAI_API_KEY
        )
        milvus_store = MilvusStore(
            collection_name="coa_points"
        )

        file_id = str(uuid.uuid4()) if file_id is None else file_id
        id_list = [plan['id'] for plan in data]
        header_list = [plan['header'] for plan in data]
        point_list = [plan['point'] for plan in data]
        embedded = openai_embedding.process_in_batches(point_list, batch_size=100)
        grouped = {}
        for id, header, embedding in zip(id_list, header_list, embedded):
            if header not in grouped:
                grouped[header] = {
                    'embeddings' : [],
                    'ids' : []
                }
            grouped[header]["ids"].append(id)
            grouped[header]["embeddings"].append(embedding)

        revelancies = []
        for section in grouped:
            embeddings = grouped[section]['embeddings']
            ids = grouped[section]['ids']
            chunk_count = len(ids)
            sections = [section for i in range(chunk_count)]
            file_ids = [file_id for i in range(chunk_count)]
            result = milvus_store.batch_get(embeddings,
                                            sections=sections, top_k=100, threshold=0.6)
            processed_result = self.process_result_data(result)
            for i in range(len(processed_result)):
                if ids[i] not in processed_result[i].get("chunks", []):
                    processed_result[i].get("chunks", []).append(ids[i])
                revelancies.append(processed_result[i])
            # Save the embeddings
            milvus_store.batch_upsert(
                embeddings=embeddings,
                ids=ids,
                sections=sections,
                fileids=file_ids
            )

        return revelancies

    async def ainsert(self, processed_textract_data, file_name, additional_fields, orginal_data=None):
        try:
            await self.full_docs.update_one(
                {"file_name": file_name},
                {
                    "status.process_textract_data.status": ProcessStatus.IN_PROGRESS,
                    "status.insertion.status": ProcessStatus.IN_PROGRESS,
                    "status.insertion.start_at": datetime.now(),
                    "status.process_textract_data.start_at": datetime.now()
                },
            )
            processed_textract_data = await self._process_textract_data(
                processed_textract_data,
                file_name,
            )
            processed_textract_data = [
                {**v, "content": format_md(v, file_name)}
                for v in processed_textract_data
            ]

            if isinstance(processed_textract_data, dict):
                processed_textract_data = [processed_textract_data]

            # Compute Document Hashes
            new_docs = compute_mdhash_id(file_name, prefix="doc-")
            if not len(new_docs):
                logger.warning("All docs are already in the storage")
                return
            logger.info(f"[New Docs] inserting {len(new_docs)} docs")
            await self.full_docs.update_one(
                {"file_name": file_name},
                {
                    "status.process_textract_data.status": ProcessStatus.COMPLETED,
                    "status.process_textract_data.end_at": datetime.now(),
                    "status.process_chunks.status": ProcessStatus.IN_PROGRESS,
                    "status.process_chunks.start_at": datetime.now(),
                },
            )

            if not new_docs:
                print("DEBUG: No new documents found to insert. Exiting.")
                return

            print(f"DEBUG: New documents detected, count: {len(processed_textract_data)}")

            # Prepare Chunks for Insertion
            inserting_chunks = {}
            prev_doc = None
            coa_points = []
            coa_point_data = {}

            for doc in processed_textract_data:
                chunk_id = compute_mdhash_id(doc.get("content", "") + doc.get("file_name", "") + str(doc.get("text", {}).get("page", "")), prefix="chunk-")
                is_coa = doc.get("is_coa", True)
                # print("Additionaladditional_fields)
                if is_coa:
                    # COA Operations
                    text = doc.get("text", {}).get("data", None)
                    page = doc.get('text', {}).get("page", None)
                    coordinates = doc.get('text', {}).get("coordinates", None)
                    header = doc.get("header", {}).get("name", None)
                    coa_points.append({
                        'id': chunk_id,
                        'header': header,
                        'point': text
                    })

                    if header not in coa_point_data:
                        coa_point_data[header] = []

                    coa_point_data[header].append({
                        'condition': text,
                        'meta_data': {
                            'chunk_id': chunk_id,
                            'page': page,
                            'coordinates': json.dumps(coordinates[0] if isinstance(coordinates, list) else coordinates),
                            'file_name': file_name
                        }
                    })

                chunk_data = {
                    **doc,
                    **additional_fields,
                    "revelancy_count": 0,
                    "full_doc_id": new_docs,
                    "content": doc["content"] + prev_doc["content"] if prev_doc else doc["content"],
                    # Add fields required by Milvus schema
                    "entity_name": doc.get("title", {}).get("data", ""),  # Using title as entity_name
                    "src_id": "",  # Empty for document chunks
                    "tgt_id": "",  # Empty for document chunks
                }
                inserting_chunks[chunk_id] = chunk_data
                prev_doc = doc
            try:
                finalized_structure = {
                    "file_name": file_name.split('/')[-1],
                    "file_path": file_name,
                    "mpud_category": "",
                    "project_type": "",
                    "proposed_density_intensity": "",
                    "location_access": "",
                    "unique_site_characteristics": "",
                    "design_elements": "",
                    "integration_surroundings": "",
                    "phasing_considerations": "",
                    "environmental_considerations": "",
                    'sections': {}
                }
                first_page = str(extract_first_page(orginal_data))
                conditions = {}
                for section_name in coa_point_data:
                    if section_name not in finalized_structure['sections']:
                        finalized_structure['sections'][section_name] = {}
                    finalized_structure['sections'][section_name]['conditions'] = coa_point_data[section_name]
                    conditions[section_name] = [condition['condition'] for condition in coa_point_data[section_name]]
                condition_data = convert_to_markdown(conditions, token_limit=10000)  # 8k Token Limit
                DOCUMENT_DATA = f"FILENAME: {file_name}\n{first_page}\n{condition_data}"
                mpud_data = analyze_mpud_document(DOCUMENT_DATA)
                for field in mpud_data:
                    finalized_structure[field] = mpud_data[field]
                meta_data = {
                    "file_name": file_name.split('/')[-1],
                    "file_path": file_name,
                    "proposed_density_intensity": finalized_structure["proposed_density_intensity"],
                    "location_access": finalized_structure["location_access"],
                    "unique_site_characteristics": finalized_structure["unique_site_characteristics"],
                    "design_elements": finalized_structure["design_elements"],
                    "integration_surroundings": finalized_structure["integration_surroundings"],
                    "phasing_considerations": finalized_structure["phasing_considerations"],
                    "environmental_considerations": finalized_structure["environmental_considerations"]
                }
                mpud = init_predictor(settings)
                mpud.store_mpud(new_docs, finalized_structure['mpud_category'], project_type=finalized_structure['project_type'], sections=finalized_structure['sections'], metadata=meta_data)
                print("Completed The Insertion to Neo4j")

            except Exception as e:
                print("errro", "".join(traceback.format_exc()))
                print(e)

            print(f"DEBUG: Total chunks prepared for insertion: {len(inserting_chunks)}")
            if not inserting_chunks:
                print("DEBUG: No chunks prepared for insertion. Exiting.")
                return

            await self.text_chunks.upsert(inserting_chunks)
            await self.question_generator.generate_and_store({k: {"data": v["text"]["data"], "header": v.get("header", {}).get("data", "")} for k, v in inserting_chunks.items()})

            logger.info(f"[New Chunks] inserting {len(inserting_chunks)} chunks")
            # if any(revelancies):
            #     await self.update_chunks_relevancy(
            #         revelancies
            #     )
            await self.full_docs.update_one(
                {"file_name": file_name},
                {
                    "status.process_chunks.status": ProcessStatus.COMPLETED,
                    "status.process_chunks.end_at": datetime.now(),
                    "status.entity_extraction.status": ProcessStatus.IN_PROGRESS,
                    "status.entity_extraction.start_at": datetime.now(),
                },
            )

            # Insert Chunks into Vector Database
            print("DEBUG: Inserting chunks into vector database...")
            insert_result = await self.chunks_vdb.upsert(inserting_chunks)
            if insert_result:
                print("Chunks inserted successfully")
            else:
                print("Failed to insert chunks")

            # Entity extraction and relationship building
            logger.info("[Entity Extraction]...")
            # TODO: Need to undo once demo completed
            # extractor = EntityExtrator(
            #     global_config=asdict(self),
            #     entity_vdb=self.entities_vdb,
            #     relationships_vdb=self.relationships_vdb,
            #     knowledge_graph_inst=self.chunk_entity_relation_graph,
            #     lightrag_batch=self.lightrag_batch,
            #     file_name=file_name,
            #     full_docs=self.full_docs,
            # )
            # import lightrag.operate as operate_module

            # operate_module.extract_entities = extractor.extract_entities
            # maybe_new_kg = await operate_module.extract_entities(
            #     inserting_chunks,
            # )
            # if maybe_new_kg is None:
            #     logger.warning("No new entities and relationships found")
            #     return
            # self.chunk_entity_relation_graph = maybe_new_kg
            await self.full_docs.update_one(
                {"file_name": file_name},
                {
                    "status.insertion.status": ProcessStatus.COMPLETED,
                    "status.insertion.end_at": datetime.now(),
                },
            )
        except Exception:
            traceback.print_exc()
            await self.full_docs.update_one(
                {"file_name": file_name},
                {
                    "status.insertion.status": ProcessStatus.ERROR,
                    "status.insertion.error": traceback.format_exc(),
                    "status.insertion.end_at": datetime.now(),
                },
            )
        finally:
            await self._insert_done()
            print(f"Insert operation completed for file: {file_name}")

    async def get_title_header_text(self, prev_title, page_data: list):
        """Transform the textract data to a chunk structure"""
        seen_titles = set()
        is_coa = False
        already_picked = set()
        total_len = len(page_data)
        i = 0
        text_index = 0
        title_index = 0
        header_index = 0
        data_chunk = []
        non_header = ""
        while i < total_len:
            # coa_data = False
            if 'is_coa_data' in page_data[i].keys():
                # coa_data = True
                pass

            if "LAYOUT_TITLE" in page_data[i].keys() or prev_title:
                if (
                    "LAYOUT_TITLE" in page_data[i].keys()
                    and page_data[i].get("LAYOUT_TITLE", "") + str(i) not in already_picked
                ):
                    prev_title = {
                        "name": page_data[i].get("LAYOUT_TITLE", "").replace("\n", " "),
                        "data": page_data[i].get("LAYOUT_TITLE", "").replace("\n", " "),
                        "coordinates": page_data[i]["Geometry"]["BoundingBox"],
                        "sequence": int(title_index),
                        "page": int(page_data[i].get("Page", "")),
                    }
                    if "CONDITIONS OF APPROVAL".casefold() in prev_title["data"].casefold():
                        is_coa = True

                    text_index = 0
                    title_index += 1
                    header_index = 0
                    already_picked.add(page_data[i].get("LAYOUT_TITLE", "") + str(i))
                i += 1
                header = None
                while i < total_len and (
                    "LAYOUT_TITLE" not in page_data[i].keys()
                    or page_data[i].get("LAYOUT_TITLE", "") + str(i) in already_picked
                ):
                    data_to_build = {"title": {}, "file_name": self.file_name}
                    if "LAYOUT_SECTION_HEADER" in page_data[i]:
                        if re.match(pattern, page_data[i]["LAYOUT_SECTION_HEADER"]) or "." in page_data[i]["LAYOUT_SECTION_HEADER"]:
                            non_header = page_data[i]["LAYOUT_SECTION_HEADER"] + "\n"
                        else:
                            header = {
                                "name": page_data[i]
                                .get("LAYOUT_SECTION_HEADER", "")
                                .replace("\n", " "),
                                "data": page_data[i]
                                .get("LAYOUT_SECTION_HEADER", "").lower().strip(),
                                "coordinates": page_data[i]["Geometry"]["BoundingBox"],
                                "sequence": int(header_index),
                                "page": int(page_data[i].get("Page", "")),
                            }
                            header_index += 1
                            text_index = 0
                    elif "LAYOUT_TEXT" in page_data[i]:
                        text = {
                            "name": f'{uuid.uuid4()}-text-{text_index}-{page_data[i].get("Page", "")}',
                            "data": non_header + page_data[i].get("LAYOUT_TEXT", ""),
                            "sequence": int(text_index),
                            "coordinates": page_data[i]["Geometry"]["BoundingBox"],
                            "page": int(page_data[i].get("Page", "")),
                        }
                        non_header = ""
                        is_new_condition = False
                        if re.match(pattern, text["data"]) and is_coa:
                            is_new_condition = True
                        last_node = (
                            data_chunk
                            and data_chunk[-1]
                            and data_chunk[-1].get("text", None)
                        )
                        if "CONDITIONS OF APPROVAL".casefold() in prev_title.get("data", "").casefold() and prev_title.get("data", "") + str(prev_title.get("page", "-")) not in seen_titles and is_new_condition:
                            is_new_coa = True
                            seen_titles.add(prev_title.get("data", "") + str(prev_title.get("page", "-")))
                        else:
                            is_new_coa = False
                        if (
                            header
                            or not last_node
                            or last_node["page"] == text["page"]
                            or await self.is_related(last_node, text)
                        ):
                            if header:
                                data_to_build.update(
                                    {
                                        "title": prev_title,
                                        "header": header,
                                        "text": text,
                                        "is_coa": is_coa,
                                        "is_new_condition": is_new_condition,
                                        "is_new_coa": is_new_coa
                                    }
                                )
                            else:
                                data_to_build.update(
                                    {"title": prev_title, "header": {}, "text": text, "is_coa": is_coa, "is_new_condition": is_new_condition, "is_new_coa": is_new_coa}
                                )
                            text_index += 1
                        else:
                            text_index = 0
                            header_index = 0
                            data_to_build.update(
                                {"title": {}, "header": header or {}, "text": text, "is_coa": is_coa, "is_new_condition": is_new_condition, "is_new_coa": is_new_coa}
                            )
                            prev_title = {}

                        data_chunk.append(data_to_build)
                    i += 1
            else:
                i += 1

        return data_chunk

    async def is_related(self, prev_node, cur_node):
        """Check the both chunks are related"""

        prev_coordinates = prev_node["coordinates"]

        cur_coordinates = cur_node["coordinates"]
        if isinstance(prev_coordinates, str):
            prev_coordinates = prev_coordinates.replace("'", '"')
            prev_coordinates = json.loads(prev_coordinates)
        prev_node_right = prev_coordinates["Left"] + prev_coordinates["Width"]
        prev_node_bottom = prev_coordinates["Top"] + prev_coordinates["Height"]
        cur_node_left = cur_coordinates["Left"]
        cur_node_top = cur_coordinates["Top"]
        return (
            cur_node_left < 0.25
            and cur_node_top < 0.15
            and prev_node_bottom > 0.8
            and prev_node_right > 0.8
        )

    async def group_points(self, formatted_data):
        points_dict = defaultdict(list)
        prev_header = ""
        start_fresh = True
        for layout in formatted_data:
            header = layout.get("header", {}).get("data", prev_header)
            if layout["is_new_condition"] and start_fresh:
                points_dict[header].append({**layout, "text": {"data": re.sub(pattern, "", layout["text"]["data"], flags=re.MULTILINE), "page": layout.get("text", {}).get("page", {}), "coordinates": [layout.get("text", {}).get("coordinates", {})]}})
                prev_header = header
            elif not start_fresh and layout.get("is_new_coa"):
                start_fresh = True
                points_dict[header].append({**layout, "text": {"data": re.sub(pattern, "", layout["text"]["data"], flags=re.MULTILINE), "page": layout.get("text", {}).get("page", {}), "coordinates": [layout.get("text", {}).get("coordinates", {})]}})
                prev_header = header
            elif prev_header == "Procedures" and not layout.get("is_new_coa"):
                start_fresh = False
                continue
            elif layout["is_coa"] and prev_header != "Procedures":
                try:
                    points_dict[header][-1]["text"]["data"] += layout.get("text", {}).get("data", "")
                    points_dict[header][-1]["text"]["coordinates"].append(layout.get("text", {}).get("coordinates", {}))
                except Exception as e:
                    print(e)
                    pass
        result_list = []
        for section in points_dict.values():
            result_list += section

        return result_list

    async def _process_textract_data(self, layout_data: list, file_name: str):
        self.file_name = file_name
        print(f"DEBUG: Received layout data for file {file_name}: {layout_data}")
        data_chunk = await self.get_title_header_text(None, layout_data)
        data_chunk = await self.group_points(data_chunk)
        return data_chunk
