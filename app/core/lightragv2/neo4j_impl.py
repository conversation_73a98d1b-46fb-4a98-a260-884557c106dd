from lightrag.kg.neo4j_impl import Neo4JStorage
from typing import List, Union, Dict, Any, Tuple


class CustomNeo4JStorage(Neo4JStorage):
    async def get_nodes_by_labels(self, nodes_id: List[str]) -> List[Union[dict, None]]:
        async with self._driver.session() as session:
            # Clean up labels and format them for the query
            cleaned_labels = [node_id.strip('"') for node_id in nodes_id]

            # Build the Cypher query to fetch nodes by their labels
            query = """
                UNWIND $labels AS label
                MATCH (n:`$label`)
                RETURN n
            """

            result = await session.run(query, labels=cleaned_labels)

            nodes = []
            async for record in result:
                node = record["n"]
                node_dict = dict(node)
                nodes.append(node_dict)
            return nodes

    async def upsert_nodes(self, nodes: List[Dict[str, Any]]):
        """
        Bulk upsert nodes in the Neo4j database without using APOC.

        Args:
            nodes: List of dictionaries where each dictionary contains:
                - node_id: The unique identifier for the node (used as label)
                - node_data: Dictionary of node properties
        """

        async def _do_bulk_upsert(tx, label, nodes_with_label):
            query = f"""
            UNWIND $nodes AS node
            MERGE (n:`{label}`)
            SET n += node.properties
            """
            await tx.run(query, nodes=nodes_with_label)

        # Group nodes by their labels
        grouped_nodes = {}
        for node in nodes:
            label = node["node_id"].strip('"')
            if label not in grouped_nodes:
                grouped_nodes[label] = []
            grouped_nodes[label].append({"properties": node["node_data"]})

        try:
            async with self._driver.session() as session:
                # Execute a separate query for each label
                for label, nodes_with_label in grouped_nodes.items():
                    await session.execute_write(
                        _do_bulk_upsert, label, nodes_with_label
                    )
        except Exception as e:
            raise e

    async def upsert_edges(self, edges: List[Dict[str, Any]]):
        """
        Bulk upsert edges and their properties between nodes with minimal Neo4j calls.

        Args:
            edges (list): A list of dictionaries where each dictionary contains:
                - source_node_id (str): Label of the source node.
                - target_node_id (str): Label of the target node.
                - edge_data (dict): Dictionary of properties to set on the edge.
        """
        # Prepare the data for the query
        formatted_edges = [
            {
                "source_label": edge.pop("src_id").strip('"'),
                "target_label": edge.pop("tgt_id").strip('"'),
                "properties": edge,
            }
            for edge in edges
        ]

        # Construct a single query that processes all edges
        query = """
        UNWIND $edges AS edge
        MATCH (source) WHERE labels(source) = [edge.source_label]
        MATCH (target) WHERE labels(target) = [edge.target_label]
        MERGE (source)-[r:DIRECTED]->(target)
        SET r += edge.properties
        """

        try:
            async with self._driver.session() as session:
                await session.execute_write(
                    lambda tx: tx.run(query, edges=formatted_edges)
                )
        except Exception as e:
            print(f"Error during bulk edge upsert: {str(e)}")
            raise

    async def has_edges(self, node_pairs: List[Tuple[str, str]]) -> List[bool]:
        """
        Check if edges exist between multiple pairs of nodes in a single query.

        Args:
            node_pairs: A list of tuples where each tuple contains:
                - source_node_id: The ID of the source node.
                - target_node_id: The ID of the target node.

        Returns:
            A list of booleans indicating whether an edge exists for each pair.
        """
        async with self._driver.session() as session:
            # Prepare the data for the query
            formatted_pairs = [
                {
                    "source": pair[0].strip('"'),
                    "target": pair[1].strip('"')
                }
                for pair in node_pairs
            ]

            query = """
            UNWIND $pairs AS pair
            MATCH (a:`${pair.source}`)-[r]-(b:`${pair.target}`)
            RETURN pair.source AS source, pair.target AS target, COUNT(r) > 0 AS edgeExists, properties(r) as edge_properties
            """

            # Execute the query
            result = await session.run(query, pairs=formatted_pairs)

            # Collect results
            edge_results = []
            async for record in result:
                edge_results.append(record["edgeExists"])

            return edge_results

        def close(self):
            self._driver.close()
