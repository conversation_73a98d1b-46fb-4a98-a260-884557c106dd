import os

from openai import (
    AsyncOpenAI,
    APIConnectionError,
    RateLimitError,
    Timeout,
)


from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)
from lightrag.base import BaseKVStorage
from lightrag.utils import compute_args_hash

os.environ["TOKENIZERS_PARALLELISM"] = "false"


async def gpt_4o_mini_complete(
    prompt, system_prompt=None, history_messages=[], **kwargs,
) -> str:
    return await openai_complete_if_cache(
        "gpt-4o-mini",
        prompt,
        system_prompt=system_prompt,
        history_messages=history_messages,
        **kwargs,
    )


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    retry=retry_if_exception_type((RateLimitError, APIConnectionError, Timeout)),
)
async def openai_complete_if_cache(
    model,
    prompts,
    system_prompt=None,
    history_messages=[],
    base_url=None,
    api_key=None,
    **kwargs,
) -> str:
    if api_key:
        os.environ["OPENAI_API_KEY"] = api_key

    openai_async_client = (
        AsyncOpenAI() if base_url is None else AsyncOpenAI(base_url=base_url)
    )
    hashing_kv: BaseKVStorage = kwargs.pop("hashing_kv", None)
    cached_results = []
    uncached_prompts = []
    uncached_indices = []
    messages_list = []

    # Prepare messages and check the cache
    for i, prompt in enumerate(prompts):
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.extend(history_messages)
        messages.append({"role": "user", "content": prompt})
        messages_list.append(messages)

        if hashing_kv:
            args_hash = compute_args_hash(model, messages)
            if_cache_return = await hashing_kv.get_by_id(args_hash)
            if if_cache_return:
                cached_results.append(if_cache_return["return"])
            else:
                uncached_prompts.append((args_hash, messages))
                uncached_indices.append(i)
        else:
            uncached_prompts.append((None, messages))
            uncached_indices.append(i)

    if uncached_prompts:
        _, uncached_messages = zip(*uncached_prompts)
        response = await openai_async_client.chat.completions.create(
            model=model,
            messages=[{"role": "system", "content": system_prompt}] + [
                {"role": "user", "content": msg[-1]["content"]}
                for msg in uncached_messages
            ],
            **kwargs,
        )

        # Store results in cache and the final result list
        for idx, args_hash in zip(uncached_indices, [u[0] for u in uncached_prompts]):
            completion = response.choices[idx].message.content
            cached_results.insert(idx, completion)
            if args_hash and hashing_kv:
                await hashing_kv.upsert(
                    {args_hash: {"return": completion, "model": model}}
                )

    return cached_results
