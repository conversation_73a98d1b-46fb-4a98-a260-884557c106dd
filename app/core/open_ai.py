import json
from langchain_openai import ChatOpenAI
from app.utils.base_utils import (
    milvus_langchain_search,
    get_latest_n_messages_from_chat,
    create_new_message,
)
from langchain_core.prompts import PromptTemplate
from pathlib import Path
from langchain.schema import HumanMessage, SystemMessage
from pydantic import BaseModel, Field
from typing import List, Dict, Union
from langchain.output_parsers import PydanticOutputParser


class AnswerOutput(BaseModel):
    answer: str = Field(description="The generated answer with a focus on bureaucratic structure.")
    structured_content: List[Dict[str, Union[str, List[Dict[str, str]]]]] = Field(
        description="A list of dictionaries where each dictionary contains a 'title', 'content', and 'metadata'. The 'content' should have multiple points with HTML tags like <strong> (only use strong if needed in cases where we have bullet points or numbered lists) enclosed in <p> tags for easy PDF creation. The 'metadata' is a list of dictionaries, each containing 'file_name' and 'page_no' of the referred content. Make it empty if it's not related to conditions of approval.",
    )
    meta_data: List[Dict[str, str]] = Field(
        description="A list of dictionaries where each dictionary contains a 'file_name' and 'page_no' as strings used to generate the answer or structured_content. If none from the context was used, make it empty.",
    )


output_parser = PydanticOutputParser(pydantic_object=AnswerOutput)


class OpenAI(object):
    def __init__(self, api_key):
        self.api_key: str = api_key

    def get_chat_response(
        self,
        model_name: str,
        query: str,
        chat_id: str,
        user_id: str,
    ):
        if model_name not in ["gpt-4o-mini", "gpt-4o"]:
            msg = "Please use gpt-4o-mini or gpt-4o model"
            yield f"data: {json.dumps({'content': msg})}\n\n"
            return

        llm = ChatOpenAI(
            model=model_name,
            temperature=0,
            max_tokens=None,
            timeout=None,
            max_retries=2,
            api_key=self.api_key,
            streaming=True,
        )

        # Retrieve documents based on the query
        documents = self.__search(query)
        # print("documents", documents)
        history, chat_id, chat_title = get_latest_n_messages_from_chat(chat_id, user_id)
        formatted_base_prompt = self.__fetch_formatted_prompt(documents, history, query)
        # print("formatted base prompt", formatted_base_prompt)

        messages = [
            SystemMessage(content=formatted_base_prompt),
            HumanMessage(content=query),
        ]

        response = ""
        for chunk in llm.stream(messages):
            response += chunk.content
            yield f"data: {json.dumps({'content': response, 'chat_id': chat_id, 'chat_title': chat_title})}\n\n"

        # Ensure the response is correctly formatted
        response = response.replace('"""', '"')
        try:
            result = output_parser.parse(response)
        except Exception:
            # Handle parsing errors
            # print("Parsing error:", e)
            yield f"data: {json.dumps({'content': 'Error parsing the response.', 'chat_id': chat_id, 'chat_title': chat_title})}\n\n"
            return

        create_new_message(chat_id, result.answer, query, result.structured_content, result.meta_data)

    def extract_metadata_params(
        self, model_name: str, file_name: str, pages: list, categories: list,
    ) -> dict:
        # TODO: submit all the pages and file name to OpenAI and get: summary, tags, category, and zoning
        llm = ChatOpenAI(
            model=model_name,
            temperature=0,
            max_tokens=None,
            timeout=None,
            max_retries=2,
            api_key=self.api_key,
            streaming=True,
        )

        formatted_base_prompt = self.__fetch_formatted_metadata_prompt(categories=categories)

        query = f"File name: {file_name}, pages: {pages}"
        messages = [
            SystemMessage(content=formatted_base_prompt),
            HumanMessage(content=query),
        ]
        # print("PROMPT: ", formatted_base_prompt)
        response = llm.invoke(messages)

        # print("RESPONSE: ", response.content)
        return self.__parse_metadata_response(response.content)

    def __search(self, query: str):
        resp = milvus_langchain_search(query)
        return resp

    def __fetch_formatted_metadata_prompt(self, categories):
        path = Path(__file__).parent / "../prompts/metadata_generation.prompt"
        base_prompt = open(path, mode="r").read()
        prompt_template = PromptTemplate.from_template(base_prompt)

        return prompt_template.format(categories=categories)

    def __fetch_formatted_prompt(self, documents, history, query):
        path = Path(__file__).parent / "../prompts/base_prompt.prompt"
        base_prompt = open(path, mode="r").read()
        prompt_template = PromptTemplate.from_template(base_prompt)

        return prompt_template.format(
            context=str(documents),
            history=str(history),
            format_instructions=output_parser.get_format_instructions(),
            question=query,
            onclick='''[<a href="/view-source?file_name=ACTUAL_FILENAME&page_no=ACTUAL_PAGE_NUMBER" >view source</a>]''',
        )

    def __parse_metadata_response(self, response: str):
        # Clean up and parse the JSON response
        clean_response = response.strip('```json').strip('```')
        return json.loads(clean_response)
