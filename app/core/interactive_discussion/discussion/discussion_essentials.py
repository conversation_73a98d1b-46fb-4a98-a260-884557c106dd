from dataclasses import dataclass
from jinja2 import Environment, FileSystemLoader, Template


@dataclass
class DiscussionEssentials:
    model: any
    prev_context: dict
    initial_mpud_description: dict
    prompt_file: str
    # chat_type: str = "Create New"

    def __post_init__(self):
        env = Environment(loader=FileSystemLoader('app/core/interactive_discussion/prompts'))
        self.prompt: Template = env.get_template(self.prompt_file)
        self.discussion_configurations = self.model.generate_json_config()
        self.data_model = self.model.generate_json_schema()
        self.function_schema = self.model.generate_json_config()
        self.system_prompt = self.model.get_system_prompt()
        self.additional_prompt_message = self.model.get_additional_prompt_message()
        self.validations = self.model.get_validations()
        self.instructions = self.model.get_instructions()
