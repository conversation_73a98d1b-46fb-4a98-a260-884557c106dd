from dataclasses import dataclass, field
from ..tools.concrete_tool import DynamicToolFactory, ToolRegistry
from ..tools.function_schema_generator import FunctionSchemaGenerator
from ..tools.utils.tools_available import ToolsAvailable
from .discussion_essentials import DiscussionEssentials


@dataclass
class DiscussionContextSetter:
    available_tools: list[ToolsAvailable]
    discussion_essentials: DiscussionEssentials
    function_schema_type: str
    node_type: str
    discussion_type: str
    invocation_type: any = field(default="interactive_config")

    def __post_init__(self):
        self.registered_tool = ToolRegistry()
        self.registered_tool.register_tool_list(self.available_tools)
        self.exec_agent = DynamicToolFactory(self.registered_tool).create_dynamic_tool(
            [tool.label for tool in self.available_tools]
        )

        self.current_configuration = (
            self.discussion_essentials.discussion_configurations.get(self.node_type, {})
        )

        self.function_schema = FunctionSchemaGenerator.get_function_schema(
            self.function_schema_type,
            self.discussion_essentials.discussion_configurations,
            self.discussion_essentials.data_model,
        )

    def prompt_for_starting_discussion(self, retrieved_info) -> tuple:
        # with open('app/agents/agents.json', 'r') as file: #TODO2
        #     ai_agents = json.load(file)
        ai_agents = None

        # template = env.get_template(self.template_name)
        template = self.discussion_essentials.prompt
        config = self.current_configuration
        config_state = {}

        function_schema = FunctionSchemaGenerator.get_function_schema(
            self.function_schema_type,
            self.discussion_essentials.discussion_configurations,
            self.discussion_essentials.data_model,
        )

        if (
            'root_node' in retrieved_info
            and 'properties' in retrieved_info['root_node']
            and 'all_section_question' in retrieved_info['root_node']['properties']
        ):
            retrieved_info['root_node']['properties'].pop('all_section_question', '')
            retrieved_info['root_node']['properties'].pop('models_available', '')
            retrieved_info['root_node']['properties'].pop('models_display', '')

        context = {
            "prompt_type": "user",
            "current_node": retrieved_info.get("current_node")["labels"],
            "details_for_discussion": retrieved_info,
            "root_node_type": self.node_type,
            "config": config,
            "ai_agents": ai_agents,
            "config_state": config_state,
            "invocation_type": self.invocation_type,
            "function_schema": function_schema,
            "discussion_type": self.discussion_type,
            "node_type": self.node_type,
            "system_prompt": self.discussion_essentials.system_prompt,
            "additional_prompt_message": self.discussion_essentials.additional_prompt_message,
            "validation_rules": self.discussion_essentials.validations,
            "instructions": self.discussion_essentials.instructions,
            "prev_context": self.discussion_essentials.prev_context,
            "initial_mpud_description": self.discussion_essentials.initial_mpud_description
        }
        print('initial_mpud_description', self.discussion_essentials.initial_mpud_description)
        user_prompt = template.render(context)
        system_prompt = template.render(dict(context, prompt_type="system"))
        return user_prompt, system_prompt, function_schema
