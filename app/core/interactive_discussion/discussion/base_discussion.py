from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from .discussion_context_setter import DiscussionContextSetter


@dataclass
class BaseDiscussion(ABC):
    db: any
    vector_db: any
    node_id: str
    discussion_id: str
    title: str
    description: str
    api_key: str
    discussion_type: str
    discussion_context: DiscussionContextSetter
    model_name: str = field(default="gpt-4o-mini")
    retrieved_info: dict = field(
        default=None
    )
    discussion_so_far: list = field(default_factory=list)
    modifications: dict = field(default_factory=dict)
    file_attachments: list = field(default_factory=list)
    has_image_urls: bool = field(default=False)
    function_schema_type: any = field(default="MPUD")

    @abstractmethod
    def initiate_discussion_with_llm(self):
        raise NotImplementedError("Subclasses must implement this method")

    def get_modifications_from_llm_output(self):
        pass
