from dataclasses import dataclass, field

from app.core.telemetry.chat_logger import get_chat_logger
from ..llm.llm_interface import LLMInterface
from .base_discussion import BaseDiscussion
from datetime import datetime
import json
from app.core.config import settings
from ..database.graph_db import GraphDB
from pymongo import MongoClient
from typing import List, Dict, Any
from neo4j import GraphDatabase
import traceback

logger = get_chat_logger("pasco.ai.chat")


def get_chunk_details(chunk_id_list: List[str], uri: str = settings.NEO4J_URI, username: str = settings.NEO4J_USERNAME, password: str = settings.NEO4J_PASSWORD) -> List[Dict[str, Any]]:
    """
    Retrieves chunk details from Neo4j database and formats them according to specified structure.

    Args:
        chunk_id_list (List[str]): List of chunk IDs to query
        uri (str): Neo4j database URI
        username (str): Database username
        password (str): Database password

    Returns:
        List[Dict[str, Any]]: List of dictionaries containing formatted chunk details
    """
    # Create Neo4j driver
    driver = GraphDatabase.driver(uri, auth=(username, password))

    try:
        with driver.session() as session:
            # Construct query with parameterized chunk_id list
            query = """
            MATCH (n:Condition)
            WHERE n.chunk_id IN $chunk_ids
            RETURN DISTINCT n.chunk_id as _id, n.file_name as file_name, n.page as page,
                   n.coordinates as coordinates, n.content as content
            """

            # Execute query
            result = session.run(query, chunk_ids=chunk_id_list)

            # Process results into required format
            formatted_results = []
            for record in result:
                chunk_detail = {
                    "_id": record["_id"],
                    "file_name": record["file_name"],
                    "text": {
                        "coordinates": record["coordinates"],
                        "data": record["content"],
                        "page": record["page"]
                    },
                    "page": record["page"]
                }
                formatted_results.append(chunk_detail)

            return formatted_results

    finally:
        driver.close()


mongo_client = MongoClient(settings.MONGO_URL)
mongo_db = mongo_client['lightrag']
text_chunks = mongo_db['text_chunks']


def add_source(points):
    try:
        chunk_id_list = []
        map = {}
        for pts in points:
            if 'chunk_id' in pts and pts['chunk_id'] not in chunk_id_list :
                chunk_id_list.append(pts['chunk_id'])

            # mongo_resp = text_chunks.find({"_id": {"$in": chunk_id_list}}, {'_id': 1, 'file_name': 1, 'text.coordinates': 1, 'text.data': 1, 'text.page': 1})
            mongo_resp = get_chunk_details(chunk_id_list)
            print(mongo_resp)
            for rec in mongo_resp:
                coordinates = rec['text']['coordinates'][0] if isinstance(rec['text']['coordinates'], list) else rec['text']['coordinates']
                map[rec['_id']] = {"meta_data": [{'file_name': rec['file_name'], 'coordinates': coordinates, 'page_no': rec['text']['page']}]}

            for pts in points:
                if 'chunk_id' in pts and pts['chunk_id'] in map :
                    pts['meta_data'] = map[pts['chunk_id']].get('meta_data', [])

        return points
    except Exception as e:
        print('error :', e)
        traceback.print_exception()
        return points


@dataclass
class Discussion(BaseDiscussion):
    db: GraphDB
    current_user: str = field(default="default")
    step: int = field(default=1)
    # chat_type: str = field(default="Create New")  # ✅ Add this

    def __post_init__(self):
        self.llm: LLMInterface = LLMInterface(
            instance_name="discussion",
            user_id=self.current_user,
            project_id=self.node_id,
            agent_name=self.discussion_context.discussion_type,
            token_limit=None,
            api_key=self.api_key,
        )

    def validate_response(self, stream, data_str):
        # Define the restricted keys (all keys except modified_node)
        restricted_keys = {
            "new_child_nodes",
            "modified_children",
            "modified_siblings",
            "modified_similar_nodes",
            "new_relationships",
            "modified_relationships",
            "new_relationship_types",
            "created_at",
        }

        try:
            # Extract the modified_node content
            modified_node = data_str["content"]["modifications"][0]["modified_node"]

            # Get all keys from modified_node (including nested ones)
            def get_all_keys(obj):
                keys = set()
                if isinstance(obj, dict):
                    for key, value in obj.items():
                        keys.add(key)
                        # Recursively get keys from nested dictionaries
                        keys.update(get_all_keys(value))
                elif isinstance(obj, list):
                    # Handle lists by checking their elements
                    for item in obj:
                        keys.update(get_all_keys(item))
                return keys

            all_modified_node_keys = get_all_keys(modified_node)

            # Find any restricted keys that appear in modified_node
            invalid_keys = restricted_keys.intersection(all_modified_node_keys)
            print("Checking validation conditions")

            if invalid_keys:
                print(f"Found restricted keys in modified_node: {invalid_keys}")
                return False, f"Found restricted keys in modified_node: {invalid_keys}"
            else:
                print("No restricted keys found in modified_node")
                return True, "No restricted keys found in modified_node"

        except Exception as e:
            return False, f"Error processing data: {str(e)}"

    async def retrieve_info(self):
        self.retrieved_info = {}

        # Step 1: Retrieve basic node information
        root_node, current_node, nodes_in_path = await self.db.get_nodes_info(
            node_id=self.node_id, root_node_type=self.root_node_type
        )
        current_node = {
            "labels": ["MPUD"],
            "properties": {
                "Type": self.node_type,
                "created_at": "2024-12-13T13:06:10.109796",
                "created_by": "a4f804a8-50f1-707c-a3fb-c42d676e48ea",
                "is_active": True,
            },
        }
        self.retrieved_info.update(
            {
                "root_node": root_node,
                "current_node": current_node,
                "nodes_in_path": nodes_in_path,
            }
        )

        # # Step 2: Retrieve child and sibling nodes
        # self.retrieved_info["child_nodes"] = await self.get_child_nodes()
        # self.retrieved_info["sibling_nodes"] = await self.db.get_sibling_nodes(
        #     self.node_id, self.node_type
        # )

        # # Step 3: Retrieve other relevant nodes using vector search
        # self.retrieved_info["other_relevant_nodes"] = await self.get_relevant_nodes(
        #     root_node["id"], current_node
        # )

        # # Step 4: Retrieve top levels of the tree
        # if nodes_in_path:
        #     self.retrieved_info["top_levels"] = await self.db.get_descendant_nodes(
        #         root_node["id"], [self.node_type], ["Title"], 2
        #     )
        # else:
        #     self.retrieved_info["top_levels"] = None

        return self.retrieved_info

    async def create_discussion_node(self, node_id, discussion_type):
        # Placeholder for node creation logic

        properties = {
            "discussion_type": f"{discussion_type}",
            "created_at": datetime.now().isoformat(),
            "created_by": self.current_user,
            "step": self.step,
        }

        discussion_node = await self.db.create_node(["Discussion"], properties, node_id)
        return discussion_node

    async def async_initialize(self):
        if self.discussion_id is None:
            if self.node_id is None:
                raise ValueError("Parent ID is required to create a new discussion.")
            self.discussion_node = await self.create_discussion_node(
                self.node_id,
                self.discussion_context.discussion_type,
            )
            self.discussion_id = self.discussion_node["id"]
            # Set the chat_id in logger context after getting discussion_id
            logger.set_chat_id(str(self.discussion_id))
            self.current_step_index = 0
        else:
            self.discussion_node = await self.db.get_one_by_id(self.discussion_id)
            self.discussion_context.discussion_type = self.discussion_node[
                "properties"
            ].get("discussion_type", self.discussion_context.discussion_type)
            self.discussion_so_far = json.loads(
                self.discussion_node["properties"].get("discussion_so_far", "[]")
            )
            if not self.discussion_node:
                raise ValueError("Discussion node not found.")
            self.current_step_index = self.discussion_node["properties"].get(
                "current_step_index", 0
            )

        # Further initialization steps
        self.modifications_history = self.discussion_node["properties"].get(
            "modifications_history", []
        )
        if self.modifications_history:
            self.modifications_history = json.loads(self.modifications_history)
        self.modifications = self.discussion_node["properties"].get("modifications", {})
        if self.modifications:
            self.modifications = json.loads(self.modifications)
        # self.steps = self.define_steps()
        self.node = await self.db.get_parent_node(self.discussion_id)
        self.node_id = self.node["id"]
        self.node_type = "MPUD"
        self.root_node_type = "MPUD"
        self.root_node = await self.db.get_root_node(self.node_id)
        await self.retrieve_info()

    async def initiate_discussion_with_llm(self, stream=False, user_comment=None):
        if not self.discussion_so_far:
            user_prompt, system_prompt, self.function_schema = (
                self.discussion_context.prompt_for_starting_discussion(
                    retrieved_info=self.retrieved_info
                )
            )
            messages = []
            user_message = {
                "role": "user",
                "user_id": self.current_user,
                "created_at": datetime.now().isoformat(),
                "content": user_prompt,
                "discussion_type": self.discussion_type,
            }
            # messages.append(user_message)
            messages.append(
                {
                    "role": "system",
                    "created_at": datetime.now().isoformat(),
                    "content": system_prompt,
                    "discussion_type": self.discussion_type,
                }
            )
            messages.append(user_message)
        else:
            messages = self.discussion_so_far
            user_prompt = user_comment
            system_prompt = None
            self.function_schema = self.discussion_context.function_schema
            user_message = {
                "role": "user",
                "user_id": self.current_user,
                "created_at": datetime.now().isoformat(),
                "content": user_prompt,
                "discussion_type": self.discussion_type,
            }
            messages.append(user_message)

        if self.function_schema:  # todo
            self.tool = self.discussion_context.exec_agent(
                "/test", logger=None, discussion=self
            )
        llm_response = {
            "role": "assistant",
            "content": "",
            "discussion_type": self.discussion_type,
            "is_stream_complete": False,
        }
        try:
            response = await self.llm.llm_interaction_wrapper(
                messages=messages,
                user_prompt=None,
                system_prompt=system_prompt,
                response_format={"type": "text"},
                model=self.model_name,
                stream=stream,
                function_schemas=self.tool.function_schemas
                if self.function_schema
                else None,
                function_executor=self.tool.function_executor
                if self.function_schema
                else None,
                validation_function=self.validate_response,
            )
            if stream:

                async for chunk in response:
                    if isinstance(chunk, dict) :
                        # Function call result: yield individual messages
                        if 'content' in chunk and 'modifications' in chunk['content'] and chunk['content']['modifications']:
                            if 'modified_node' in chunk['content']['modifications'][0] and 'Points' in chunk['content']['modifications'][0]['modified_node'] :
                                points = add_source(chunk["content"]['modifications'][0]['modified_node']['Points'])
                                chunk["content"]['modifications'][0]['modified_node']['Points'] = points

                        yield {
                            "message": chunk["content"],
                            "chat_id": self.discussion_id,
                        }
                        # llm_response["content"] = (
                        #     "If you are okay with the result, please do merge it."
                        # )
                        llm_response["content"] = " "
                        llm_response["chat_finished"] = True
                        llm_response["is_stream_complete"] = False

                        if 'content' in chunk and chunk['content']['modifications']:
                            llm_response["modified_node"] = chunk['content']['modifications']

                    else:
                        # Streaming text: accumulate and yield
                        llm_response["is_stream_complete"] = True
                        llm_response["content"] += chunk
                        yield {
                            "message": llm_response,
                            "chat_id": self.discussion_id,
                        }
                # Need to format the last message
                last_message = messages.pop()
                messages.append(last_message)
                messages.append(
                    {
                        "role": "assistant",
                        "created_at": datetime.now().isoformat(),
                        "content": llm_response["content"],
                        "discussion_type": self.discussion_type,
                    }
                )
                self.discussion_so_far = messages
                formatted_output_text = f"LLM response:\n {llm_response['content']}"
                await self.update_discussion_node(
                    self.discussion_id,
                    formatted_output_text=formatted_output_text,
                    updated_discussion=self.discussion_so_far,
                )
                yield {"message": llm_response, "chat_id": self.discussion_id}
            else:
                llm_response["content"] = response
                messages.append(
                    {
                        "role": "assistant",
                        "created_at": datetime.now().isoformat(),
                        "content": response,
                        "discussion_type": self.discussion_type,
                    }
                )
                self.discussion_so_far = messages
                formatted_output_text = f"LLM response:\n {response}"
                await self.update_discussion_node(
                    self.discussion_id,
                    formatted_output_text=formatted_output_text,
                    updated_discussion=self.discussion_so_far,
                )
                yield {"message": response, "chat_id": self.discussion_id}
        except Exception as e:  # Exception handling for both modes
            print(f"Error in main_discussion: {e}")
            # self.update_logger.error(f"Error in main_discussion: {e}")
            yield {"error": str(e)}

    async def update_discussion_node(
        self,
        discussion_id,
        formatted_output_text=None,
        updated_discussion=None,
        discussion_type=None,
        action=None,
        modifications=None,
        status=None,
        modifications_history=None,
    ):
        # Initialize an empty dictionary for the parameters to update
        update_params = {}

        # Check each parameter and add it to the dictionary if not None
        if formatted_output_text is not None:
            update_params["formatted_output_text"] = formatted_output_text

        if updated_discussion is not None:
            # Convert to JSON for storage as text, as the updated_discussion is in LLM messages format and is a list
            update_params["discussion_so_far"] = json.dumps(updated_discussion)

        if discussion_type is not None:
            update_params["discussion_type"] = discussion_type

        if action is not None:
            update_params["action"] = action
        # print(f'update_node {modifications}')
        if modifications is not None:
            update_params["modifications"] = json.dumps(modifications)

        if modifications_history is not None:
            update_params["modifications_history"] = json.dumps(modifications_history)

        if status is not None:
            update_params["status"] = status

        # Proceed with the update only if there are parameters to update
        if update_params:
            update_params["updated_at"] = datetime.now().isoformat()
            await self.db.update_node_by_id(discussion_id, update_params, "Discussion")

        return
