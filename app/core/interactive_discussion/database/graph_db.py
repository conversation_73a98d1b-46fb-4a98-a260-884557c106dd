from .base_db import BaseDB
from typing import Dict, Optional, List, Tuple
import json


class GraphDB(BaseDB):
    def __init__(self, db):
        super().__init__()
        self.graph = db.get_db()

    async def create_relationship(
        self,
        start_node_id: int,
        end_node_id: int,
        relationship_type: str,
        properties: Optional[Dict] = None,
    ) -> Optional[Dict]:
        properties = properties if isinstance(properties, dict) else {}

        # Build property SET clause
        set_clauses = ", ".join([f"r.{key} = ${key}" for key in properties.keys()])

        query = f"""
        MATCH (n), (m)
        WHERE ID(n) = {start_node_id} AND ID(m) = {end_node_id}
        CREATE (n)-[r:{relationship_type}]->(m)
        {f"SET {set_clauses}" if set_clauses else ""}
        RETURN type(r) AS relationship_type, properties(r) AS properties
        """

        with self.graph.session() as session:
            try:
                result = session.write_transaction(
                    lambda tx: tx.run(
                        query,
                        **properties,
                    ).data()
                )
                return result[0] if result else None
            except Exception:
                return None

    async def create_node(
        self, node_types: List[str], properties: Dict, parent_id: Optional[int] = None
    ) -> Dict:
        labels = ":".join(node_types)

        # Handle repository details
        if "repository_details" in properties:
            properties["repository_details"] = json.dumps(
                properties["repository_details"]
            )

        # Set is_active for Project nodes
        if "Project" in node_types:
            properties["is_active"] = True

        # Construct SET clause
        set_clauses = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"""
        CREATE (n:{labels})
        SET {set_clauses}
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """

        with self.graph.session() as session:
            try:
                result = session.write_transaction(
                    lambda tx: tx.run(query, **properties).data()
                )[0]

                # Create relationship if parent_id exists
                if parent_id is not None:
                    relationship_query = f"""
                    MATCH (parent), (child)
                    WHERE ID(parent) = {parent_id} AND ID(child) = {result["id"]}
                    CREATE (parent)-[r:HAS_CHILD]->(child)
                    """
                    session.write_transaction(
                        lambda tx: tx.run(
                            relationship_query,
                        )
                    )

                return result
            except Exception as e:
                raise Exception(f"Failed to create node: {str(e)}")

    async def update_node_by_id(
        self, node_id: int, properties: Dict, node_type: Optional[str] = None
    ) -> Optional[Dict]:
        set_clauses = ", ".join([f"n.{key} = ${key}" for key in properties.keys()])
        query = f"""
        MATCH (n)
        WHERE ID(n) = {node_id}
        SET {set_clauses}
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """

        with self.graph.session() as session:
            params = {**properties}
            result = session.write_transaction(
                lambda tx: tx.run(query, **params).data()
            )
            return result[0] if result else None

    async def get_one_by_id(
        self, node_id: int, node_type: Optional[str] = None
    ) -> Optional[Dict]:
        match_label = f"(n:{node_type})" if node_type else "(n)"
        query = f"""
        MATCH {match_label}
        WHERE ID(n) = {node_id}
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """

        if node_type == "Project":
            query += ", n.is_active AS is_active"

        print(query, node_type, node_id)

        with self.graph.session() as session:
            result = session.read_transaction(
                lambda tx: tx.run(query).data()
            )
            print(result, "===", node_id)
            if result:
                result = result[0]
                if node_type == "Project" and not result.get("is_active"):
                    return None
                return result
            return None

    async def get_parent_node(self, child_node_id: int) -> Optional[Dict]:
        query = f"""
        MATCH (n)-[:HAS_CHILD]->(m)
        WHERE ID(m) = {child_node_id}
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """

        with self.graph.session() as session:
            result = session.read_transaction(
                lambda tx: tx.run(query).data()
            )
            return result[0] if result else None

    async def get_root_node(self, node_id: int) -> Optional[Dict]:
        query = f"""
        MATCH (n)-[:HAS_CHILD*]->(m)
        WHERE ID(m) = {node_id}
        RETURN ID(n) AS id, LABELS(n) AS labels, properties(n) AS properties
        """

        with self.graph.session() as session:
            result = session.read_transaction(lambda tx: tx.run(query).data())
            return result[-1] if result else None

    async def get_nodes_in_path(
        self, end_node_id: int, start_node_type: str
    ) -> List[Dict]:
        query = f"""
        MATCH path=(n:{start_node_type})-[:HAS_CHILD*]->(m)
        WHERE ID(m) = {end_node_id}
        UNWIND nodes(path) AS node
        RETURN ID(node) AS id, LABELS(node) AS labels, properties(node) AS properties
        """

        with self.graph.session() as session:
            return session.read_transaction(
                lambda tx: tx.run(query).data()
            )

    async def get_nodes_info(
        self, node_id: int, root_node_type: str
    ) -> Tuple[Dict, Dict, Optional[List[Dict]]]:
        nodes_in_path = await self.get_nodes_in_path(node_id, root_node_type)

        if not nodes_in_path:
            current_node = await self.get_one_by_id(node_id)
            if not current_node:
                raise ValueError(f"No node found with ID {node_id}")
            return current_node, current_node, None

        if len(nodes_in_path) == 1:
            return nodes_in_path[0], nodes_in_path[0], nodes_in_path

        return nodes_in_path[0], nodes_in_path[-1], nodes_in_path[1:-1]
