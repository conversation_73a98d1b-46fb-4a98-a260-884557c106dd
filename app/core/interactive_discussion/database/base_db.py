from abc import ABC, abstractmethod


class BaseDB(ABC):

    # @abstractmethod
    # def find(self,query):
    #     raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_one_by_id(self, id):
        raise NotImplementedError("Subclasses must implement this method")

    # @abstractmethod
    # def insert(self,data):
    #     raise NotImplementedError("Subclasses must implement this method")
