from dataclasses import dataclass
from app.core.config import settings
from pymongo import MongoClient
import json
from app.crud.neo4j.mpud_toolkit import init_predictor, process_all_condition
import difflib
import concurrent.futures


def convert_to_json(text):
    # Split the text into lines
    lines = text.strip().split("\n")

    # Initialize dictionary
    result = {}

    # Process each line
    for line in lines:
        if ":" in line:
            # Split at first occurrence of ':'
            key, value = line.split(":", 1)

            # Clean up the key and value
            key = key.strip()
            value = value.strip()

            # Convert key to camelCase
            words = key.split()
            camelKey = words[0].lower() + "".join(
                word.capitalize() for word in words[1:]
            )

            result[camelKey] = value

    return result


def extract_points(data):
    # Check if data is not empty
    if not data:
        return []

    # Access the relevant Points list from the first element in the data array
    points_data = (
        data[0][0]
        .get("message", {})
        .get("modifications", [])[0]
        .get("modified_node", {})
        .get("Points", [])
    )

    # Use map to extract 'point' from each dictionary in the 'Points' list
    points = list(map(lambda x: x.get("point"), points_data))

    return points


def extract_conditions(data):
    conditions = []
    for _data in data:
        conditions.append(_data.get("conditions", [""])[0])

    return conditions


@dataclass
class QuestionRetriever:

    def __post_init__(self):
        self.mongo_client = MongoClient(settings.MONGO_URL)
        self.questions_collection = self.mongo_client[settings.MONGO_DATABASE_LIGHTRAG][
            "questions"
        ]

    """
    "proposed_density_intensity":data["proposed_density_intensity"],
    "location_access": data["location_access"],
    "unique_site_characteristics":data["unique_site_characteristics"],
    "design_elements":data["design_elements"],
    "integration_surroundings":data["integration_surroundings"],
    "phasing_considerations": data["phasing_considerations"],
    "environmental_considerations":data["environmental_considerations"]
    """
    # projectType
    # proposedDensity/intensity
    # locationAndAccess
    # uniqueSiteCharacteristics
    # designElements
    # integrationWithSurroundingDevelopments
    # phasingConsiderations
    # environmentalConsiderations

    def _get_standard_sources(self, header, description="") -> list:

        # header_regex = {"header.data": {"$regex": header, "$options": "i"}}
        # total_docs = len(self.mongo_client[settings.MONGO_DATABASE_LIGHTRAG]["text_chunks"].distinct("file_name", header_regex))
        # common_idss = self.mongo_client[settings.MONGO_DATABASE_LIGHTRAG]["text_chunks"].find({"revelancy_count": {"$gt": 1}, **header_regex}, {"_id": 1, "text.data": 1, "text.coordinates": 1, "text.page": 1, "file_name": 1})

        # return list(common_ids) [ {text, source}]
        # if description:
        #     print('COA WITH DESCRIPTION')
        #     result = process_similar_coa(
        #         Neo4jTool(uri=settings.NEO4J_URI, user=settings.NEO4J_USERNAME, password=settings.NEO4J_PASSWORD).find_similar_points_latest(section_name=header, similarity_threshold=0.60),
        #         description=description, api_key=settings.OPENAI_API_KEY)
        # else:
        # return []
        # result = Neo4jTool(uri=settings.NEO4J_URI, user=settings.NEO4J_USERNAME, password=settings.NEO4J_PASSWORD).find_similar_points_latest(section_name=header, similarity_threshold=0.60)
        try:
            result = init_predictor(settings).find_similar_conditions(
                header, min_file_group=2, limit=5
            )
            result = process_all_condition(result, description)
            common_ids = []
            for condition in result:
                if "metadata" in condition and condition["metadata"]:
                    chunk_id = condition["metadata"][-1]["chunk_id"]
                    data = condition.get("conditions", [""])[0]
                    meta_data = condition.get("metadata", [])
                    is_actionable = condition.get("is_actionable", False)
                    extracted_data = condition.get("extracted_data", {})
                    for meta in meta_data:
                        if (
                            "coordinates" in meta
                            and isinstance(meta["coordinates"], list)
                            and meta["coordinates"]
                        ):
                            meta["coordinates"] = meta["coordinates"][0]
                    common_ids.append(
                        {
                            "common_points": True,
                            "_id": chunk_id,
                            "text": {"data": data},
                            "meta_data": meta_data,
                            "is_actionable": is_actionable,
                            "extracted_data": extracted_data,
                        }
                    )
                # for source in condition.get('metadata',{}):
                #     common_ids.append(
                #         {
                #             '_id':source["chunk_id"],
                #             'text':{
                #                 'coordinates':source["coordinates"],
                #                 'page':source["page"],
                #                 'data':data
                #             },
                #             'file_name':source["source"]
                #         }
                #     )

            return common_ids
        except BaseException:
            return []

    def get_questions(
        self, header: str, description, common_points, neo4j_db_connection, chat_id
    ):
        from app.core.lightragv2.question_generator import QuestionGenerator

        # First check if we already have stored questions for this header
        neo4j_root_info = neo4j_db_connection.get_one_by_id({"id": chat_id})
        if neo4j_root_info.get("all_section_question", {}):
            question_gen = json.loads(neo4j_root_info["all_section_question"])
            if header in question_gen and question_gen[header]:
                print(
                    "returning stored-questions",
                    question_gen[header],
                    type(question_gen[header]),
                )
                return question_gen[header]

        # Get common points
        extracted_common_points = []
        # if common_points:
        #     extracted_common_points = extract_points(common_points)
        # else:
        result = init_predictor(settings).find_similar_conditions(
            header, min_file_group=2, limit=5
        )
        extracted_common_points = extract_conditions(result)
        print("####### COMMON POINTS:", common_points, extracted_common_points)

        header = header.lower().strip()
        questions = []

        if description:
            if isinstance(description, str):
                description = json.loads(description)

            mpud = init_predictor(settings)
            mpud_data = mpud.predict_next_conditions(
                [],
                header,
                project_type=None,
                filter_dict=[],
                next_k=10,
                similarity_threshold=0.0,
                random_state=10,
            )

            # Function to process a single condition
            def process_condition(condition):
                try:
                    (
                        question,
                        word,
                        is_actionable,
                        extracted_data,
                        question_already_generated,
                        point,
                    ) = QuestionGenerator(None)._generate_sync(
                        condition["content"], extracted_common_points, questions.copy(), description
                    )

                    print("question_already_generated", question_already_generated)
                    print("condition[content]", condition["content"])

                    if not question_already_generated:
                        return {
                            "_id": condition["chunk_id"],
                            "point": point if point else condition["content"],
                            "question": question,
                            "word": word,
                            "is_actionable": is_actionable,
                            "extracted_data": extracted_data,
                        }
                    return None
                except Exception as e:
                    print(f"error processing condition {condition.get('chunk_id')}: {e}")
                    return None

            # Process conditions in parallel
            with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(process_condition, condition) for condition in mpud_data]
                for future in concurrent.futures.as_completed(futures):
                    result = future.result()
                    if result:
                        questions.append(result)
                        # Print updated questions for debugging
                        print("updated questions:", questions)

        # Update the database with new questions
        if neo4j_root_info.get("all_section_question", {}):
            cur_sect_questions = json.loads(neo4j_root_info["all_section_question"])
        else:
            cur_sect_questions = {}

        cur_sect_questions.update({header: questions})
        neo4j_db_connection.update_node_by_id_sync(
            node_id=chat_id,
            properties={
                "all_section_question": json.dumps(cur_sect_questions, indent=2)
            },
        )
        filtered_questions = []
        for q in questions:
            is_similar = any(difflib.SequenceMatcher(None, q['question'], cp).ratio() >= 0.8 for cp in extracted_common_points)
            if not is_similar:
                filtered_questions.append(q)

        questions = filtered_questions

        return questions
        with open("testing.txt", "w") as f:
            f.write(f"{header} \n\n {description}")
        format_header = header.lower().strip()
        header_regex = {"header": {"$regex": format_header, "$options": "i"}}
        standard_source = self._get_standard_sources(header)
        if any(standard_source):
            questions = self.questions_collection.find(
                {
                    "_id": {"$nin": [src["_id"] for src in standard_source]},
                    **header_regex,
                },
                {"question": 1, "point": 1},
            )
            return list(questions)
        else:
            return []
