import logging
import os


def get_logger(name: str, log_file: str):
    logger = logging.getLogger(name)
    logger.setLevel(logging.DEBUG)

    ch = logging.StreamHandler()
    ch.setLevel(logging.DEBUG)

    fh = logging.FileHandler(log_file)
    fh.setLevel(logging.DEBUG)

    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    ch.setFormatter(formatter)
    fh.setFormatter(formatter)

    # Add both handlers to the logger
    logger.addHandler(ch)
    logger.addHandler(fh)

    return logger


# Example usage
log_file_path = os.path.join(os.getcwd(), "application.log")  # Log file path
logger = get_logger("my_logger", log_file_path)

logger.debug("This is a debug message")
logger.info("This is an info message")
logger.warning("This is a warning message")
logger.error("This is an error message")
logger.critical("This is a critical message")
