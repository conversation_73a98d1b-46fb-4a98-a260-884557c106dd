from dataclasses import dataclass, fields
from abc import ABC, abstractmethod


@dataclass
class BaseModel(ABC):
    @staticmethod
    def format_field_name(field_name: str) -> str:
        """Format field name into capitalized and space-separated format."""
        return " ".join([word.capitalize() for word in field_name.split("_")])

    @classmethod
    def generate_json_schema(cls):
        schema = {
            cls.__name__: {
                "description": cls.__doc__.strip() if cls.__doc__ else "",
                "attributes": {},
            }
        }

        for f in fields(cls):
            attribute_schema = {
                "type": "string"
                if f.type == str
                else "array"
                if f.type == list
                else "object",
            }
            for k in f.metadata.keys():
                attribute_schema[k] = f.metadata[k]
            schema[cls.__name__]["attributes"][cls.format_field_name(f.name)] = (
                attribute_schema
            )
            schema[cls.__name__]["relationships"] = {
                "hasChild": {
                    "types": ["RequirementRoot", "ArchitectureRoot", "WorkItemRoot"],
                    "description": "Child nodes of the Project",
                }
            }

        return {"model": schema}

    @classmethod
    def generate_json_config(cls):
        schema = {
            cls.__name__: {
                "modified_node_type": cls.__name__,
                "modifiable_fields": [],
                "new_children_type": [],
                "modified_children": False,
                "modified_siblings": False,
                "modified_other_nodes": False,
                "new_relationship_types": [],
                "modified_relationship_types": False,
            }
        }
        for f in fields(cls):
            if f.name in ["type", "configuration_state"]:
                continue
            schema[cls.__name__]["modifiable_fields"].append(
                cls.format_field_name(f.name)
            )

        return schema

    @classmethod
    def get_validations(cls):
        schema = """"""
        for f in fields(cls):
            if f.name in ["type", "configuration_state"]:
                continue
            schema += f"""
            ## FIELD : {f.name} ##
            {f.name} - Validation Rule (Strictly To Follow): {f.metadata.get('validation', 'No validation')}
            """
            # {f.name} Special Instructions: {f.metadata.get('Instruction_to_follow', 'No Special Instructions')}

        return schema

    @abstractmethod
    def get_system_prompt(self):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_additional_prompt_message(self):
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def get_additional_prompt_coa_message(self):
        raise NotImplementedError("Subclasses must implement this method")
