from abc import ABC, abstractmethod
from typing import Any


class BaseLLM(ABC):
    """Abstract base class for implementing LLM functionalities."""
    @abstractmethod
    def llm_chat_completions(self, **kwargs: Any) -> Any:
        """
        Abstract method for generating chat completions using an LLM.
        Subclasses must implement this method.

        Args:
            **kwargs (dict): Keyword arguments for the chat completion.

        Returns:
            Any: Chat completion response.
        """
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def llm_completion(self, **kwargs: Any) -> Any:
        """
        Abstract method for generating text completions using an LLM.
        Subclasses must implement this method.

        Args:
            **kwargs (dict): Keyword arguments for the text completion.

        Returns:
            Any: Text completion response.
        """
        raise NotImplementedError("Subclasses must implement this method")

    @abstractmethod
    def generate_embedding(self, input: str, model: str) -> Any:
        """
        Abstract method for generating embeddings.

        Args:
            input (str): The input text for embedding generation.
            model (str): The model name to be used for embedding generation.

        Returns:
            Any: Embedding vector or related response.
        """
        raise NotImplementedError("Subclasses must implement this method")
