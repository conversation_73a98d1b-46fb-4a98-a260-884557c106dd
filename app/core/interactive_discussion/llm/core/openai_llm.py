from .base_llm import <PERSON>LL<PERSON>
from typing import Any
from openai import OpenAI
from dataclasses import dataclass, field
from anthropic import Anthropic


@dataclass
class OpenAILLM(BaseLLM):
    api_key: str
    base_url: str = field(default="https://api.openai.com/v1/")

    def __post_init__(self):
        self.client: OpenAI = OpenAI(base_url=self.base_url, api_key=self.api_key)

    def llm_chat_completions(self, **kwargs: Any) -> str:
        return self.client.chat.completions.create(**kwargs)

    def llm_completion(self, **kwargs: Any) -> str:
        return self.client.completion(**kwargs)

    def generate_embedding(self, input: str, model: str) -> list:
        return self.client.embeddings.create(input=input, model=model)


@dataclass
class AnthropicLLM(BaseLLM):
    api_key: str
    base_url: str = field(default="https://api.anthropic.com")

    def __post_init__(self):
        self.client = Anthropic(
            api_key=self.api_key,
            base_url=self.base_url
        )

    def llm_chat_completions(self, **kwargs: Any) -> Any:
        """
        Handle chat completions using Anthropic's messages API.
        Returns response in OpenAI-compatible format.
        """
        messages = kwargs.get("messages", [])

        # Extract system message if present
        system_message = None
        filtered_messages = []

        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            elif msg["role"] in ["user", "assistant"]:
                filtered_messages.append(msg)

        anthropic_kwargs = {
            "model": kwargs.get("model"),
            "messages": filtered_messages,
            "max_tokens": kwargs.get("max_tokens", 1024),
            "temperature": kwargs.get("temperature", 0.7),
        }

        if system_message:
            anthropic_kwargs["system"] = system_message

        response = self.client.messages.create(**anthropic_kwargs)

        # Return in OpenAI-compatible format
        return type('OpenAIResponse', (), {
            'choices': [
                type('Choice', (), {
                    'message': type('Message', (), {
                        'content': response.content[0].text
                    })
                })
            ]
        })

    def llm_completion(self, **kwargs: Any) -> Any:
        """
        Handle completions using Anthropic's messages API.
        Returns response in OpenAI-compatible format.
        """
        messages = [{"role": "user", "content": kwargs.get("prompt", "")}]

        anthropic_kwargs = {
            "model": kwargs.get("model"),
            "messages": messages,
            "max_tokens": kwargs.get("max_tokens", 1024),
            "temperature": kwargs.get("temperature", 0.7),
        }

        if "system" in kwargs:
            anthropic_kwargs["system"] = kwargs["system"]

        response = self.client.messages.create(**anthropic_kwargs)

        # Return in OpenAI-compatible format
        return type('OpenAIResponse', (), {
            'choices': [
                type('Choice', (), {
                    'message': type('Message', (), {
                        'content': response.content[0].text
                    })
                })
            ]
        })

    def generate_embedding(self, input: str, model: str) -> list:
        """
        Anthropic does not currently provide an embedding API.
        """
        raise NotImplementedError("Anthropic does not provide an embedding API.")
