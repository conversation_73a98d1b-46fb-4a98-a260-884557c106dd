from .openai_llm import OpenAILLM, AnthropicLLM
from .base_llm import BaseLLM


class LLMFactory():
    @staticmethod
    def get_client(api_key: str, type: str = 'openai') -> BaseLLM:
        # return AnthropicLLM(api_key=settings.ANTHROPIC_API_KEY)
        if type == 'openai':
            return OpenAILLM(api_key=api_key)
        elif type == 'anthropic':
            return AnthropicLLM(api_key=api_key)
        else:
            return OpenAILLM(api_key=api_key)
