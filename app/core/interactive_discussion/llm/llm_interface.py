from dataclasses import dataclass, field
from .core.llm_factory import LLMFactory
from .core.llm_factory import BaseLLM
from .interactions.base_llm_interaction import BaseLLMInteraction
from .interactions.llm_interaction import LLMInteraction
from .utils.llm_output_processor import LLMOutputProcessor
from app.core.telemetry.chat_logger import get_chat_logger

logger = get_chat_logger("pasco.ai.chat")


@dataclass
class LLMInterface:
    instance_name: str
    last_interaction_cost: int = field(init=False, default=0.0)
    user_id: str
    project_id: str
    agent_name: str
    token_limit: str
    api_key: str
    llm_type: str = field(default='openai')
    prompt_tokens: int = field(init=False, default=0)
    completion_tokens: int = field(init=False, default=0)
    total_tokens: int = field(init=False, default=0)
    total_cost: int = field(init=False, default=0.0)
    timeouts: any = field(default_factory=lambda: [90, 180, 300])
    mongo_handler: any = field(default=0)

    def __post_init__(self):
        self.client: BaseLLM = LLMFactory.get_client(api_key=self.api_key, type=self.llm_type)
        self.llm_interaction: BaseLLMInteraction = LLMInteraction(
            parent_ref=self
        )
        self.llm_output_processor = LLMOutputProcessor(llm_interface=self)

    def add_warning_messages(self, remaining_calls, warning_threshold, messages):
        if remaining_calls <= warning_threshold:
            warning_message = f"Warning: Only {remaining_calls} tool call(s) remaining."
            messages.append({"role": "system", "content": warning_message})

        if remaining_calls == 1:
            messages.append(
                {
                    "role": "system",
                    "content": "This is your last function call. Please complete the request after this.",
                }
            )

        if remaining_calls == 0:
            messages.append(
                {
                    "role": "system",
                    "content": "No more function calls are allowed. Please complete the request.",
                }
            )

    def perpare_system_message(self, messages):
        if self.response_format and self.response_format.get("type") == "json_object":
            json_system_message = "You are an AI assistant. When responding, you must always provide your entire response as a single, valid JSON object. There should not be any plain text in the response outside of the JSON object."
            if self.system_prompt:
                self.system_prompt = f"{self.system_prompt}\n\n{json_system_message}"
            else:
                self.system_prompt = json_system_message
        else:
            # Check if there's already a system message
            if self.system_prompt and not any(
                message["role"] == "system" for message in messages
            ):
                # If no system message exists, add the new one
                messages.append({"role": "system", "content": self.system_prompt})

    async def llm_interaction_wrapper(
        self,
        messages,
        user_prompt,
        system_prompt,
        model,
        response_format,
        function_schemas=None,
        function_executor=None,
        stream=False,
        max_retries=5,
        max_tool_calls=25,
        warning_threshold=5,
        validation_function: callable = None,
        validation_attempts=None,
    ):
        logger.debug("Starting LLM interaction wrapper")
        self.model = model
        self.response_format = response_format
        self.function_schemas = function_schemas
        self.stream = stream
        self.system_prompt = system_prompt
        self.max_retries = max_retries

        # Prepare the system message
        self.perpare_system_message(messages=messages)

        json_retry_count = 0
        max_json_retries = 3  # Maximum number of times to retry JSON generation
        tool_calls_count = 0
        called_functions = set()  # Set to track already called functions

        if not validation_attempts:
            self.validation_attempts = 0
        self.max_validation_attempts = 3

        while True:
            tool_calls_count += 1
            remaining_calls = max_tool_calls - tool_calls_count
            if remaining_calls < 0:
                logger.error("Maximum number of tool calls reached. Exiting.")
                return {
                    "error": "Maximum number of tool calls reached. Request could not be completed."
                }

            # Add warnings to messages if needed
            self.add_warning_messages(
                remaining_calls=remaining_calls,
                warning_threshold=warning_threshold,
                messages=messages,
            )

            completion = await self.llm_interaction.invoke_llm(messages=messages)
            if self.token_limit is not None and self.total_tokens >= self.token_limit:
                logger.info(f"Reached token limit of {self.token_limit}, stopping.")
                break
            if stream:
                return self.llm_output_processor.process_stream(
                    completion,
                    messages,
                    called_functions,
                    function_executor,
                    validation_function,
                )

            else:
                result = await self.llm_output_processor.process_non_stream(
                    completion,
                    model,
                    messages,
                    function_executor,
                    response_format,
                    json_retry_count,
                    max_json_retries,
                    validation_function,
                )

                # TODO: need to change the approach
                if result == 1:
                    continue
                else:
                    return result

    def get_model_pricing(self, model):
        pricing_table = [
            {
                "model": "gpt-4o",
                "input_token_cost": 5.0 / 1000000,
                "output_token_cost": 15.0 / 1000000,
            },
            {
                "model": "gpt-4-turbo",
                "input_token_cost": 10.0 / 1000000,
                "output_token_cost": 30.0 / 1000000,
            },
            {
                "model": "gpt-3.5-turbo",
                "input_token_cost": 0.5 / 1000000,
                "output_token_cost": 1.5 / 1000000,
            },
            {
                "model": "gpt-4o-mini",
                "input_token_cost": 0.15 / 1000000,
                "output_token_cost": 0.6 / 1000000,
            },
            {
                "model": "claude-2",
                "input_token_cost": 11.02 / 1000000,
                "output_token_cost": 32.68 / 1000000,
            },
            {
                "model": "claude-instant-1",
                "input_token_cost": 1.63 / 1000000,
                "output_token_cost": 5.51 / 1000000,
            },
            {
                "model": "claude-3-5-sonnet-20240620",
                "input_token_cost": 3.0 / 1000000,
                "output_token_cost": 15.0 / 1000000,
            },
            {
                "model": "claude-3-haiku-20240307",
                "input_token_cost": 0.25 / 1000000,
                "output_token_cost": 1.25 / 1000000,
            },
        ]

        for pricing in pricing_table:
            if pricing["model"] == model:
                return pricing
