from .base_llm_interaction import <PERSON><PERSON><PERSON>nteraction
from dataclasses import dataclass, field
import asyncio
import json
import os
from ..utils.message_processor import MessageProcessor
from app.core.telemetry.chat_logger import get_chat_logger

log = get_chat_logger("pasco.ai.chat")


@dataclass
class LLMInteraction(BaseLLMInteraction):
    parent_ref: any
    message_processor: any = field(default_factory=lambda: MessageProcessor())
    loop: any = field(default_factory=asyncio.get_running_loop)

    def exponential_backoff(self, retry):
        # Basic exponential backoff formula: 2^retry * 100 milliseconds
        return 2**retry * 0.1

    def handle_rate_limit(self, attempt):
        wait = self.exponential_backoff(attempt)
        log.info(f"Rate limit hit, waiting {wait} seconds before retrying...")
        return asyncio.sleep(wait)

    async def handle_network_error(self, attempt):
        wait = self.exponential_backoff(attempt)
        log.warning(f"Network error occurred, waiting {wait} seconds before retrying...")
        await asyncio.sleep(wait)

    async def invoke_llm(self, messages):
        attempt = 0

        # Get the current chat_id from the logger context
        current_chat_id = log.get_chat_id()
        log.debug(f"Using chat_id from context: {current_chat_id}")

        while attempt < self.parent_ref.max_retries:
            timeout = self.parent_ref.timeouts[
                min(attempt, len(self.parent_ref.timeouts) - 1)
            ]

            try:
                return await self.loop.run_in_executor(
                    None, self.blocking_call_wrapper, messages, current_chat_id
                )

            except Exception as e:
                log.error(f"Network error occurred: {str(e)}. Retrying...")
                await self.handle_network_error(attempt)
                attempt += 1

            except asyncio.TimeoutError:
                log.error(
                    f"Timeout occurred after {timeout} seconds. Retrying with a longer timeout."
                )
                attempt += 1

            except Exception as e:
                if "rate limit" in str(e).lower():
                    await self.handle_rate_limit(attempt)
                    attempt += 1
                elif "rate_limit_exceeded" in str(e).lower():
                    await self.handle_rate_limit(attempt)
                    attempt += 1
                else:
                    log.error(f"Error in LLM interaction: {e}")
                    raise e

        log.error("Max retries hit, failing with rate limit error.")
        raise Exception("Rate limit error: Max retries reached.")

    def blocking_call_wrapper(self, messages, chat_id: str):
        # Set chat_id in the new thread
        log.set_chat_id(chat_id)

        # Process messages to keep only the latest screenshot
        self.message_processor.process_messages(messages=messages)

        log.debug("++++++++ BEGIN LLM INPUT++++++++")
        log.debug(
            f"model:{self.parent_ref.model};format:{self.parent_ref.response_format};stream:{self.parent_ref.stream}"
        )
        for message in messages:
            log.debug(json.dumps(message, indent=2))

        log.debug("++++++++++ END LLM INPUT+++++++++")

        kwargs = {
            "messages": messages,  # messages,
            "model": self.parent_ref.model,
            "response_format": self.parent_ref.response_format,
            "temperature": 0,
            "stream": self.parent_ref.stream,
        }

        if self.parent_ref.stream:
            kwargs["stream_options"] = {"include_usage": True}

        if self.parent_ref.function_schemas is not None:
            kwargs["tools"] = self.parent_ref.function_schemas
            kwargs["tool_choice"] = "auto"

        if os.getenv("USE_KEYWORDS_AI") and os.environ["USE_KEYWORDS_AI"] == "true":
            kwargs["extra_body"] = {
                "customer_identifier": self.parent_ref.instance_name
            }
            return self.parent_ref.client.llm_chat_completions(**kwargs)
        else:
            return self.parent_ref.client.llm_chat_completions(**kwargs)
