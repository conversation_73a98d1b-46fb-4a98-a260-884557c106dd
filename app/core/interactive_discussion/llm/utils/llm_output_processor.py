import json
from dataclasses import dataclass
from typing import Callable, List, Any, Dict
from openai.types.chat.chat_completion_message_tool_call import (
    ChatCompletionMessageToolCall,
    Function,
)
from app.core.telemetry.chat_logger import get_chat_logger
# from llm_wrapper.models.function_context import FunctionContext

log = get_chat_logger("pasco.ai.chat")


@dataclass
class FunctionContext:
    id: str
    function_name: str
    function_arguments: Dict[str, Any]


@dataclass
class LLMOutputProcessor:
    llm_interface: any

    async def process_completion(
        self,
        messages,
        response_messages,
        response_format,
        max_json_retries,
        json_retry_count,
        validation_function,
        completion,
        function_executor,
    ):
        response_content = response_messages.content

        # Check if JSON output was requested
        if response_format and response_format.get("type") == "json_object":
            json_response = self.llm_interface.ensure_json_response(response_content)

            if "error" in json_response:
                log.error(
                    f"Failed to parse LLM response as JSON: {json_response['original_response']}"
                )
                if json_retry_count < max_json_retries:
                    json_retry_count += 1
                    log.info(
                        f"Retrying JSON generation (Attempt {json_retry_count}/{max_json_retries})"
                    )

                    # Add an error message to the conversation
                    error_message = f"The previous response was not valid JSON. Please generate a valid JSON response. Error: {json_response['error']}"
                    messages.append({"role": "user", "content": error_message})
                    return {
                        "error": "Failed to generate valid JSON after multiple attempts",
                        "original_response": error_message,
                    }

                else:
                    log.warning(
                        "Max JSON generation retries reached. Returning error response."
                    )
                    return {
                        "error": "Failed to generate valid JSON after multiple attempts",
                        "original_response": response_content,
                    }

            log.debug(f"LLM Final Response (JSON):\n{json.dumps(json_response, indent=2)}")

            if validation_function:
                valid, msg = validation_function(False, completion)
                if not valid:
                    self.llm_interface.validation_attempts += 1
                    if (
                        self.llm_interface.validation_attempts
                        >= self.llm_interface.max_validation_attempts
                    ):
                        log.error(
                            f"Validation failed after {self.llm_interface.max_validation_attempts} attempts. Terminating process."
                        )
                        return {
                            "error": f"Validation failed after {self.llm_interface.max_validation_attempts} attempts",
                            "last_response": json_response,
                        }

                    log.warning(
                        f"Validation failed (Attempt {self.llm_interface.validation_attempts}/{self.llm_interface.max_validation_attempts}): {msg}"
                    )
                    error_message = f"Previous response was not valid. Error: {msg}"
                    messages.append({"role": "user", "content": error_message})

                    # Call the completion function again
                    response = await self.llm_interface.llm_interaction_wrapper(
                        messages=messages,
                        user_prompt=None,
                        system_prompt=None,
                        model=self.llm_interface.model,
                        response_format=self.llm_interface.response_format,
                        function_schemas=self.llm_interface.function_schemas,
                        stream=False,
                        function_executor=function_executor,
                        validation_function=validation_function,
                        validation_attempts=self.llm_interface.validation_attempts,
                    )
                    if isinstance(response, dict):
                        return response
                    else:
                        pass

            # Create a new completion object with the parsed JSON response
            parsed_completion = type(completion)(
                id=completion.id,
                choices=[
                    type(completion.choices[0])(
                        index=completion.choices[0].index,
                        message=type(completion.choices[0].message)(
                            role=completion.choices[0].message.role,
                            content=json.dumps(json_response),
                        ),
                        finish_reason=completion.choices[0].finish_reason,
                    )
                ],
                created=completion.created,
                model=completion.model,
                usage=completion.usage,
            )
            log.debug("+++++++++++++++++ END LLM SESSION++++++++")
            return json.loads(parsed_completion.choices[0].message.content)
        else:
            # If JSON wasn't requested, return the original response
            log.debug(f"LLM Final Response:\n{response_content}")
            log.debug("+++++++++++++++++ END LLM SESSION++++++++")
            return completion.choices[0].message.content

    async def process_tool_call(
        self, messages, response_messages, tool_calls, function_executor
    ):
        messages.append(response_messages)

        for tool_call in tool_calls:
            function_name = tool_call.function.name
            function_args = json.loads(tool_call.function.arguments)

            # TODO: need a consistent API for this. The result should be well
            # known type and common across all functions/agents
            function_result = await function_executor(function_name, function_args)

            assert function_result is not None
            log.debug(
                f"LLM OUTPUT function: {function_name}\nArgs: {json.dumps(function_args, indent=2)}\n Function Response: {str(function_result)}"
            )

            if (
                isinstance(function_result, dict)
                and "skip_last_call" in function_result
                and function_result["skip_last_call"]
            ):
                return function_result
            else:
                messages.append(
                    {
                        "tool_call_id": tool_call.id,
                        "role": "tool",
                        "name": function_name,
                        "content": str(function_result),
                    }
                )

        return 1

    async def process_stream(
        self,
        completion,
        messages,
        called_functions,
        function_executor,
        validation_function: Callable = None,
    ):
        functions = {}
        tool_call_id = None
        last_content = ""  # Variable to store the last non-empty content
        total_prompt_tokens = 0
        total_completion_tokens = 0

        for chunk in completion:
            # Log usage information when available
            if hasattr(chunk, "usage") and chunk.usage:
                log.debug(f"Usage: {chunk.usage}")
                total_prompt_tokens += chunk.usage.prompt_tokens
                total_completion_tokens += chunk.usage.completion_tokens

            if not chunk.choices or not chunk.choices[0].delta:
                continue

            delta = chunk.choices[0].delta

            if delta.tool_calls:
                tool_call = delta.tool_calls[0]

                if tool_call.id:
                    tool_call_id = tool_call.id

                if tool_call.function.arguments:
                    assert tool_call_id is not None
                    functions.setdefault(tool_call_id, {}).setdefault(
                        "function_args", ""
                    )
                    functions[tool_call_id]["function_args"] += (
                        tool_call.function.arguments
                    )

                if tool_call.function.name:
                    assert tool_call_id is not None
                    functions.setdefault(tool_call_id, {})["function_name"] = (
                        tool_call.function.name
                    )

            if delta.content:
                last_content += delta.content  # Update the last content
                yield delta.content

        self.llm_interface.prompt_tokens += total_prompt_tokens
        self.llm_interface.completion_tokens += total_completion_tokens
        self.llm_interface.total_tokens += total_prompt_tokens + total_completion_tokens
        model_pricing = self.llm_interface.get_model_pricing(self.llm_interface.model)
        if model_pricing:
            self.llm_interface.total_cost += (
                total_prompt_tokens * model_pricing["input_token_cost"]
                + total_completion_tokens * model_pricing["output_token_cost"]
            )
            self.llm_interface.last_interaction_cost = (
                total_prompt_tokens * model_pricing["input_token_cost"]
                + total_completion_tokens * model_pricing["output_token_cost"]
            )

        # if self.mongo_handler:
        #     await self.llm_manager.update_interaction_data(
        #         self.user_id,
        #         self.project_id,
        #         self.agent_name,
        #         self.last_interaction_cost,
        #         self.prompt_tokens,
        #         self.completion_tokens,
        #     )

        # print(
        #     f"Stream Usage: Prompt Tokens: {total_prompt_tokens}, Completion Tokens: {total_completion_tokens}"
        # )
        # print(
        #     f"Total input tokens {self.prompt_tokens}, Total output tokens {self.completion_tokens}, Total tokens {self.total_tokens} Total cost ${self.total_cost:.4f}"
        # )

        # After the loop, last_content will contain the final non-empty content
        if last_content:
            log.debug(f"Final content: {last_content}")
            return

        if functions:
            chain = []
            assistant_requested_tool_calls: List[ChatCompletionMessageToolCall] = []

            for tool_id, tool in functions.items():
                function_name = tool["function_name"]
                function_args = tool["function_args"]

                log.debug('function_name: %s', function_name)
                log.debug('function_args: %s', function_args)

                chain.append(
                    FunctionContext(
                        id=tool_id,
                        function_name=function_name,
                        function_arguments=json.loads(function_args),
                    )
                )

                assistant_requested_tool_calls.append(
                    ChatCompletionMessageToolCall(
                        id=tool_id,
                        type="function",
                        function=Function(name=function_name, arguments=function_args),
                    ).model_dump()
                )

            messages.append(
                {
                    "role": "assistant",
                    "tool_calls": assistant_requested_tool_calls,
                }
            )

            for ctx in chain:
                yield "Checking the data"  # process indication
                if ctx.id not in called_functions:  # Check before executing
                    log.debug('ctx.function_arguments: %s', ctx.function_arguments)
                    called_functions.add(id)  # Mark function as called
                    res = await function_executor(
                        ctx.function_name, ctx.function_arguments
                    )
                    log.debug('res-capture_discussion: %s', res)
                    if "skip_last_call" in res and res["skip_last_call"] is True:
                        yield res
                        messages.pop()  # TODO: check with esakki and need to work
                        messages.append(
                            {
                                "tool_call_id": ctx.id,
                                "role": "tool",
                                "name": ctx.function_name,
                                "content": str(res),
                            }
                        )
                        return
                    else:
                        messages.append(
                            {
                                "tool_call_id": ctx.id,
                                "role": "tool",
                                "name": ctx.function_name,
                                "content": str(res),
                            }
                        )

                    yield "Fetching the data"  # process indication

        second_response = ""
        # Call the completion function again
        async for response in await self.llm_interface.llm_interaction_wrapper(
            messages=messages,
            user_prompt=None,
            system_prompt=None,
            model=self.llm_interface.model,
            response_format=self.llm_interface.response_format,
            function_schemas=self.llm_interface.function_schemas,
            stream=True,
            function_executor=function_executor,
            validation_function=validation_function,
            validation_attempts=self.llm_interface.validation_attempts,
        ):
            second_response += response
            yield response

        if validation_function:
            valid, msg = validation_function(True, second_response)
            if not valid:
                self.llm_interface.validation_attempts += 1
                if (
                    self.llm_interface.validation_attempts
                    >= self.llm_interface.max_validation_attempts
                ):
                    log.error(
                        f"Validation failed after {self.llm_interface.max_validation_attempts} attempts. Terminating process."
                    )
                    yield {
                        "error": f"Validation failed after {self.llm_interface.max_validation_attempts} attempts",
                        "last_response": last_content,
                    }
                    return

                log.warning(
                    f"Validation failed (Attempt {self.llm_interface.validation_attempts}/{self.llm_interface.max_validation_attempts}): {msg}"
                )
                error_message = f"Previous response was not valid. Error: {msg}"
                messages.append({"role": "user", "content": error_message})

                # Call the completion function again
                async for response in await self.llm_interface.llm_interaction_wrapper(
                    messages=messages,
                    user_prompt=None,
                    system_prompt=None,
                    model=self.llm_interface.model,
                    response_format=self.llm_interface.response_format,
                    function_schemas=self.llm_interface.function_schemas,
                    stream=True,
                    function_executor=function_executor,
                    validation_function=validation_function,
                    validation_attempts=self.llm_interface.validation_attempts,
                ):
                    continue

    async def process_non_stream(
        self,
        completion,
        model,
        messages,
        function_executor,
        response_format,
        json_retry_count,
        max_json_retries,
        validation_function: callable = None,
    ):
        self.llm_interface.prompt_tokens += completion.usage.prompt_tokens
        self.llm_interface.completion_tokens += completion.usage.completion_tokens
        self.llm_interface.total_tokens += completion.usage.total_tokens
        model_pricing = self.llm_interface.get_model_pricing(model)
        if model_pricing:
            self.llm_interface.total_cost = (
                self.llm_interface.prompt_tokens * model_pricing["input_token_cost"]
                + self.llm_interface.completion_tokens
                * model_pricing["output_token_cost"]
            )
            self.llm_interface.last_interaction_cost = self.llm_interface.total_cost

        if self.llm_interface.mongo_handler:
            await self.llm_interface.llm_manager.update_interaction_data(
                self.llm_interface.user_id,
                self.llm_interface.project_id,
                self.llm_interface.agent_name,
                self.llm_interface.last_interaction_cost,
                self.llm_interface.prompt_tokens,
                self.llm_interface.completion_tokens,
            )

        response_messages = completion.choices[0].message
        # tool_calls = response_messages.tool_calls
        # tool_calls = response_messages.get('tool_calls', None)

        tool_calls = (
            response_messages.tool_calls
            if hasattr(response_messages, "tool_calls")
            else None
        )

        log.debug(
            f"Usage: Prompt Tokens: {completion.usage.prompt_tokens}, Completion Tokens: {completion.usage.completion_tokens}, Tokens: {completion.usage.total_tokens}"
        )
        log.debug(
            f"Total input tokens {self.llm_interface.prompt_tokens}, Total output tokens {self.llm_interface.completion_tokens}, Total tokens {self.llm_interface.total_tokens} Total cost ${self.llm_interface.total_cost:.4f}"
        )
        if tool_calls:
            return await self.process_tool_call(
                messages=messages,
                response_messages=response_messages,
                tool_calls=tool_calls,
                function_executor=function_executor,
            )
        else:
            return await self.process_completion(
                messages=messages,
                response_messages=response_messages,
                response_format=response_format,
                max_json_retries=max_json_retries,
                json_retry_count=json_retry_count,
                validation_function=validation_function,
                completion=completion,
                function_executor=function_executor,
            )
