import json


class MessageProcessor:
    def __init__(self):
        self.screenshot_found = False

    def _update_arguments(self, arguments):
        """Update 'image_for_llm' in the arguments if found and required."""
        try:
            result = json.loads(arguments)
            if "image_for_llm" in result:
                if self.screenshot_found:
                    result["image_for_llm"] = "image removed to abbreviate"
                else:
                    self.screenshot_found = True
                return json.dumps(result)
        except json.JSONDecodeError:
            pass
        return arguments

    def process_messages(self, messages) -> None:
        for message in reversed(messages):
            if isinstance(message, dict):
                if "function_call" in message:
                    message["function_call"]["arguments"] = self._update_arguments(
                        message["function_call"]["arguments"]
                    )
                elif "tool_calls" in message:
                    for tool_call in message["tool_calls"]:
                        if (
                            isinstance(tool_call, dict)
                            and tool_call.get("type") == "function"
                        ):
                            tool_call["function"]["arguments"] = self._update_arguments(
                                tool_call["function"]["arguments"]
                            )
                        elif (
                            hasattr(tool_call, "function")
                            and tool_call.type == "function"
                        ):
                            tool_call.function.arguments = self._update_arguments(
                                tool_call.function.arguments
                            )
