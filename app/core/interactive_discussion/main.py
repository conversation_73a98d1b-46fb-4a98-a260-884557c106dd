from .discussion.discussion import Discussion
from .discussion.discussion_context_setter import (
    DiscussionContextSetter,
)
from .tools.discussion_tool import DiscussionTools
from .tools.utils.tools_available import ToolsAvailable
from .discussion.discussion_essentials import DiscussionEssentials
from .database.graph_db import GraphDB
from .database.graph.neo4j import Neo4j
from dataclasses import dataclass, field
from .base_model import BaseModel


@dataclass
class InteractiveDiscussion:
    model: BaseModel
    function_schema_type: str
    node_type: str
    discussion_type: str
    db_uri: str
    db_password: str
    db_user: str
    node_id: int
    discussion_id: int
    title: str
    description: str
    api_key: str
    step: int
    prev_context: dict
    initial_mpud_description: dict
    prompt_file: str = field(default="main_configuration.prompt")
    # chat_type: str = field(default="Create New")

    def __post_init__(self):
        print('self.initial_mpud_description----->>>>>', self.initial_mpud_description)
        print('self.prompt_file', self.prompt_file)
        self.discussion_context: DiscussionContextSetter = DiscussionContextSetter(
            available_tools=[ToolsAvailable("DiscussionTools", DiscussionTools)],
            discussion_essentials=DiscussionEssentials(self.model, self.prev_context, self.initial_mpud_description, self.prompt_file),
            function_schema_type=self.function_schema_type,
            node_type=self.node_type,
            discussion_type=self.node_type,
        )
        self.discussion = Discussion(
            db=GraphDB(
                Neo4j(
                    uri=self.db_uri,
                    user=self.db_user,
                    password=self.db_password,
                )
            ),
            vector_db=None,
            node_id=self.node_id,
            discussion_id=self.discussion_id,
            title=self.title,
            description=self.description,
            discussion_context=self.discussion_context,
            api_key=self.api_key,
            discussion_type=self.discussion_type,
            step=self.step,
        )

    async def start(self, message, stream):
        await self.discussion.async_initialize()
        return self.discussion.initiate_discussion_with_llm(
            user_comment=message, stream=stream
        )
