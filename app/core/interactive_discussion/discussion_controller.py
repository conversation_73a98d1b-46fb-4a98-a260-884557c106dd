from dataclasses import dataclass, field
from app.core.interactive_discussion import InteractiveDiscussion
from app.core.config import settings
import importlib
from app.database.neo4j_db import Neo4jDB
import uuid
import json
from app.crud.application_repository import ApplicationRepository
from fastapi import HTTPException
from pymongo import MongoClient
from app.core.interactive_discussion.utils.question_retriever import <PERSON><PERSON><PERSON>riever
from datetime import datetime, timezone
from app.core.coa_models.clean_data import deduplicate_headers
from openai import OpenAI
from typing import List, Dict, Any, AsyncGenerator
from neo4j import GraphDatabase
import logging
from app.crud.chat_repository import ChatRepository
from openai import AsyncOpenAI

prompt = """
[TASK]
You are an AI designed to guide users through the configuration of Master Planned Unit Developments (MPUDs).
** No need to summarize collected information twice, reduce the Redundancy in output messages. **

IMPORTANT: Your first message must include a greeting followed by this exact text in markdown bold format:

    - **Welcome to the MPUD Extraction process. If you have relevant documentation about your MPUD, you can upload it to help streamline the process.**

    - I'm here to assist selection of COAs from  Master Planned Unit Development.
---
After displaying this message, if the user tries to communicate, reply in a generic way
"""

client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
logger = logging.getLogger(__name__)


def get_chunk_details(
    chunk_id_list: List[str],
    uri: str = settings.NEO4J_URI,
    username: str = settings.NEO4J_USERNAME,
    password: str = settings.NEO4J_PASSWORD,
) -> List[Dict[str, Any]]:
    """
    Retrieves chunk details from Neo4j database and formats them according to specified structure.

    Args:
        chunk_id_list (List[str]): List of chunk IDs to query
        uri (str): Neo4j database URI
        username (str): Database username
        password (str): Database password

    Returns:
        List[Dict[str, Any]]: List of dictionaries containing formatted chunk details
    """
    # Create Neo4j driver
    driver = GraphDatabase.driver(uri, auth=(username, password))

    try:
        with driver.session() as session:
            # Construct query with parameterized chunk_id list
            query = """
            MATCH (n:Condition)
            WHERE n.chunk_id IN $chunk_ids
            RETURN DISTINCT n.chunk_id as _id, n.file_name as file_name, n.page as page,
                   n.coordinates as coordinates, n.content as content
            """

            # Execute query
            result = session.run(query, chunk_ids=chunk_id_list)

            # Process results into required format
            formatted_results = []
            for record in result:
                chunk_detail = {
                    "_id": record["_id"],
                    "file_name": record["file_name"],
                    "text": {
                        "coordinates": record["coordinates"],
                        "data": record["content"],
                        "page": record["page"],
                    },
                    "page": record["page"],
                }
                formatted_results.append(chunk_detail)

            return formatted_results

    finally:
        driver.close()


@dataclass
class FinalizeGenerator:
    discussion_id: any
    sections_available: list

    async def start(self, message, stream):
        resp = self.finalize()
        return resp

    async def finalize(self):
        if self.discussion_id:
            llm_response = {"discussion_type": f"layout_{self.sections_available[-1]}"}
            llm_response["content"] = "Thank you chat finished.😊"
            yield {
                "message": llm_response,
                "chat_id": self.discussion_id,
            }
        else:
            llm_response = {"discussion_type": "layout_final"}
            llm_response["content"] = (
                "😊 Thank you . ✅ Your document is ready to go. Visit the MPUD tab to export it."
            )
            llm_response["is_stream_complete"] = True
            yield {
                "message": llm_response,
                "chat_id": "",
            }


@dataclass
class DiscussionController:
    MODELS_AVAILABLE = {"step-1": "mpud", "step-2": "layout"}
    MODELS_DISPLAY = {"step-1": "info", "step-2": "layout"}
    chat_id: str
    message: str
    stream: bool
    user_id: str
    discussion_type: str
    user: dict
    chat_type: str
    question_retriever: QuestionRetriever = field(
        default_factory=lambda: QuestionRetriever()
    )
    project_id: str = field(default=None)

    async def setup_neo4j_instance(self):
        """Set up the Neo4j instance and assign it to an instance variable."""
        self.neo4j_db = Neo4jDB()
        self.neo4j_db.connect()
        self.mpud_description_info = {}
        await self.setup_mongo_instance()

    async def setup_mongo_instance(self):
        """Set up the Mongo instance and assign it to an instance variable."""
        self.mongo_client = MongoClient(settings.MONGO_URL)
        self.mongo_db = self.mongo_client[settings.MONGO_DATABASE_LIGHTRAG]
        self.text_chunks = self.mongo_db["text_chunks"]

    async def init_discussion(self):
        await self.setup_neo4j_instance()
        await self.check_and_create_user_node()
        await self.application_creation()

        if self.chat_type == "Create New":
            return self.init_chat()

        if self.chat_type in ["Upload", "Extract"]:
            return self.init_chat()

    async def generate_chat_stream(self) -> AsyncGenerator[Dict[str, Any], None]:
        """Real streaming from OpenAI"""

        prompt = """
        [TASK]
        You are an AI designed to guide users through the configuration of Master Planned Unit Developments (MPUDs).
        ** Do not include any welcome/greeting messages in your responses - the system will handle this **

        IMPORTANT: Focus only on content extraction and guidance. If this is the welcome prompt, do not extract or analyze content.
        Just acknowledge that document processing has started and user can begin.
        """
        try:
            response = await client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "assistant", "content": prompt}],
                stream=True  # This enables real streaming
            )
            accumulated_content = ""
            async for chunk in response:
                if chunk.choices[0].delta.content:
                    delta_content = chunk.choices[0].delta.content
                    accumulated_content += delta_content

                    yield {
                        'chat_id': self.chat_id,
                        'message': {
                            'role': 'assistant',
                            'content': accumulated_content,
                            'is_stream_complete': False,
                            'discussion_type': None
                        }
                    }

            # Final message with completion flag
            yield {
                'chat_id': self.chat_id,
                'message': {
                    'role': 'assistant',
                    'content': accumulated_content,
                    'is_stream_complete': True,
                    'discussion_type': None
                }
            }
        except Exception as e:
            error_msg = f"Error processing stream: {str(e)}"
            yield {'error': error_msg}

    async def application_creation(self) :
        properties = {'title': 'No title provided yet', 'request': 'No request submitted yet', 'status': 'todo', 'chat_id': str(self.chat_id), 'id': str(uuid.uuid4()), 'created_at': datetime.now(timezone.utc).isoformat(), "last_modified": datetime.now(timezone.utc).isoformat(), "user_name": self.user.get('name', '')}
        rr = self.neo4j_db.has_edge(start_node=self.chat_id, relationship='ASSIGNED_TO')
        if not rr :
            resp = self.neo4j_db.match_create_merge(self.chat_id, 'application', 'ASSIGNED_TO', properties)
            if not resp:
                raise ValueError("Failed to create application.")

    async def create_discussion_node(self, props):
        node = self.neo4j_db.create_node("Discussion", props)
        self.neo4j_db.create_edge(
            int(self.chat_id), node["node_id"], "HAS_CHILD", {"id": str(uuid.uuid4())}
        )
        return node["node_id"]

    async def get_step_model(self):
        # try:
        model_mapped_with_step = self.MODELS_AVAILABLE[f"step-{self.step}"]
        if self.step > 2:

            def get_model(common_points=[]):
                header = model_mapped_with_step.split("_")[-1].lower()
                mod = model_mapped_with_step.split("_")[0].lower()
                module_path = f"app.models.interactive_models.{mod}_model"
                module = importlib.import_module(module_path)
                models = getattr(module, "coa_dynamic")
                model, number_of_questions = models(
                    header,
                    self.neo4j_db,
                    self.chat_id,
                    self.mpud_description_info,
                    common_points,
                )
                return model, len(number_of_questions), header

            total_steps = len(list(self.MODELS_AVAILABLE.values()))
            common_points_layout = []
            model, question_number, header = get_model()
            while question_number == 0 and self.step < total_steps:
                try:
                    if self.discussion_id is None:
                        header = self.MODELS_DISPLAY[f"step-{self.step}"][7:]
                        node_id = await self.create_discussion_node(
                            {
                                "step": self.step,
                                "discussion_type": header,
                                "id": str(uuid.uuid4()),
                            }
                        )
                        self.discussion_id = node_id
                    common_points = await self.get_common_points(self.discussion_id)
                    self.discussion_id = None
                    common_points_layout.append(common_points)
                    await self.next_step()
                    self.prev_step = self.step
                    model_mapped_with_step = self.MODELS_AVAILABLE[f"step-{self.step}"]
                    model, question_number, header = get_model(common_points_layout)
                except BaseException:
                    break
            # COASubclass(coa_header=header)
            if not self.step < total_steps:
                return None, None, None, []
            return model, "COA", header, common_points_layout

        else:
            if model_mapped_with_step.lower() == "mpud":
                module_path = (
                    f"app.models.interactive_models.{model_mapped_with_step}_model"
                )
                class_name = model_mapped_with_step.upper()
                module = importlib.import_module(module_path)
                models = getattr(module, "mpud_dynamic")
                # project_repo = ChatRepository(user=self.user, db=self.neo4j_db)
                # project_data = project_repo.get_project_by_chatid(self.chat_id)
                # if project_data:
                #     if "assets" in project_data:
                #         assets = json.loads(project_data["assets"])
                #         model = models(self.chat_id, assets)
                #         return model, class_name, class_name, []
                chat_repo = ChatRepository(user=self.user, db=self.neo4j_db)
                asset_result = chat_repo.get_assets(chat_id=self.chat_id)
                if asset_result and asset_result[0]["n.assets"]:
                    assets = json.loads(asset_result[0]["n.assets"])
                    model = models(self.chat_id, assets)
                    return model, class_name, class_name, []
                else:
                    model = models(self.chat_id)
                    return model, class_name, class_name, []
            else:
                module_path = (
                    f"app.models.interactive_models.{model_mapped_with_step}_model"
                )
                class_name = model_mapped_with_step.upper()
                module = importlib.import_module(module_path)
                return model, class_name, class_name, []

        # except Exception as e:
        #     raise ValueError(f"Invalid discussion type: {str(e)}")

    async def check_and_create_user_node(self):
        node = self.neo4j_db.has_node("user", {"id": self.user_id})
        if not node:
            node = self.neo4j_db.create_node("user", {"id": self.user_id})
        self.node_id = node["node_id"]
        if self.chat_id:
            chat_node = self.neo4j_db.get_one_by_id({"id": self.chat_id})
            self.step = chat_node["step"]
            self.mpud_description_info = chat_node.get("mpud_description_info", {})
            discussion_types = list(self.MODELS_DISPLAY.values())
            if self.discussion_type in discussion_types:
                idx = discussion_types.index(self.discussion_type)
                if idx < 1:
                    raise ValueError("Invalid discussion type to switch")
                self.step = idx + 1
                self.neo4j_db.update_node_property(
                    node_id=self.chat_id, property_name="step", new_value=self.step
                )
            self.root_id = chat_node["id"]

            if "models_available" in chat_node and chat_node["models_available"]:
                self.MODELS_AVAILABLE = {
                    f"step-{idx + 1}": i
                    for idx, i in enumerate(chat_node["models_available"])
                }

            if "models_display" in chat_node and chat_node["models_display"]:
                self.MODELS_DISPLAY = {
                    f"step-{idx + 1}": i
                    for idx, i in enumerate(chat_node["models_display"])
                }
        else:
            self.root_id = str(uuid.uuid4())
            chat_node = self.neo4j_db.create_node(
                "DiscussionRoot", {"id": self.root_id, "step": 1}
            )
            self.chat_id = chat_node["node_id"]
            self.neo4j_db.create_edge(self.node_id, self.chat_id, "HAS_CHILD")
            if self.project_id:
                self.neo4j_db.create_edge(
                    self.project_id, self.chat_id, "HAS_DISCUSSION"
                )
            self.step = 1
        discussion_node = self.neo4j_db.get_child_node(
            "DiscussionRoot", "Discussion", {"id": self.root_id}, {"step": self.step}
        )
        if discussion_node:
            self.discussion_id = discussion_node["node_id"]
        else:
            self.discussion_id = None
        self.prev_step = self.step

    async def get_step_discussion(self, prev_context={}, initial_mpud_description={}):
        model, class_name, header, common_points_layout = await self.get_step_model()
        if not model:
            if self.discussion_id:
                return (
                    FinalizeGenerator(self.discussion_id, self.sections_available),
                    self.sections_available[-1],
                    common_points_layout,
                )
            else:
                return (
                    FinalizeGenerator(None, ["Final"]),
                    "Final",
                    common_points_layout,
                )
        if class_name == "COA":
            return (
                InteractiveDiscussion(
                    model=model,
                    db_password=settings.NEO4J_PASSWORD,
                    db_uri=settings.NEO4J_URI,
                    db_user=settings.NEO4J_USERNAME,
                    description="",
                    discussion_id=self.discussion_id,
                    discussion_type=self.MODELS_DISPLAY[f"step-{self.step}"],
                    function_schema_type=class_name,
                    node_id=int(self.chat_id),
                    node_type=header,
                    title="",
                    api_key=settings.OPENAI_API_KEY,
                    step=self.step,
                    prev_context=prev_context,
                    initial_mpud_description=initial_mpud_description,
                    prompt_file="main_coa_configuration.prompt",
                    # chat_type=self.chat_type,
                ),
                header,
                common_points_layout,
            )
        if class_name == "LAYOUT":
            return (
                InteractiveDiscussion(
                    model=model,
                    db_password=settings.NEO4J_PASSWORD,
                    db_uri=settings.NEO4J_URI,
                    db_user=settings.NEO4J_USERNAME,
                    description="",
                    discussion_id=self.discussion_id,
                    discussion_type=self.MODELS_DISPLAY[f"step-{self.step}"],
                    function_schema_type=class_name,
                    node_id=int(self.chat_id),
                    node_type=header,
                    title="",
                    api_key=settings.OPENAI_API_KEY,
                    step=self.step,
                    prev_context=prev_context,
                    prompt_file="dynamic_prompt_from_model.prompt",
                ),
                header,
                common_points_layout,
            )

        return (
            InteractiveDiscussion(
                model=model,
                db_password=settings.NEO4J_PASSWORD,
                db_uri=settings.NEO4J_URI,
                db_user=settings.NEO4J_USERNAME,
                description="",
                discussion_id=self.discussion_id,
                discussion_type=self.MODELS_DISPLAY[f"step-{self.step}"],
                function_schema_type=class_name,
                node_id=int(self.chat_id),
                node_type=header,
                title="",
                api_key=settings.OPENAI_API_KEY,
                step=self.step,
                prev_context=prev_context,
                initial_mpud_description=initial_mpud_description,
            ),
            header,
            common_points_layout,
        )

    async def next_step(self):
        self.step += 1
        self.neo4j_db.update_node_property(
            node_id=self.chat_id, property_name="step", new_value=self.step
        )

    async def create_application_and_assign_edge(self, modified):
        application_data = {
            "mpud_info": modified,
            "chat_id": self.chat_id,
        }

        app_repo = ApplicationRepository(user=self.user, db=self.neo4j_db)

        try:
            application = app_repo.update_application_with_discussion_assignment(
                application_data, self.chat_id,
            )
            if not application or "node_id" not in application:
                raise ValueError("Failed to create application or missing node_id.")

        except Exception as e:
            print(e)
            raise HTTPException(status_code=500, detail="Application creation failed.")

    async def common_points_format_change(self, data, header):
        result = {
            "message": {
                "modifications": [
                    {
                        "modified_node": {"Points": []},
                        "new_child_nodes": None,
                        "modified_children": None,
                        "modified_siblings": None,
                        "modified_similar_nodes": None,
                        "new_relationships": None,
                        "modified_relationships": None,
                        "new_relationship_types": None,
                        "created_at": datetime.utcnow().isoformat(),
                    }
                ],
                "discussion_type": "layout_" + header,
            },
            "chat_id": self.chat_id,
        }

        points = []
        # Iterate through the provided data chunks
        for chunk in data:
            # Extract relevant data for each chunk
            chunk_id = chunk["_id"]
            data = chunk["text"]["data"]
            is_actionable = chunk["is_actionable"]
            extracted_data = chunk["extracted_data"]

            if "common_points" in chunk:
                meta_lis = []
                for _meta in chunk["meta_data"]:
                    meta = {}  # Create new dictionary for each iteration
                    meta["file_name"] = _meta["source"]
                    meta["coordinates"] = (
                        _meta["coordinates"][0]
                        if isinstance(_meta["coordinates"], list)
                        else _meta["coordinates"]
                    )
                    meta["page_no"] = _meta["page"]
                    meta_lis.append(meta)

                points.append(
                    {
                        "point": data,
                        "chunk_id": chunk_id,
                        "meta_data": meta_lis,
                        "is_actionable": is_actionable,
                        "extracted_data": extracted_data,
                    }
                )
            else:
                meta_data = {}  # Reset meta_data for non-common points
                coordinates = (
                    chunk["text"]["coordinates"][0]
                    if isinstance(chunk["text"]["coordinates"], list)
                    else chunk["text"]["coordinates"]
                )

                meta_data["file_name"] = chunk["file_name"]
                meta_data["coordinates"] = coordinates
                meta_data["page_no"] = chunk["text"]["page"]
                points.append(
                    {
                        "point": data,
                        "chunk_id": chunk_id,
                        "meta_data": [meta_data],
                        "is_actionable": is_actionable,
                        "extracted_data": extracted_data,
                    }
                )

        result["message"]["modifications"][0]["modified_node"]["Points"] = points
        yield result

    async def get_common_points(self, current_discussion_id):
        header = self.MODELS_DISPLAY[f"step-{self.step}"][7:]
        resp = self.question_retriever._get_standard_sources(
            header, self.mpud_description_info
        )
        print("Common Point :", resp)
        respp = []
        async for item in self.common_points_format_change(resp, header):
            respp.append(item)
        await self.neo4j_db.update_node_by_id(
            node_id=current_discussion_id,
            properties={"common_points": json.dumps(respp)},
        )
        return respp

    async def init_chat(self):
        if self.step != 2:
            discussion, header, common_points_layout = await self.get_step_discussion(
                initial_mpud_description=self.mpud_description_info
            )
            chat_response = await discussion.start(
                message=self.message, stream=self.stream
            )
            modified = {}

            for common_point in common_points_layout:
                yield (
                    common_point[0] if isinstance(common_point, list) else common_point
                )

            async for data in chat_response:
                data["message"]["discussion_type"] = self.MODELS_DISPLAY[
                    f"step-{self.step}"
                ]
                if "modifications" in data["message"]:
                    modified = data["message"]["modifications"][0]["modified_node"]
                data["chat_id"] = self.chat_id

                if data.get("message", {}).get("chat_finished", False):
                    step_list = []
                    # if self.step == 2:
                    #     if (
                    #         "message" in data
                    #         and "modified_node" in data["message"]
                    #         and data["message"]["modified_node"]
                    #     ):
                    #         if "modified_node" in data["message"]["modified_node"][0]:
                    #             modified_data = data["message"]["modified_node"][0][
                    #                 "modified_node"
                    #             ]
                    #             if "Conditions" in modified_data:
                    #                 step_list = modified_data["Conditions"]

                    #                 temp_step = 0
                    #                 temp_step += self.step
                    #                 for step in step_list:
                    #                     temp_step += 1
                    #                     self.MODELS_AVAILABLE[
                    #                         "step-" + str(temp_step)
                    #                     ] = f"coa_{step.strip()}"
                    #                     self.MODELS_DISPLAY[
                    #                         "step-" + str(temp_step)
                    #                     ] = f"layout_{step.strip()}"

                    #                     self.neo4j_db.update_node_property(
                    #                         node_id=self.chat_id,
                    #                         property_name="models_available",
                    #                         new_value=list(
                    #                             self.MODELS_AVAILABLE.values()
                    #                         ),
                    #                     )
                    #                     self.neo4j_db.update_node_property(
                    #                         node_id=self.chat_id,
                    #                         property_name="models_display",
                    #                         new_value=list(
                    #                             self.MODELS_DISPLAY.values()
                    #                         ),
                    #                     )

                    #                 if self.step >= len(
                    #                     self.MODELS_AVAILABLE
                    #                 ):
                    #                     print(
                    #                         "***" * 7,
                    #                         "\n",
                    #                         "CHAT COMPLETE",
                    #                         "\n",
                    #                         "***" * 7,
                    #                     )
                    #                     break
                    if self.step == 1 and self.discussion_id:
                        if modified:
                            self.mpud_description_info = modified
                            await self.neo4j_db.update_node_by_id(
                                node_id=self.chat_id,
                                properties={
                                    "mpud_description_info": json.dumps(
                                        modified, indent=2
                                    )
                                },
                            )
                            await self.create_application_and_assign_edge(
                                modified,
                            )

                    if self.step > 2:
                        map = {}
                        chunk_id_list = []
                        for pts in modified["Points"]:
                            if (
                                "chunk_id" in pts
                                and pts["chunk_id"] not in chunk_id_list
                            ):
                                chunk_id_list.append(pts["chunk_id"])

                        mongo_resp = get_chunk_details(chunk_id_list)
                        for rec in mongo_resp:
                            map[rec["_id"]] = {
                                "meta_data": [
                                    {
                                        "file_name": rec["file_name"],
                                        "coordinates": rec["text"]["coordinates"],
                                    }
                                ]
                            }

                        # print('modified["Points"]', modified["Points"])
                        # for pts in modified["Points"] :
                        #     if 'meta_data' in pts:
                        #         pts['meta_data'] = [pts['meta_data']]

                        # print('modified["Points"]-----', modified["Points"])

                        for pts in modified["Points"]:
                            if "chunk_id" in pts and pts["chunk_id"] in map:
                                pts["meta_data"] = map[pts["chunk_id"]].get(
                                    "meta_data", []
                                )

                        print("modified--->>", modified)

                        data["message"]["modified_node"][0]["modified_node"] = modified
                    await self.next_step()

                yield data

        if self.prev_step != self.step and self.step <= len(
            list(self.MODELS_AVAILABLE.keys())
        ):
            if self.step == 2:
                # self.sections_available = self.text_chunks.distinct("header.data", {})

                raw_headers = self.text_chunks.distinct("header.data", {})
                self.sections_available = await deduplicate_headers(
                    raw_headers, openai_client=OpenAI(api_key=settings.OPENAI_API_KEY)
                )

                data = {
                    "message": {
                        "modifications": [
                            {"modified_node": {"Conditions": self.sections_available}}
                        ]
                    }
                }
                node_id = await self.create_discussion_node(
                    {
                        "modifications": json.dumps(
                            data["message"]["modifications"][0]
                        ),
                        "step": self.step,
                        "discussion_type": "layout",
                        "id": str(uuid.uuid4()),
                    }
                )
                self.discussion_id = node_id
                if (
                    "message" in data
                    and "modifications" in data["message"]
                    and data["message"]["modifications"]
                ):
                    if "modified_node" in data["message"]["modifications"][0]:
                        modified_data = data["message"]["modifications"][0][
                            "modified_node"
                        ]
                        if "Conditions" in modified_data:
                            step_list = modified_data["Conditions"]

                            temp_step = 0
                            temp_step += self.step
                            for step in step_list:
                                temp_step += 1
                                self.MODELS_AVAILABLE["step-" + str(temp_step)] = (
                                    f"coa_{step.strip()}"
                                )
                                self.MODELS_DISPLAY["step-" + str(temp_step)] = (
                                    f"layout_{step.strip()}"
                                )

                                self.neo4j_db.update_node_property(
                                    node_id=self.chat_id,
                                    property_name="models_available",
                                    new_value=list(self.MODELS_AVAILABLE.values()),
                                )
                                self.neo4j_db.update_node_property(
                                    node_id=self.chat_id,
                                    property_name="models_display",
                                    new_value=list(self.MODELS_DISPLAY.values()),
                                )

                            if self.step >= len(self.MODELS_AVAILABLE):
                                print(
                                    "***" * 7,
                                    "\n",
                                    "CHAT COMPLETE",
                                    "\n",
                                    "***" * 7,
                                )
                data["message"]["discussion_type"] = "layout"
                yield data
                await self.next_step()

            if self.prev_step <= 2:
                prev_discussion_modification_list = []
            else:
                prev_discussion = self.neo4j_db.get_child_node(
                    label="DiscussionRoot",
                    child_label="Discussion",
                    properties={"id": self.root_id},
                    child_properties={"step": self.step - 1},
                )
                prev_discussion_modification_list = prev_discussion.get(
                    "prev_discussion", "[]"
                )
                prev_discussion_modification_list = json.loads(
                    prev_discussion_modification_list
                )

            prev_discussion_modification_list.append(
                data.get("modified_node", [{}])[-1]
            )
            await self.neo4j_db.update_node_by_id(
                node_id=self.discussion_id,
                properties={
                    "prev_discussion": json.dumps(prev_discussion_modification_list)
                },
            )
            self.discussion_id = None
            discussion, header, common_points_layout = await self.get_step_discussion(
                modified, self.mpud_description_info
            )
            chat_response = await discussion.start(
                message=self.message, stream=self.stream
            )

            for common_point in common_points_layout:
                yield (
                    common_point[0] if isinstance(common_point, list) else common_point
                )

            count = 0
            async for data in chat_response:
                count += 1
                data["message"]["discussion_type"] = self.MODELS_DISPLAY[
                    f"step-{self.step}"
                ]
                if data.get("message", {}).get("chat_finished", False):
                    await self.next_step()
                current_discussion_id = data["chat_id"]

                if count == 1 and self.step > 2:
                    common_points = await self.get_common_points(current_discussion_id)
                    yield (
                        common_points[0]
                        if isinstance(common_points, list)
                        else common_points
                    )

                data["chat_id"] = self.chat_id
                yield data
