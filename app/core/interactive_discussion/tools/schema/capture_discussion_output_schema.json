{"type": "function", "function": {"name": "capture_discussion_output", "description": "Save all consolidated modifications from the current discussion for {discussion_type} of a {node_type} node. This function is called once at the end of a discussion. The parameters should reflect all the changes made during the discussion.Keep all rich text formatting intact if there are any.", "parameters": {"type": "object", "properties": {"modified_node": {"type": "object", "description": "Specified fields of the current node, with or without any modifications as required by this discussion", "properties": {}, "required": "{required_config}"}}, "required": ["modified_node", "reason_for_this_call"]}}}