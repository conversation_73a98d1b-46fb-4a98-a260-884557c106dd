from datetime import datetime
from ..llm.llm_interface import LLMInterface
from ..tools.base_tool import BaseTool
from ..tools.function_schema_generator import FunctionSchemaGenerator
import json


class DiscussionTools(BaseTool):
    def __init__(self, base_path, logger, llm=None, user_id=None, discussion=None):
        super().__init__(logger)
        self.user_id = user_id
        self.node_db = lambda: []
        self.discussion = discussion

        self.function_schemas = [
            FunctionSchemaGenerator.get_function_schema(
                self.discussion.discussion_context.function_schema_type,
                self.discussion.discussion_context.discussion_essentials.discussion_configurations,
                self.discussion.discussion_context.discussion_essentials.data_model
            )
        ]

        self.function_mapping.update(
            {"capture_discussion_output": self.capture_discussion_output}
        )

        self.llm = LLMInterface(
            "discussion_tools",
            self.discussion.current_user,
            self.discussion.node_id,
            "discussion_" + self.discussion.discussion_context.discussion_type,
            100,
            ""
        )

    async def capture_discussion_output(
        self,
        modified_node,
        modified_child_nodes=None,
        modified_relationships=None,
        new_child_nodes=None,
        modified_sibling_nodes=None,
        modified_other_nodes=None,
        new_relationships=None,
        new_relationship_types=None,
        reason_for_this_call=None,
    ):
        # modified_node = {
        #     'modified_node': modified_node
        # }
        # old_format_args = self.discussion.convert_function_args_to_old_format(modified_node, self.discussion.discussion_type)
        # modification = await self.get_modifications_from_discussion(old_format_args)

        self.discussion.modifications = {
            "modified_node": modified_node,
            "new_child_nodes": new_child_nodes,
            "modified_children": modified_child_nodes,
            "modified_siblings": modified_sibling_nodes,
            "modified_similar_nodes": modified_other_nodes,
            "new_relationships": new_relationships,
            "modified_relationships": modified_relationships,
            "new_relationship_types": new_relationship_types,
        }

        self.discussion.get_modifications_from_llm_output()
        # print(self.discussion.modifications)
        self.discussion.modifications["created_at"] = datetime.now().isoformat()

        self.discussion.modifications_history.append(self.discussion.modifications)
        await self.discussion.update_discussion_node(
            self.discussion.discussion_id,
            modifications=self.discussion.modifications,
            modifications_history=self.discussion.modifications_history,
        )
        modifications = {"modifications": self.discussion.modifications_history}
        self.discussion.retrieved_info["latest_modifications"] = (
            self.discussion.modifications
        )
        self.discussion.discussion_so_far.append(
            {
                "role": "assistant",
                "created_at": datetime.now().isoformat(),
                "content": (json.dumps(modifications)),
            }
        )

        return {"content": modifications, "skip_last_call": True}

    async def get_modifications_from_discussion(self, function_args):
        self.discussion.modifications = self.get_modifications_from_llm_output(
            self.discussion.node_type,
            self.discussion.root_node_type,
            self.discussion.discussion_type,
            function_args,
        )

        self.discussion.modifications["created_at"] = datetime.now().isoformat()

        self.discussion.modifications_history.append(self.discussion.modifications)

        await self.discussion.update_discussion_node(
            self.discussion.discussion_id,
            modifications=self.discussion.modifications,
            modifications_history=self.discussion.modifications_history,
        )

        return (
            self.discussion.modifications
        )  # Return the current modifications, not the history

    def get_modifications_from_llm_output(
        self, node_type, root_node_type, discussion_type, function_args
    ):
        config = self.current_configuration
        modified_node = function_args.get("modified_node", {})

        self.modifications = {
            "modified_node": modified_node,
            "new_child_nodes": modified_node.get("new_child_nodes", []),
            "modified_children": modified_node.get("modified_child_nodes", []),
            "modified_siblings": modified_node.get("modified_sibling_nodes", []),
            "modified_similar_nodes": modified_node.get("modified_other_nodes", []),
            "new_relationships": modified_node.get("new_relationships", []),
            "modified_relationships": modified_node.get("modified_relationships", []),
        }

        for field in config.get("additional_fields", []):
            self.modifications[field.lower().replace("-", "_")] = modified_node.get(
                field
            )

        return self.modifications
