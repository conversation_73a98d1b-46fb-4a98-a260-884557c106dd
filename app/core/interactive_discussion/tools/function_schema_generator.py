class FunctionSchemaGenerator:  # todo modularize thia
    @staticmethod
    def get_function_schema(discussion_type, discussion_configs, data_model):
        print('discussion_configs---', discussion_configs)
        # Get the configuration for this discussion type
        config_key = f"{discussion_type}"

        if config_key not in discussion_configs:
            raise ValueError(f"Configuration for '{config_key}' not found.")

        config = discussion_configs[config_key]
        node_type = config["modified_node_type"]

        # Get the node info from the data model
        if node_type not in data_model["model"]:
            raise ValueError(f"Node type '{node_type}' not found in data model.")

        node_info = data_model["model"][node_type]
        # Add formatting instructions to schema description
        formatting_instructions = """
        - The modified_node object should only contain direct node properties (Title, Description, etc)
        - Do not nest new_child_nodes or other structural elements inside modified_node
        """
        # Start building the function schema
        function_schema = {
            "type": "function",
            "function": {
                "name": "capture_discussion_output",
                "description": f"Save all consolidated modifications from the current discussion for {discussion_type} of a {node_type} node. This function is called once at the end of a discussion. The parameters should reflect all the changes made during the discussion.Keep all rich text formatting intact if there are any.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "modified_node": {
                            "type": "object",
                            "description": "Specified fields of the current node, with or without any modifications as required by this discussion",
                            "properties": {},
                            "required": config.get("modifiable_fields", []),
                        }
                    },
                    "required": ["modified_node", "reason_for_this_call"],
                },
            },
        }

        if (
            "function" in function_schema
            and "description" in function_schema["function"]
        ):
            function_schema["function"]["description"] = (
                function_schema["function"]["description"]
                + "\n\n"
                + formatting_instructions
            )

        # Populate modified_node schema
        modifiable_fields = config.get("modifiable_fields", [])
        for attr, attr_info in node_info["attributes"].items():
            if not modifiable_fields or attr in modifiable_fields:
                function_schema["function"]["parameters"]["properties"][
                    "modified_node"
                ]["properties"][attr] = {
                    "type": convert_type(attr_info["type"]),
                    "description": attr_info["description"],
                }

                if "items" in attr_info:
                    function_schema["function"]["parameters"]["properties"][
                        "modified_node"
                    ]["properties"][attr]["items"] = attr_info["items"]

        # Handle child node schemas based on configuration
        new_children_types = config.get("new_children_type", [])
        if new_children_types:
            # Always include new_child_nodes for configurations that allow new children
            function_schema["function"]["parameters"]["properties"][
                "new_child_nodes"
            ] = {
                "type": "array",
                "description": "New child nodes created during this discussion. Use this for completely new nodes that don't already exist.",
                "items": {},
            }

            if len(new_children_types) == 1:
                child_type = new_children_types[0]
                new_child_schema = FunctionSchemaGenerator.create_child_schema(
                    child_type, config, data_model, include_id=False
                )
                function_schema["function"]["parameters"]["properties"][
                    "new_child_nodes"
                ]["items"] = new_child_schema
            else:
                new_child_schema = {
                    "anyOf": [
                        FunctionSchemaGenerator.create_child_schema(
                            child_type, config, data_model, include_id=False
                        )
                        for child_type in new_children_types
                    ]
                }
                function_schema["function"]["parameters"]["properties"][
                    "new_child_nodes"
                ]["items"] = new_child_schema

        # Only include modified_child_nodes if explicitly allowed in the configuration
        if config.get("modified_children", False):
            function_schema["function"]["parameters"]["properties"][
                "modified_child_nodes"
            ] = {
                "type": "array",
                "description": "Existing child nodes that were modified during the discussion. Each node MUST include an 'id' field. If no existing children were modified and the 'id' field is not an integer, this array should be empty and create new_child_nodes.",
                "items": {},
            }

            if len(new_children_types) == 1:
                child_type = new_children_types[0]
                modified_child_schema = FunctionSchemaGenerator.create_child_schema(
                    child_type, config, data_model, include_id=True
                )
                function_schema["function"]["parameters"]["properties"][
                    "modified_child_nodes"
                ]["items"] = modified_child_schema
            else:
                modified_child_schema = {
                    "anyOf": [
                        FunctionSchemaGenerator.create_child_schema(
                            child_type, config, data_model, include_id=True
                        )
                        for child_type in new_children_types
                    ]
                }
                function_schema["function"]["parameters"]["properties"][
                    "modified_child_nodes"
                ]["items"] = modified_child_schema

        # Add llm_metadata if present in the data model
        if "llm_metadata" in node_info:
            function_schema["function"]["metadata"] = {
                "llm_guidelines": {
                    "usage_instruction": "Use these guidelines to inform your decisions, but do not include them in your output."
                }
            }
            function_schema["function"]["metadata"]["llm_guidelines"].update(
                node_info["llm_metadata"]
            )

        # Add new_relationships schema if applicable
        if config.get("new_relationship_types"):
            new_relationship_schema = dict(
                type="array",
                description="New relationships created from the discussion.",
                items={},
            )

            if len(config["new_relationship_types"]) == 1:
                # Only one relationship type
                rel_type = config["new_relationship_types"][0]
                relationship_entity = data_model["model"].get(rel_type, {})
                # Build the schema for this relationship type
                relationship_schema = dict(
                    type="object",
                    description=relationship_entity.get("description", ""),
                    properties={},
                    required=[],
                )
                for attr, attr_info in relationship_entity.get(
                    "attributes", {}
                ).items():
                    relationship_schema["properties"][attr] = dict(
                        type=convert_type(attr_info.get("type", "string")),
                        description=attr_info.get(
                            "description", f"Description for {attr}"
                        ),
                    )
                    if attr_info.get("required", False):
                        relationship_schema["required"].append(attr)
                new_relationship_schema["items"] = relationship_schema
            else:
                # Multiple relationship types
                relationship_schemas = []
                for rel_type in config["new_relationship_types"]:
                    relationship_entity = data_model["model"].get(rel_type, {})
                    relationship_schema = dict(
                        type="object",
                        description=relationship_entity.get("description", ""),
                        properties={},
                        required=[],
                    )
                    # Add 'type' property to distinguish relationship types
                    relationship_schema["properties"]["type"] = dict(
                        type="string",
                        enum=[rel_type],
                        description="Type of the relationship",
                    )
                    relationship_schema["required"].append("type")
                    for attr, attr_info in relationship_entity.get(
                        "attributes", {}
                    ).items():
                        relationship_schema["properties"][attr] = dict(
                            type=convert_type(attr_info.get("type", "string")),
                            description=attr_info.get(
                                "description", f"Description for {attr}"
                            ),
                        )
                        if attr_info.get("required", False):
                            relationship_schema["required"].append(attr)
                    relationship_schemas.append(relationship_schema)
                new_relationship_schema["items"] = dict(anyOf=relationship_schemas)

            function_schema["function"]["parameters"]["properties"][
                "new_relationships"
            ] = new_relationship_schema

        if config.get("modified_siblings"):
            function_schema["function"]["parameters"]["properties"][
                "modified_sibling_nodes"
            ] = {
                "type": "array",
                "description": "Sibling nodes modified in the discussion.",
                "items": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "Title": {"type": "string"},
                        "Description": {"type": "string"},
                    },
                },
            }

        if config.get("modified_other_nodes"):
            function_schema["function"]["parameters"]["properties"][
                "modified_other_nodes"
            ] = {
                "type": "array",
                "description": "Other nodes modified in the discussion.",
                "items": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "Title": {"type": "string"},
                        "Description": {"type": "string"},
                    },
                },
            }

        if config.get("modified_relationship_types"):
            function_schema["function"]["parameters"]["properties"][
                "modified_relationships"
            ] = {
                "type": "array",
                "description": "Existing relationships modified in the discussion.",
                "items": {
                    "type": "object",
                    "properties": {
                        "source": {"type": "string"},
                        "target": {"type": "string"},
                        "type": {"type": "string"},
                    },
                },
            }

        function_schema["function"]["parameters"]["properties"][
            "reason_for_this_call"
        ] = {
            "type": "object",
            "description": "Explain the reasons for making this function call.",
            "properties": {
                "reason": {"type": "string"},
            },
        }
        # print("function_schema", function_schema)
        return function_schema

    @staticmethod
    def create_child_schema(child_type, config, data_model, include_id=False):
        child_node_info = data_model["model"][child_type]
        new_child_fields = config.get("new_child_fields", {}).get(child_type, [])

        properties = {}
        if include_id:
            properties["id"] = {
                "type": "string",
                "description": "Unique identifier of the existing child node. This field is required for all modified child nodes.",
            }

        for attr, attr_info in child_node_info["attributes"].items():
            if not new_child_fields or attr in new_child_fields:
                properties[attr] = {
                    "type": convert_type(attr_info["type"]),
                    "description": attr_info["description"],
                }

        schema = {
            "type": "object",
            "properties": properties,
            "required": ["id", "Title"] if include_id else ["Title"],
        }

        return schema


def convert_type(attr_type):
    type_mapping = {
        "string": "string",
        "boolean": "boolean",
        "date": "string",
        "enum": "string",
        "integer": "integer",
        "float": "number",
        "object": "object",
        "array": "array",
    }
    return type_mapping.get(attr_type, "string")
