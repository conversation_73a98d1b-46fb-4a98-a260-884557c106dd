import importlib
import inspect
from ..tools.utils.tools_available import ToolsAvailable
from .base_tool import BaseTool


class ToolRegistry:
    """A registry to keep track of all available tools."""

    def __init__(self):
        self.tools = {}

    def register_tool(self, tool_name, tool_class):
        """Register a new tool."""
        if not issubclass(tool_class, BaseTool):
            raise TypeError(f"{tool_class.__name__} must be a subclass of BaseTool")
        self.tools[tool_name] = tool_class

    def register_tool_list(self, tool_list: list[ToolsAvailable]):
        if any(not issubclass(val.tool, BaseTool) for val in tool_list):
            raise TypeError("All tools must be a subclass of BaseTool")
        for each_tool in tool_list:
            self.tools[each_tool.label] = each_tool.tool

    def get_tool(self, tool_name):
        """Get a tool by name."""
        return self.tools.get(tool_name)

    def list_tools(self):
        """List all registered tools."""
        return list(self.tools.keys())


class DynamicToolFactory:
    """A factory for creating dynamic tool compositions."""

    def __init__(self, tool_registry):
        self.tool_registry = tool_registry

    def create_dynamic_tool(self, tool_names, dynamic_tool_name="DynamicTool"):
        """Create a dynamic tool by combining multiple tools."""
        tool_classes = [
            self.tool_registry.get_tool(name)
            for name in tool_names
            if self.tool_registry.get_tool(name)
        ]

        if not tool_classes:
            raise ValueError("No valid tools specified")

        class DynamicTool(*tool_classes):
            def __init__(
                self, base_path, logger=None, llm=None, user_id=None, discussion=None
            ):
                # Initialize BaseTool
                BaseTool.__init__(
                    self, base_path, logger, llm, user_id=None, discussion=None
                )

                # Initialize all other parent classes
                for cls in self.__class__.__mro__[1:-1]:  # Exclude self and object
                    if cls != BaseTool and hasattr(cls, "__init__"):
                        cls.__init__(self, base_path, logger, llm, user_id, discussion)

        DynamicTool.__name__ = dynamic_tool_name
        return DynamicTool


def load_tools_from_module(module_name, tool_registry):
    """Load all tool classes from a module and register them."""
    module = importlib.import_module(module_name)
    for name, obj in inspect.getmembers(module):
        if inspect.isclass(obj) and issubclass(obj, BaseTool) and obj != BaseTool:
            tool_registry.register_tool(name, obj)
