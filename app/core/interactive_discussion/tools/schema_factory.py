from ..utils.json_handler import JsonHandler
import os


class SchemaFactory():
    @staticmethod
    def get_schema(function_name: str):
        try:
            file_path = f'./schema/{function_name}_schema.json'
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Schema file not found: {file_path}")
            return JsonHandler.load_json_file(file_path)
        except Exception as e:
            raise RuntimeError(f"Failed to load schema for {function_name}: {e}")
