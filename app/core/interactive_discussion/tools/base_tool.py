import asyncio
import inspect
import logging
from collections import deque
import json


class BaseTool:
    """Base class for all tools."""

    function_schemas = []
    function_mapping = {}

    def __init__(self, base_path, logger=None, llm=None, user_id=None, discussion=None):
        self.base_path = base_path
        if logger is None:
            self.logger = logging.getLogger(self.__class__.__name__)
        else:
            self.logger = logger

        self.recent_calls = deque(maxlen=3)
        self.max_repetitions = 3
        self.llm = llm

    def set_parameter(self, name, value):
        """Set a parameter for the tool."""
        setattr(self, name, value)

    def set_parameters(self, **kwargs):
        """Set multiple parameters at once."""
        for name, value in kwargs.items():
            self.set_parameter(name, value)

    async def function_executor(self, function_name, function_args):
        """Execute a function from the function_mapping with loop detection and enhanced logging."""
        # Convert args to a hashable type for comparison
        args_hash = json.dumps(function_args, sort_keys=True)

        # Add the current call to recent calls
        self.recent_calls.append((function_name, args_hash))

        # Check for loop
        if len(self.recent_calls) == self.max_repetitions:
            if all(call == self.recent_calls[0] for call in self.recent_calls):
                # Loop detected
                self.recent_calls.clear()  # Reset the call history
                loop_error = {
                    "status": "ERROR",
                    "message": f"Loop detected: {function_name} called {self.max_repetitions} times with the same arguments. Please use the review_progress_and_plan function to analyze the situation and determine the next steps.",
                    "suggestion": "Call review_progress_and_plan function",
                }
                self.logger.warning(f"Loop detected: {json.dumps(loop_error)}")
                return loop_error

        if function_name in self.function_mapping:
            function = self.function_mapping[function_name]
            self.logger.info(
                f"Executing function: {function_name} with arguments: {json.dumps(function_args)}"
            )
            try:
                # print(f"Result from inspect module : ", inspect.iscoroutinefunction(function))
                if inspect.iscoroutinefunction(function):
                    # Async function
                    result = await function(**function_args)
                else:
                    # Sync function
                    result = await asyncio.to_thread(function, **function_args)

                self.logger.info(
                    f"Function {function_name} executed successfully. Output: {json.dumps(result)}"
                )
                return result
            except TypeError as e:
                error_message = str(e)
                error_result = {
                    "status": "ERROR",
                    "message": f"Invalid argument passed to {function_name}: {error_message}",
                    "error_type": "InvalidArgument",
                    "function_name": function_name,
                    "provided_arguments": function_args,
                }
                self.logger.error(
                    f"TypeError in function execution: {json.dumps(error_result)}"
                )
                return error_result
        else:
            unknown_function_error = {
                "status": "ERROR",
                "message": f"Unknown function: {function_name}",
                "error_type": "UnknownFunction",
                "function_name": function_name,
            }
            self.logger.error(f"Unknown function: {json.dumps(unknown_function_error)}")
            return unknown_function_error
