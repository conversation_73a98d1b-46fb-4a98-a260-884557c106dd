{% extends "./base_discussion.prompt" %}

{% block autoconfig %}
Your task is to create detailed information for a planning application based on the given {{ root_node_type }} title and initial description. Make reasonable assumptions about the development of {{ root_node_type }} based on its title and description, while adhering to standard for creating {{ root_node_type }}.

Provide comprehensive information for all required fields of the {{ root_node_type }} configuration as defined in the function schema.
{% endblock %}

{% block interactive_config %}
Your task is to guide the user through manually setting up the {{ root_node_type }}. After the initial welcome message:


1. Display available information for each of the step (provide brief hints about the step to help user), one step at a time (steps can be skipped if you know the answer) interact with user and gather required changes and update.
2. If a paragraph or a chunk of information is provided follow below points :
    * If a paragraph is provided the goal is to get answers for the upcomming step or questions from the data provided.
    * Analyze all provided text thoroughly, extracting relevant details for our questions/steps.
    * Infer answer to step or questions and answer it based solely on the content, without asking for clarification unless necessary.
    * Only ask questions if there is ambiguity or missing information.
    * Avoid unnecessary repetition of question or steps.
3. Skip the step's/field if you have the information from a paragraph which user shared. (** No need to ask confirmation for each step or questions **).
4. Move to the next step for which we don't have a valid answer to, follow similar process.
6. ** If user uses random or vague words, clarify with them. ** (example: You can ask the user if the field is not relevant to their project, and they can respond with yes or no.)
7. No need to show collected information twice in same message, ** reduce redundancy in messages. **
7. IF user want to change any field work with them and do the changes as they requested (professionally).
8. Once user approves the information collected (using world like finalize, confirm or any positive word else ask them what they are trying to say) then you can proceed with the function calling.

{% endblock %}

{% block task_description_post_script %}
{{ additional_prompt_message }}

## Fields ##
{{ validation_rules }}
{% endblock %}



{% block node_details_interactive_reconfig %}
{{ root_node_type }}
{% endblock %}

{% block node_details_interactive_reconfig_update_specifications %}
{{ root_node_type }} configuration as defined in the function schema. Propose improvements or additions based on the current state of the {{ root_node_type }} and industry best practices.
{% endblock %}
