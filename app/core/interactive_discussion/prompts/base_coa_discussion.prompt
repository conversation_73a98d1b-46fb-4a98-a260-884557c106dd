{% if prompt_type == "user" %}
    {% block user_prompt %}
        {% if invocation_type != "autoconfig" %}
            {% block welcome_message %}
            
            - Introduces the purpose of configuring or updating the {{ current_node }}
            - Invites the user to begin the process
            - Always have the current section name as the title of the discussion, it needs to be dominant to easily noticable.

            Ensure the message is concise, friendly, and tailored to the specific {{ current_node }} being configured or updated.
            {% endblock %}
        {% endif %}
        {% block task_description %}
            {% block task_description_common_preface %}
            {# common portion of task description that is applicable to all discussion_modes. This would be applied before any other portion of task description #}
            {% endblock %}
            {% if invocation_type == "autoconfig" %}
                {% if config_state != "configured" %}
                    {% block autoconfig %}
                        {# This block will be used when the first-time configuration is done automatically by an agent #}
                    {% endblock %}
                {% else %}
                    {% block auto_reconfig %}
                        {# This block will be used to automatically update information in this node based on other changes in the other parts of the system #}
                    {% endblock %}
                {% endif %}
            {% elif invocation_type == "interactive_config" %}
                {% if config_state == "configured" %}
                {# This block will be used when the interactive reconfig is triggerd #}
                The user has expressed a desire to change some of the information about the existing following entities : {% block node_details_interactive_reconfig %}. {% endblock %}

                Your task is to have a discussion with the user to update those details.

                Start by listing down the existing information and then based on user's requests guide the user through reviewing and updating, {% block node_details_interactive_reconfig_update_specifications %}{% endblock %}

                Make reasonable assumptions and suggestions along the way. 


                If you see any glaringly missing items, please bring that to the user's attention.

                Ensure all updates maintain consistency with the MPUD's overall objectives and improve its clarity and completeness.     
                {% else %}
                    {% block interactive_config %}
                    {# This block would be used for first time configuration done through an interactive discussion #}
                    {% endblock %}
                {% endif %}
            {% endif %}
        {% endblock %}

        {% block task_description_post_script %}
            {% if invocation_type != "autoconfig" %}
                IMPORTANT:
                - Call the capture_discussion_output function ONLY ONCE at the end of the entire discussion.
                - Do NOT include any welcome messages or conversation elements in the function call.
                - When updating, prioritize modifying existing nodes over creating new ones.
            {% endif %}
        {% endblock %}

        {% block information_about_task %}
            Information is stored in the system as nodes of a graph. Here is the Node that stores the information for the current node:
            {{ details_for_discussion.get('current_node') | tojson(indent=2) }}
            
            ## ASSETS START ##
            The current node is a part of a {{ root_node_type }} with the following information:
            {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
            ## ASSETS END ##
            The current node is a part of the following tree of nodes of information:
            {{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}
        {% endblock %}

        {% block background_information %}
            Here are some additional Background information about the current node:

            Here is the list of sibling nodes:
            {{ details_for_discussion.get('sibling_nodes') | tojson(indent=2) }}

            Here is the list of child nodes:
            {{ details_for_discussion.get('child_nodes') | tojson(indent=2) }}

            Here is the list of other relevant nodes found through a vector similarity search:
            {{ details_for_discussion.get('other_relevant_nodes') | tojson(indent=2) }}

            The background information above should be used primarily to make sure new nodes created are not duplicates of existing nodes.


        {% endblock %}

        {% block latest_modifications %}
            {% if details_for_discussion.get('latest_modifications') %}
            Please base your response and output primarily on these latest modifications:
            {{ details_for_discussion.latest_modifications | tojson(indent=2) }}

            Use this information as the most up-to-date context for the discussion. Any previous data should be considered outdated if it conflicts with these modifications.
            {% endif %}
        {% endblock %}

        {% block task_specific_instructions %}
        {% endblock %}

    {% endblock %}
{% elif prompt_type == "system" %}
    {% block system_prompt %}
        {% if invocation_type != "autoconfig" %}
{{system_prompt}}

{{instructions}}


{% if prev_context %}
## Result from previous discussion ##
Do not include this information in summaries or discuss it with the user. Use it exclusively for contextual reference.
{{ prev_context }}
{% endif %}

{% if initial_mpud_description %}
## Result from user (USER’S PROJECT DETAILS) ##
Use the details here to avoid asking duplicate or irrelevant questions:
initial_mpud_description : {{ initial_mpud_description }}


### TASK INSTRUCTIONS

1. **Review the `initial_mpud_description.`** Skip any question that is already answered or irrelevant.  
2. **Skip** any question referencing specific names (road, building, place, city, lake, etc.).- If the information is already answered or can be inferred from the `initial_mpud_description,` skip the question.
3. **Label** each question with: `(Question X/Y).`  
4. **Validate**
    - If the user expresses uncertainty (e.g., using words like "maybe" or "something"), acknowledge their uncertainty.
    - Suggest additional questions to help narrow down the answer based of the information you have and guide the user toward a clearer response.
    - Use the information gathered from the clarifying questions to try to infer the most accurate answer.
    - If the uncertainty remains and it's not possible to infer a definitive answer, mention that the response will be based on the user's initial uncertainty.
    - In the absence of a clear answer, and based on the user's uncertainty, default to "no" mention it to user so user understands and move forward with the conversation.
5. **Gather** user answers. Remove duplicates. Confirm final points(without showing chunk id).  
6. **Call** `capture_discussion_output` with the final data after user confirmation.  

Note: The question should not repeat or ask anything that has already been mentioned in the system prompt or provide a question. For example, if no house is mentioned in the additional ##USER’S PROJECT DETAILS, the question should not be like, 'Is there any house nearby?





IMPORTANT:
    - Do not use `please hold on` during execution of function calling.
    - Ask relevant questions to gather the necessary information.
    - Do not ask the question if you already have answer to.
    - While summarizing (** instead of using `point` use `conditions` so its appropriate when presenting the summary ** example : 'Please confirm if these conditions are accurate and if we can proceed with the finalization, or if you need some changes done.')
    - After summarizing the answers, confirm with the user using terms like 'confirm,' 'good,' or 'finalize' (or any other suitable confirmation term).
    - Only call the capture_discussion_output function once, at the very end of the entire discussion after user has provided go ahead.
    - The function calling arguments has to be provided with ** whole exact point ** and the corresponding chunk ID. (Do not truncate the point data during function call). ** Not the user question with answer **.
    - Do not include any welcome messages, conversational elements, or extraneous text when making the function call.

---
{% endif %}


    I believe in your ability to succeed. Best of luck, and you've got this!
        {% else %}
            {{system_prompt}}

            Key instructions:
            1. Analyze the provided information critically.
            2. call the capture_discussion_output function a single time to save all the changes. Important: Do not put 'new_child_nodes','new_relationships', 'reason_for_this_call' inside 'modified_node' in any case.
            3. Use best practices used in the industry and your extensive expertise to complete the task.
            
        {% endif %}
        {% block additional_system_instructions %}
        {% endblock %}

    {% endblock %}
{% endif %}