{% if prompt_type == "system"%}
{{system_prompt}}


{{instructions}}



{% if prev_context %}

    ## Question and answer from previous discussion ##
    Do not include this information in summaries or discuss it with the user. Use it exclusively for contextual reference. 
    *** During the question asking process if the question is already asked use the answer from here and do not repeat the same question to the user.
    
    {{ prev_context | tojson(indent=2) }}
{% endif %}
        
    I believe in your ability to succeed. Best of luck, and you've got this!
{%elif prompt_type == "user" %}

{{additional_prompt_message}}

    Information is stored in the system as nodes of a graph. Here is the Node that stores the information for the current node:
    {
    "labels": [
        "MPUD"
    ],
    "properties": {
        "Type": "MPUD",
        "created_at": "2024-12-13T13:06:10.109796",
        "created_by": "a4f804a8-50f1-707c-a3fb-c42d676e48ea",
        "is_active": true
    }
    }

    The current node is a part of a LAYOUT with the following information:
    {
    "id": 942,
    "labels": [
        "DiscussionRoot"
    ],
    "properties": {
        "id": "0f3d1283-e598-4146-8a58-dfba01f69c7b",
        "step": 2
    }
    }

    The background information above should be used primarily to make sure new nodes created are not duplicates of existing nodes.
{%endif%}