{% if prompt_type == "user" %}
    {% block user_prompt %}
        {% if invocation_type != "autoconfig" %}
            {% block welcome_message %}
            Generate a brief, context-appropriate Welcome message for the user containing following:
            - Greets the user
            - Introduces the purpose of configuring or updating the {{ current_node }}
            - Invites the user to begin the process

            Ensure the message is concise, friendly, and tailored to the specific {{ current_node }} being configured or updated.
            {% endblock %}
        {% endif %}
        {% block task_description %}
            {% block task_description_common_preface %}
            {# common portion of task description that is applicable to all discussion_modes. This would be applied before any other portion of task description #}
            {% endblock %}
            {% if invocation_type == "autoconfig" %}
                {% if config_state != "configured" %}
                    {% block autoconfig %}
                        {# This block will be used when the first-time configuration is done automatically by an agent #}
                    {% endblock %}
                {% else %}
                    {% block auto_reconfig %}
                        {# This block will be used to automatically update information in this node based on other changes in the other parts of the system #}
                    {% endblock %}
                {% endif %}
            {% elif invocation_type == "interactive_config" %}
                {% if config_state == "configured" %}
                {# This block will be used when the interactive reconfig is triggerd #}
                The user has expressed a desire to change some of the information about the existing following entities : {% block node_details_interactive_reconfig %}. {% endblock %}

                Your task is to have a discussion with the user to update those details.

                Start by listing down the existing information and then based on user's requests guide the user through reviewing and updating, {% block node_details_interactive_reconfig_update_specifications %}{% endblock %}

                Make reasonable assumptions and suggestions along the way. 


                If you see any glaringly missing items, please bring that to the user's attention.

                Ensure all updates maintain consistency with the MPUD's overall objectives and improve its clarity and completeness.     
                {% else %}
                    {% block interactive_config %}
                    {# This block would be used for first time configuration done through an interactive discussion #}
                    {% endblock %}
                {% endif %}
            {% endif %}
        {% endblock %}

        {% block task_description_post_script %}
            {% if invocation_type != "autoconfig" %}
                IMPORTANT:
                - Call the capture_discussion_output function ONLY ONCE at the end of the entire discussion.
                - Do NOT include any welcome messages or conversation elements in the function call.
                - When updating, prioritize modifying existing nodes over creating new ones.
            {% endif %}
        {% endblock %}

        {% block information_about_task %}
            Information is stored in the system as nodes of a graph. Here is the Node that stores the information for the current node:
            {{ details_for_discussion.get('current_node') | tojson(indent=2) }}

            ## ASSETS START ##
            The current node is a part of a {{ root_node_type }} with the following information:
            {{ details_for_discussion.get('root_node') | tojson(indent=2) }}
            ## ASSETS END ##

            The current node is a part of the following tree of nodes of information:
            {{ details_for_discussion.get('parent_tree') | tojson(indent=2) }}
        {% endblock %}

        {% block background_information %}
            Here are some additional Background information about the current node:

            Here is the list of sibling nodes:
            {{ details_for_discussion.get('sibling_nodes') | tojson(indent=2) }}

            Here is the list of child nodes:
            {{ details_for_discussion.get('child_nodes') | tojson(indent=2) }}

            Here is the list of other relevant nodes found through a vector similarity search:
            {{ details_for_discussion.get('other_relevant_nodes') | tojson(indent=2) }}

            The background information above should be used primarily to make sure new nodes created are not duplicates of existing nodes.
            
        {% endblock %}

        {% block latest_modifications %}
            {% if details_for_discussion.get('latest_modifications') %}
            Please base your response and output primarily on these latest modifications:
            {{ details_for_discussion.latest_modifications | tojson(indent=2) }}

            Use this information as the most up-to-date context for the discussion. Any previous data should be considered outdated if it conflicts with these modifications.
            {% endif %}
        {% endblock %}

        {% block task_specific_instructions %}
        {% endblock %}

    {% endblock %}
{% elif prompt_type == "system" %}
    {% block system_prompt %}
        {% if invocation_type != "autoconfig" %}
{{system_prompt}}

{{instructions}}


{% if prev_context %}
## Result from previous discussion ##
Do not include this information in summaries or discuss it with the user. Use it exclusively for contextual reference.
{{ prev_context }}
{% endif %}


{% if initial_mpud_description %}
### Initial Response from user on the MPUD construction details ##

*** `initial_mpud_description` ***:

{{ initial_mpud_description }}

{% endif %}
    

    I believe in your ability to succeed. Best of luck, and you've got this!
        {% else %}
            {{system_prompt}}

            Key instructions:
            1. Analyze the provided information critically.
            2. call the capture_discussion_output function a single time to save all the changes. Important: Do not put 'new_child_nodes','new_relationships', 'reason_for_this_call' inside 'modified_node' in any case.
            3. Use best practices used in the industry and your extensive expertise to complete the task.
            4. If the output contains a list of items , list down the items one after the other with proper indentation every time.

        {% endif %}
        {% block additional_system_instructions %}
        {% endblock %}

    {% endblock %}
{% endif %}