import json
import time
from app.crud.neo4j.neo4j_tool import Neo4jTool
from app.core.embedding import VoyageAiEmbedding


class COAManager:
    def __init__(self, neo4j_tool: Neo4jTool, voyage_embedding: VoyageAiEmbedding):
        """
        Initialize COAManager with Neo4j and Voyage AI embedding tools.

        Args:
            neo4j_tool (Neo4jTool): Instance of Neo4jTool for database operations.
            voyage_embedding (VoyageAiEmbedding): Instance of VoyageAiEmbedding for generating embeddings.
        """
        try:
            self.neo4j = neo4j_tool
            self.vo = voyage_embedding
            print("COAManager initialized successfully.")
        except Exception as e:
            print(f"Failed to initialize COAManager: {str(e)}")
            raise

    def load_data(self, data, filename, uploaded_timestamp=None):
        if uploaded_timestamp is None:
            uploaded_timestamp = time.time()

        def process_section(parent_label, parent_name, key, value):
            print("Loading... ", parent_label, parent_name)
            section_name = str(key)
            self.neo4j.upsert_section(parent_label, parent_name, section_name)

            if isinstance(value, list):
                if value and isinstance(value[0], dict):
                    conditions = []
                    metadata = []
                    for item in value:
                        conditions.append(item["text"].strip())
                        metadata.append({
                            "chunk_id": item.get("chunk_id"),
                            "coordinates": json.dumps(item.get("coordinates")) if item.get("coordinates") else None,
                            "page": item.get("page")
                        })
                else:
                    conditions = [pt.strip() for pt in value]
                    metadata = [{"chunk_id": None, "coordinates": None, "page": None} for _ in conditions]

                embeddings = self.vo.embed_documents(conditions)

                for condition, embedding, meta in zip(conditions, embeddings, metadata):
                    self.neo4j.upsert_point(
                        section_name, condition, filename, embedding,
                        chunk_id=meta["chunk_id"],
                        coordinates=meta["coordinates"],
                        page=meta["page"],
                        uploaded_timestamp=uploaded_timestamp
                    )

            elif isinstance(value, dict):
                for subkey, subval in value.items():
                    process_section("Section", section_name, subkey, subval)

        self.neo4j.upsert_file_node(filename, uploaded_timestamp)
        for key, val in data.items():
            process_section("File", filename, key, val)
        print(f"Loaded {filename}")

    def find_similar_points(self, section_name, similarity_threshold=0.9):
        """
        Finds similar points within a given section based on cosine similarity.

        Args:
            section_name (str): The name of the section to query.
            similarity_threshold (float): The similarity threshold (0.0-1.0).

        Returns:
            list: A list of dictionaries containing similar conditions, sources, and average similarity.
        """
        results = self.neo4j.find_similar_points(section_name, similarity_threshold)

        # Transform the results into the expected format
        formatted_results = []
        for result in results:
            formatted_results.append({
                "similar_texts": result["similar_conditions"],  # Update key to match Neo4jTool
                "sources": result["sources"],  # Keep this key as is
                "uploaded_timestamps": result["uploaded_timestamps"],  # Add uploaded_timestamps
                "avg_similarity": result["avg_similarity"]  # Keep this key as is
            })

        return formatted_results

    def get_sections(self):
        """
        Retrieves all sections from the Neo4j database.

        Returns:
            list: A list of section names.
        """
        return self.neo4j.get_sections()

    def close(self):
        """
        Closes the Neo4j driver connection.
        """
        self.neo4j.close()
