import boto3
import json
import logging
from openai import OpenAI
from typing import Optional
import voyageai

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BedrockEmbeddings:
    # [Previous BedrockEmbeddings class remains unchanged]
    def __init__(
        self,
        model_id: str = "amazon.titan-embed-text-v1",
        region_name: str = "us-east-1",
        max_tokens: int = 8000,
        aws_access_key_id: str = None,
        aws_secret_access_key: str = None,
        aws_session_token: str = None,
    ):
        """
        Initialize Bedrock embeddings client.

        Args:
            model_id: The model ID to use for embeddings
            region_name: AWS region name
            max_tokens: Maximum tokens per API call
        """
        try:
            self.model_id = model_id
            self.max_tokens = max_tokens

            # Initialize Bedrock client with optional credentials
            client_kwargs = {
                "service_name": "bedrock-runtime",
                "region_name": region_name,
            }

            # Add credentials if provided
            if aws_access_key_id and aws_secret_access_key:
                client_kwargs.update({
                    "aws_access_key_id": aws_access_key_id,
                    "aws_secret_access_key": aws_secret_access_key,
                })
                if aws_session_token:
                    client_kwargs["aws_session_token"] = aws_session_token

            self.bedrock = boto3.client(**client_kwargs)
            logger.info(f"Initialized Bedrock client with model {model_id}")

        except Exception as e:
            logger.error(f"Failed to initialize Bedrock client: {str(e)}")
            raise

    def embed_text(self, text: str):
        """
        Generate embeddings for a single text input.

        Args:
            text: Input text to embed

        Returns:
            List of embedding values
        """
        try:
            if not text or not isinstance(text, str):
                raise ValueError("Input text must be a non-empty string")

            # Prepare the request body
            request_body = {
                "inputText": text.strip()
            }

            body = json.dumps(request_body)

            # Call Bedrock API
            response = self.bedrock.invoke_model(
                modelId=self.model_id,
                body=body,
                accept="application/json",
                contentType="application/json"
            )

            # Parse response
            response_body = json.loads(response.get("body").read())
            embedding = response_body.get("embedding")

            if not embedding:
                raise ValueError("No embedding found in response")

            return embedding

        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            raise

    def embed_documents(self, documents):
        """
        Generate embeddings for a list of documents.

        Args:
            documents: List of documents to embed

        Returns:
            List of embedding vectors
        """
        try:
            if not documents:
                raise ValueError("Documents list cannot be empty")

            embeddings = []
            for i, doc in enumerate(documents):
                try:
                    embedding = self.embed_text(doc)
                    embeddings.append(embedding)
                    logger.debug(f"Generated embedding for document {i + 1}/{len(documents)}")
                except Exception as e:
                    logger.error(f"Failed to embed document {i + 1}: {str(e)}")
                    raise

            return embeddings

        except Exception as e:
            logger.error(f"Error in batch embedding: {str(e)}")
            raise

    def embed_query(self, query: str):
        """
        Generate embeddings for a query string.

        Args:
            query: Query text to embed

        Returns:
            Query embedding vector
        """
        return self.embed_text(query)


class OpenAIEmbeddings:
    def __init__(
        self,
        api_key: str,
        model: str = "text-embedding-3-small",
        organization: Optional[str] = None,
        max_tokens: int = 8191,
    ):
        """
        Initialize OpenAI embeddings client.

        Args:
            api_key: OpenAI API key
            model: Model ID to use for embeddings (default: text-embedding-3-small)
            organization: Optional organization ID
            max_tokens: Maximum tokens per API call
        """
        try:
            self.model = model
            self.max_tokens = max_tokens

            # Initialize OpenAI client
            client_kwargs = {
                "api_key": api_key,
            }
            if organization:
                client_kwargs["organization"] = organization

            self.client = OpenAI(**client_kwargs)
            logger.info(f"Initialized OpenAI client with model {model}")

        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {str(e)}")
            raise

    def embed_text(self, text: str):
        """
        Generate embeddings for a single text input.
        Args:
            text: Input text to embed

        Returns:
            List of embedding values
        """
        try:
            if not text or not isinstance(text, str):
                raise ValueError("Input text must be a non-empty string")

            response = self.client.embeddings.create(
                model=self.model,
                input=text.strip(),
                encoding_format="float"
            )

            embedding = response.data[0].embedding
            return embedding

        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            raise

    def embed_documents(self, documents):
        """
        Generate embeddings for a list of documents.

        Args:
            documents: List of documents to embed

        Returns:
            List of embedding vectors
        """
        try:
            if not documents:
                raise ValueError("Documents list cannot be empty")

            # OpenAI supports batch processing
            response = self.client.embeddings.create(
                model=self.model,
                input=[doc.strip() for doc in documents],
                encoding_format="float"
            )

            embeddings = [data.embedding for data in response.data]
            return embeddings

        except Exception as e:
            logger.error(f"Error in batch embedding: {str(e)}")
            raise

    def process_in_batches(self, documents, batch_size: int = 8):
        """
        Process documents in batches to generate embeddings using OpenAI's API.

        Args:
            documents: List of documents to embed
            batch_size: Number of documents to process per batch

        Returns:
            List of embedding vectors
        """
        try:
            if not documents:
                raise ValueError("Documents list cannot be empty")

            all_embeddings = []
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                response = self.client.embeddings.create(
                    model=self.model,
                    input=[doc.strip() for doc in batch],
                    encoding_format="float"
                )
                batch_embeddings = [data.embedding for data in response.data]
                all_embeddings.extend(batch_embeddings)
                logger.info(f"Processed batch {i // batch_size + 1}/{(len(documents) + batch_size - 1) // batch_size}")

            return all_embeddings

        except Exception as e:
            logger.error(f"Error in batch processing: {str(e)}")
            raise

    def embed_query(self, query: str):
        """
        Generate embeddings for a query string.

        Args:
            query: Query text to embed

        Returns:
            Query embedding vector
        """
        return self.embed_text(query)


class VoyageAiEmbedding:
    def __init__(self, api_key: str = None, model: str = "voyage-3-large"):
        """
        Initialize Voyage AI embedding client.

        Args:
            api_key (str): Voyage AI API key.
            model (str): Model ID to use for embeddings (default: "voyage-3-large").
        """
        try:
            self.client = voyageai.Client(api_key=api_key)
            self.model = model
            print("Voyage AI client initialized successfully.")
        except Exception as e:
            print(f"Failed to initialize Voyage AI client: {str(e)}")
            raise

    def embed_text(self, text: str):
        """
        Generate embeddings for a single text input.

        Args:
            text (str): Input text to embed.

        Returns:
            list: List of embedding values.
        """
        try:
            if not text or not isinstance(text, str):
                raise ValueError("Input text must be a non-empty string")

            response = self.client.embed([text], model=self.model, input_type="document")
            return response.embeddings[0]
        except Exception as e:
            print(f"Error generating embedding: {str(e)}")
            raise

    def embed_documents(self, documents: list):
        """
        Generate embeddings for a list of documents.

        Args:
            documents (list): List of documents to embed.

        Returns:
            list: List of embedding vectors.
        """
        try:
            if not documents:
                raise ValueError("Documents list cannot be empty")

            response = self.client.embed(documents, model=self.model, input_type="document")
            return response.embeddings
        except Exception as e:
            print(f"Error in batch embedding: {str(e)}")
            raise


def main():
    """Example usage of both BedrockEmbeddings and OpenAIEmbeddings."""
    try:
        # Initialize Bedrock embeddings client
        bedrock_embeddings = BedrockEmbeddings(
            model_id="amazon.titan-embed-text-v1",
            region_name="us-east-1",
            aws_access_key_id="YOUR_AWS_ACCESS_KEY",
            aws_secret_access_key="YOUR_AWS_SECRET_KEY"
        )

        # Initialize OpenAI embeddings client
        openai_embeddings = OpenAIEmbeddings(
            api_key="YOUR_OPENAI_API_KEY",
            model="text-embedding-3-small"
        )

        # Example documents
        documents = [
            "The quick brown fox jumps over the lazy dog",
            "Pack my box with five dozen liquor jugs",
            "How vexingly quick daft zebras jump"
        ]

        # Generate embeddings using both services
        bedrock_doc_embeddings = bedrock_embeddings.embed_documents(documents)
        openai_doc_embeddings = openai_embeddings.embed_documents(documents)

        logger.info(f"Generated Bedrock embeddings for {len(documents)} documents")
        logger.info(f"Bedrock embedding dimension: {len(bedrock_doc_embeddings[0])}")

        logger.info(f"Generated OpenAI embeddings for {len(documents)} documents")
        logger.info(f"OpenAI embedding dimension: {len(openai_doc_embeddings[0])}")

        # Generate embeddings for a query using both services
        query = "jumping animals"
        bedrock_query_embedding = bedrock_embeddings.embed_query(query)
        openai_query_embedding = openai_embeddings.embed_query(query)

        logger.info("Generated query embeddings with dimensions:")
        logger.info(f"Bedrock: {len(bedrock_query_embedding)}")
        logger.info(f"OpenAI: {len(openai_query_embedding)}")

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        raise


if __name__ == "__main__":
    main()
