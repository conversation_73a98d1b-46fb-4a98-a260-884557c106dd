import asyncio
import functools
from typing import Optional, Dict, Any, Callable
from opentelemetry import trace


def traced(name: Optional[str] = None, attributes: Optional[Dict[str, Any]] = None) -> Callable:
    """
    Decorator to add OpenTelemetry tracing to a function.

    Args:
        name: Optional name for the span. If not provided, uses function name
        attributes: Optional dictionary of attributes to add to the span
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            tracer = trace.get_tracer(__name__)
            span_name = name if name else func.__name__

            with tracer.start_as_current_span(span_name) as span:
                if attributes:
                    span.set_attributes(attributes)
                result = await func(*args, **kwargs)
                return result

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            tracer = trace.get_tracer(__name__)
            span_name = name if name else func.__name__

            with tracer.start_as_current_span(span_name) as span:
                if attributes:
                    span.set_attributes(attributes)
                result = func(*args, **kwargs)
                return result

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator
