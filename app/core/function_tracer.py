import sys
import time
import uuid
import inspect
from pydantic import BaseModel, <PERSON>
from typing import Any, List
import contextvars
import asyncio

# Add at the top of the file with other imports
trace_context = contextvars.ContextVar('trace_session', default=None)


class Value(BaseModel):
    datatype: str = Field(default='')
    value: Any = Field(default=None)


class Step(BaseModel):
    id: str = Field(default='')
    name: str = Field(default='')
    message: str = Field(default='')
    timestamp: str = Field(default='')


class Steps(BaseModel):
    steps: List[Step] = Field(default=[])


class Function(BaseModel):
    id: str = Field(default='')
    name: str = Field(default='')
    parameters: List[Value] = Field(default=[])
    output: Value = Field(default=Value())
    timestamp: Any = Field(default=None)
    status: str = Field(default='initialized')


class FunctionTracer:
    def __init__(self, session_id: str = None):
        self.session_id = session_id if session_id else str(uuid.uuid4())
        self.functions: List[Function] = []
        self.check_functions: list[str] = []
        self.stop_functions: list[str] = []
        self.is_end = False
        # Set this instance in the context
        self.token = trace_context.set(self)

    def __exit__(self, *args):
        # Reset the context when done
        trace_context.reset(self.token)

    async def set_check_functions(self, function_name, kargs={}, check_functions=[], stop_functions=[]):
        try:
            self.check_functions = check_functions
            self.stop_functions = stop_functions
            sys.settrace(self._trace_function)
            if inspect.iscoroutinefunction(function_name):
                result = await function_name(**kargs)
            else:
                result = function_name(**kargs)
            return result
        except Exception as e:
            # Capture the exception and set the status to "error"
            self.functions.append(Function(
                name=function_name.__name__,
                status="error",
                output=Step(value=str(e)),
                parameters=[Step(value=kargs[key]) for key in kargs],
            ))
            raise
        finally:
            self.stop_trace()
            self.is_end = True

    def start_trace(self, function_name, kargs={}, check_functions=[], stop_functions=[]):
        return asyncio.create_task(
            self.set_check_functions(
                function_name=function_name,
                kargs=kargs,
                check_functions=check_functions,
                stop_functions=stop_functions,
            ),
        )

    def stop_trace(self):
        # Disable tracing
        sys.settrace(None)

    def _trace_function(self, frame, event, arg):
        if frame.f_code.co_name not in self.check_functions:
            return self._trace_function

        if event == 'call':
            # Create a new function record when the function is called
            function = Function()
            function.id = self.session_id
            function.name = frame.f_code.co_name
            function.timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
            function.status = 'initialized'
            function_obj = frame.f_globals.get(function.name)
            if function_obj:
                signature = inspect.signature(function_obj)
                for param_name, param in signature.parameters.items():
                    value = Value()
                    value.datatype = param.annotation.__name__ if param.annotation != inspect.Parameter.empty else "unknown"
                    value.value = frame.f_locals.get(param_name, "unknown")
                    function.parameters.append(value)
            self.functions.append(function)

        elif event == 'return':
            # Update the existing function record with return value
            current_function = next(
                (f for f in reversed(self.functions)
                 if f.name == frame.f_code.co_name and f.status != 'returned'),
                None,
            )
            if current_function:
                current_function.output.datatype = type(arg).__name__
                current_function.output.value = arg
                # Check if the return value is a Future
                if isinstance(arg, asyncio.Future):
                    current_function.status = 'pending'
                else:
                    current_function.status = 'returned'
                current_function.timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())

        elif event == 'exception':
            # Handle exceptions
            current_function = next(
                (f for f in reversed(self.functions) if f.name == frame.f_code.co_name and f.status != 'error'),
                None,
            )
            if current_function:
                current_function.status = 'error'
                current_function.output.datatype = 'exception'
                current_function.output.value = str(arg[1])
                current_function.timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())

        if frame.f_code.co_name in self.stop_functions and event == 'return':
            self.is_end = True
            self.stop_trace()
            return None

        return self._trace_function
