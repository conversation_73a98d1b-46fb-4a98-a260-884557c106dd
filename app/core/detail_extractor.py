import openai
import json
from app.core.config import settings

openai.api_key = settings.OPENAI_API_KEY


def calculate_tokens(text, method='simple'):
    """
    Calculate the approximate number of tokens in a given text.

    Args:
        text (str): The input text to analyze
        method (str): The counting method to use
            'simple': Rough estimation based on words and punctuation
            'detailed': More detailed estimation including special characters

    Returns:
        dict: Token count statistics including:
            - total_tokens: Estimated total tokens
            - word_tokens: Number of word tokens
            - punctuation_tokens: Number of punctuation tokens
            - whitespace_tokens: Number of whitespace tokens
            - special_tokens: Number of special tokens (detailed mode only)
    """
    if not text:
        return {
            'total_tokens': 0,
            'word_tokens': 0,
            'punctuation_tokens': 0,
            'whitespace_tokens': 0,
            'special_tokens': 0
        }

    import re

    # Basic token patterns
    word_pattern = r'\b\w+\b'
    punctuation_pattern = r'[.,!?;:"\'\(\)\[\]\{\}]'
    whitespace_pattern = r'\s+'
    special_pattern = r'[^a-zA-Z0-9\s.,!?;:"\'\(\)\[\]\{\}]'

    # Count tokens
    words = len(re.findall(word_pattern, text))
    punctuation = len(re.findall(punctuation_pattern, text))
    whitespace = len(re.findall(whitespace_pattern, text))

    if method == 'detailed':
        special = len(re.findall(special_pattern, text))
        total = words + punctuation + whitespace + special
    else:
        special = 0
        total = words + punctuation + whitespace

    return {
        'total_tokens': total,
        'word_tokens': words,
        'punctuation_tokens': punctuation,
        'whitespace_tokens': whitespace,
        'special_tokens': special
    }


def estimate_tokens(text):
    """
    Estimate the number of tokens in text.
    This is a simple estimation - actual GPT tokens may vary.
    """
    import re
    # Count words
    words = len(re.findall(r'\b\w+\b', text))
    # Count punctuation and special characters
    other = len(re.findall(r'[^\w\s]', text))
    # Count whitespace
    whitespace = len(re.findall(r'\s+', text))
    return words + other + whitespace


def convert_to_markdown(data, token_limit=None):
    """
    Convert nested JSON/dict structure to markdown formatted text.
    Stops when token limit is reached.

    Args:
        data: Dict or list containing the structured data
        token_limit: Maximum number of tokens (optional)
    Returns:
        str: Markdown formatted text
    """
    def count_tokens(text):
        """Simple token counter"""
        import re
        words = len(re.findall(r'\b\w+\b', text))
        others = len(re.findall(r'[^\w\s]', text))
        spaces = len(re.findall(r'\s+', text))
        return words + others + spaces

    def recursive_convert(data, level=1, current_tokens=0):
        """Recursive helper function"""
        output = []
        tokens = current_tokens

        if isinstance(data, dict):
            for key, value in data.items():
                # Add header
                header = f"{'#' * level} {key}\n"
                new_tokens = tokens + count_tokens(header)

                if token_limit and new_tokens > token_limit:
                    return "\n".join(output), tokens, True

                output.append(header)
                tokens = new_tokens

                if isinstance(value, (dict, list)):
                    content, new_tokens, limit_hit = recursive_convert(value, level + 1, tokens)
                    output.append(content)
                    tokens = new_tokens
                    if limit_hit:
                        return "\n".join(output), tokens, True
                else:
                    bullet = f"* {value}\n"
                    new_tokens = tokens + count_tokens(bullet)
                    if token_limit and new_tokens > token_limit:
                        return "\n".join(output), tokens, True
                    output.append(bullet)
                    tokens = new_tokens

        elif isinstance(data, list):
            for item in data:
                if isinstance(item, (dict, list)):
                    content, new_tokens, limit_hit = recursive_convert(item, level, tokens)
                    output.append(content)
                    tokens = new_tokens
                    if limit_hit:
                        return "\n".join(output), tokens, True
                else:
                    bullet = f"* {item}\n"
                    new_tokens = tokens + count_tokens(bullet)
                    if token_limit and new_tokens > token_limit:
                        return "\n".join(output), tokens, True
                    output.append(bullet)
                    tokens = new_tokens

        return "\n".join(output), tokens, False

    # Call recursive helper and return just the text
    text, _, _ = recursive_convert(data)
    return text


def extract_first_page(layout_data):
    """
    Extracts and formats content from the first page of layout data.

    Args:
        layout_data (list): List of dictionaries containing document layout information

    Returns:
        str: Formatted string containing first page content
    """
    # Initialize variables
    page_content = []
    current_section = None

    # Process each block in the layout data
    for block in layout_data:
        # Check if block is from page 1
        if block.get('Page', 0) != 1:
            continue

        # Get the block type and text
        block_type = next((key for key in block.keys() if key.startswith('LAYOUT_')), None)
        if not block_type or 'Geometry' in block_type:
            continue

        text = block.get(block_type, '').strip()
        if not text:
            continue

        # Format based on block type
        if block_type == 'LAYOUT_TITLE':
            page_content.append(f"\n# {text}")
        elif block_type == 'LAYOUT_FIGURE':
            page_content.append(f"\n{text}")
        elif block_type == 'LAYOUT_SECTION_HEADER':
            current_section = text
            page_content.append(f"\n## {text}")
        elif block_type == 'LAYOUT_TEXT':
            if current_section:
                page_content.append(text)
            else:
                page_content.append(text)
        elif block_type == 'LAYOUT_FOOTER':
            page_content.append(f"\n---\n{text}")
        elif block_type == 'LAYOUT_PAGE_NUMBER':
            page_content.append(text)

    # Join all content with proper spacing
    formatted_content = '\n'.join(page_content)

    # Remove any excessive blank lines
    formatted_content = '\n'.join(line for line in formatted_content.splitlines() if line.strip())

    return formatted_content


def analyze_mpud_document(document_text):
    """
    Processes Master Plan Development Documents to extract structured planning information
    Returns JSON with snake_case field names
    """
    SYSTEM_PROMPT = """Analyze the development document and return EXACTLY this JSON structure:
    {
        "mpud_category": "[exactly one: non-substantial-modification|substantial-modification|zoning-amendment|land-use-equivalency-request]",
        "project_type": "[e.g., residential, commercial, mixed-use]",
        "proposed_density_intensity": "[quantitative metrics]",
        "location_access": "[transportation context]",
        "unique_site_characteristics": "[environmental/physical features]",
        "design_elements": "[architectural/planning features]",
        "integration_surroundings": "[compatibility analysis]",
        "phasing_considerations": "[construction staging or N/A]",
        "environmental_considerations": "[impacts + mitigation]"
    }

    Rules:
    1. Use snake_case field names exactly as shown
    2. mpud_category must match exactly one specified option
    3. Use 'N/A' for missing information
    4. Maintain professional urban planning terms"""

    try:
        response = openai.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": document_text}
            ],
            response_format={"type": "json_object"},
            temperature=0.0,
            max_tokens=500
        )

        result = json.loads(response.choices[0].message.content)

        # Validation and fallback values
        required_fields = {
            "mpud_category": ["non-substantial-modification", "substantial-modification",
                              "zoning-amendment", "land-use-equivalency-request"],
            "project_type": "N/A",
            "proposed_density_intensity": "N/A",
            "location_access": "N/A",
            "unique_site_characteristics": "N/A",
            "design_elements": "N/A",
            "integration_surroundings": "N/A",
            "phasing_considerations": "N/A",
            "environmental_considerations": "N/A"
        }

        # Ensure all fields are present with valid values
        output = {field: result.get(field, default) if field != "mpud_category"
                  else result.get(field, "N/A")
                  for field, default in required_fields.items()}

        # Validate mpud_category
        if output["mpud_category"] not in required_fields["mpud_category"]:
            output["mpud_category"] = "N/A"

        return output

    except json.JSONDecodeError:
        return {"error": "Failed to parse API response"}
    except Exception as e:
        return {"error": str(e)}

