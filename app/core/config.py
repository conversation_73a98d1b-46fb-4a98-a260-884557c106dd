from pydantic_settings import BaseSettings
from pydantic import Field
import os


class Settings(BaseSettings):
    PROJECT_NAME: str = Field(
        env="PROJECT_NAME", default="Connector", description="The name of the project",
    )
    PROJECT_VERSION: str = Field(
        env="PROJECT_VERSION", default="0.0.1", description="The version of the project",
    )
    PROJECT_DESCRIPTION: str = Field(
        env="PROJECT_DESCRIPTION", default="Connector with FastAPI with MongoDB stack",
    )
    MONGO_URL: str = Field(
        env="MONGO_URL",
        default="mongodb://mongo:27017/",
        description="The url of the MongoDB",
    )
    MONGO_USER: str = Field(
        env="MONGO_USER", default="admin", description="The MongoDB user",
    )
    MONGO_PASSWORD: str = Field(
        env="MONGO_PASSWORD", default="password", description="The MongoDB password",
    )
    MONG<PERSON>_DEFAULT_DATABASE: str = Field(
        env="MONGO_DEFAULT_DATABASE",
        default="dummy",
        description="The default mongo db",
    )
    MONGO_DATABASE_LIGHTRAG: str = Field(
        env="MONGO_DATABASE",
        default="lightrag",
        description="The default lightrag mongodb",
    )
    MONGO_CONFIG_DATABASE: str = Field(
        env="MONGO_CONFIG_DATABASE",
        default="dummyConfig",
        description="The default mongo config db",
    )
    MONGO_CHAT_COLLECTION: str = Field(
        env="MONGO_CHAT_COLLECTION",
        default="chat_history",
        description="Mongo collection to store chat history",
    )
    MILVUS_URI: str = Field(
        env="MILVUS_URI",
        default="http://standalone:19530",
        description="The url of the Milvus",
    )
    MILVUS_TOKEN: str = Field(
        env="MILVUS_TOKEN",
        default="",
        description="The token of the Milvus",
    )
    MILVUS_DEFAULT_DATABASE: str = Field(
        env="MILVUS_DEFAULT_DATABASE",
        default="dummy",
        description="The default milvus db",
    )
    OPENAI_BASE: str = Field(
        env="OPENAI_BASE",
        default="http://localhost:11434/v1",
        description="The base URL for OpenAI API",
    )
    AWS_ACCESS_KEY_ID_S3_Textract: str = Field(env="AWS_ACCESS_KEY_ID_S3_Textract", default="dummy")
    AWS_SECRET_ACCESS_KEY_S3_Textract: str = Field(env="AWS_SECRET_ACCESS_KEY_S3_Textract", default="dummy")
    AWS_ACCESS_KEY_ID: str = Field(env="AWS_ACCESS_KEY_ID", default="dummy")
    AWS_SECRET_ACCESS_KEY: str = Field(env="AWS_SECRET_ACCESS_KEY", default="dummy")
    AWS_ACCOUNT_ID: str = Field(env="AWS_ACCOUNT_ID", default="dummy")
    AWS_USER_POOL_ID: str = Field(env="AWS_USER_POOL_ID", default="us-east-1_GJSW3ALy7")
    AWS_COGNITO_USER_POOL_ID: str = Field(env="AWS_USER_POOL_ID", default="dummy")
    AWS_REGION: str = Field(env="AWS_REGION", default="dummy")
    STAGE: str = Field(env="STAGE", default="dev")
    AWS_COGNITO_APP_CLIENT_ID: str = Field(
        env="AWS_COGNITO_APP_CLIENT_ID", default="dummy",
    )
    OPENAI_API_KEY: str = Field(
        env="OPENAI_API_KEY",
        default="sk-proj-UKh37MbwZK",
        description="The API key for OpenAI",
    )
    VOYAGE_API_KEY: str = Field(
        env="VOYAGE_API_KEY",
        default='',
        description="The API Key for VOYAGE_API_KEY"
    )
    ANTHROPIC_API_KEY: str = Field(
        env="ANTHROPIC_API_KEY",
        default='',
        description="Anthropic Api Key"
    )
    MILVUS_SEARCH_RESULTS_LIMIT: int = Field(
        env="MILVUS_SEARCH_RESULTS_LIMIT",
        default=5,
        description="Number of search results by Milvus must be limited to this.",
    )
    HISTORY_FETCH_LIMIT: int = Field(
        env="HISTORY_FETCH_LIMIT",
        default=100,
        description="Number of conversations from history to forward to LLM",
    )
    NO_PAGE_FOR_PARAM_EXTRACT: int = Field(
        env="NO_PAGE_FOR_PARAM_EXTRACT",
        default=2,
        description="Number of pages to extract metadata params from",
    )
    NO_SECTIONS_PARAM_EXTRACT_PAGE: int = Field(
        env="NO_SECTIONS_PARAM_EXTRACT_PAGE",
        default=0,
        description="Number of sections to divide the pages to, for metadata extraction",
    )
    NEO4J_URI: str = Field(
        env="NEO4J_URI",
        default="bolt://localhost:7687",
        description="The url of the neo4j db",
    )
    NEO4J_USERNAME: str = Field(
        env="NEO4J_USERNAME",
        default="neo4j",
        description="The username of the neo4j db",
    )
    NEO4J_PASSWORD: str = Field(
        env="NEO4J_PASSWORD",
        default="password",
        description="The password of the neo4j db",
    )
    APP_SYNC_API_KEY: str = Field(
        env="APP_SYNC_API_KEY",
        default="dummy",
        description="The API key for AppSync",
    )
    APP_SYNC_HTTP_DOMAIN: str = Field(
        env="APP_SYNC_HTTP_DOMAIN",
        default="dummy",
        description="The HTTP domain for AppSync",
    )
    OTEL_SERVICE_NAME: str = Field(
        env="OTEL_SERVICE_NAME",
        default="FastAPI-connector",
        description="The service name for OpenTelemetry",
    )
    OTEL_EXPORTER_OTLP_ENDPOINT: str = Field(
        env="OTEL_EXPORTER_OTLP_ENDPOINT",
        default="http://localhost:4317",
        description="The OTLP endpoint for OpenTelemetry",
    )
    OTEL_PYTHON_LOG_CORRELATION: bool = Field(
        env="OTEL_PYTHON_LOG_CORRELATION",
        default=True,
        description="Enable log correlation for OpenTelemetry",
    )
    OTEL_TRACES_EXPORTER: str = Field(
        env="OTEL_TRACES_EXPORTER",
        default="otlp",
        description="The traces exporter for OpenTelemetry",
    )
    OTEL_METRICS_EXPORTER: str = Field(
        env="OTEL_METRICS_EXPORTER",
        default="otlp",
        description="The metrics exporter for OpenTelemetry",
    )
    OTEL_LOGS_EXPORTER: str = Field(
        env="OTEL_LOGS_EXPORTER",
        default="otlp",
        description="The logs exporter for OpenTelemetry",
    )
    INSTANCE_ID: str = Field(
        env="INSTANCE_ID",
        default="instance-1",
        description="The instance ID of the application",
    )

    BEDROCK_LLM_MODEL: str = Field(
        env="BEDROCK_LLM_MODEL",
        default="us.meta.llama3-3-70b-instruct-v1:0",
        description="The LLM model for Bedrock",
    )

    BEDROCK_EMBEDDING_MODEL: str = Field(
        env="BEDROCK_EMBEDDING_MODEL",
        default="us.meta.llama3-3-70b-instruct-v1:0",
        description="The embedding model for Bedrock",
    )
    BEDROCK_ACCESS_KEY_ID: str = Field(
        env="BEDROCK_ACCESS_KEY_ID",
        default="dummy",
        description="The access key ID for Bedrock",
    )
    BEDROCK_SECRET_ACCESS_KEY: str = Field(
        env="BEDROCK_SECRET_ACCESS_KEY",
        default="dummy",
        description="The secret access key for Bedrock",
    )

    # Accela API Configuration
    ACCELA_APP_ID: str = Field(
        env="ACCELA_APP_ID",
        default="638859388421435942",
        description="Accela application ID",
    )
    ACCELA_APP_SECRET: str = Field(
        env="ACCELA_APP_SECRET",
        default="1d48643e5ba248cc8ee9d2760b68757e",
        description="Accela application secret",
    )
    ACCELA_ENVIRONMENT: str = Field(
        env="ACCELA_ENVIRONMENT",
        default="TEST",
        description="Accela environment (TEST, PROD)",
    )
    ACCELA_AGENCY: str = Field(
        env="ACCELA_AGENCY",
        default="NULLISLAND",
        description="Accela agency name",
    )
    ACCELA_BASE_URL: str = Field(
        env="ACCELA_BASE_URL",
        default="https://apis.accela.com/v4",
        description="Accela API base URL",
    )
    ACCELA_AUTHORIZATION_URL: str = Field(
        env="ACCELA_AUTHORIZATION_URL",
        default="https://auth.accela.com/oauth2/authorize",
        description="Accela OAuth2 authorization URL",
    )
    ACCELA_TOKEN_URL: str = Field(
        env="ACCELA_TOKEN_URL",
        default="https://apis.accela.com/oauth2/token",
        description="Accela OAuth2 token URL",
    )
    ACCELA_REDIRECT_URI: str = Field(
        env="ACCELA_REDIRECT_URI",
        default="http://localhost/accela/connection",
        description="Accela OAuth2 redirect URI",
    )
    ACCELA_SCOPE: str = Field(
        env="ACCELA_SCOPE",
        default="records conditions",
        description="Accela OAuth2 scope",
    )

    class Config:
        extra = "ignore"
        validate_assignment = True
        case_sensitive = True
        env_file = ".env"
        env_file_encoding = "utf-8"


settings = Settings()
if settings.MONGO_URL:
    os.environ["MONGO_URI"] = settings.MONGO_URL

