import json
import traceback
from pydantic import BaseModel

inp_ex = [
    {
        "point": "spyman is the super man of 21st century",
        "chunk_id": "chunk-123456789",
        "file_path": "file_1",
    },
    {
        "point": "the super man of 21st century is spyman",
        "chunk_id": "chunk-567890123",
        "file_path": "file_2",
    },
    {
        "point": "new movie releasing on jan of 2022",
        "chunk_id": "chunk-8901234567",
        "file_path": "file_3",
    },
]
ex_out = [
    {
        "point": "spyman is the super man of 21st century",
        "count": "2",
        "chunk_id": "chunk-123456789",
        "file_path": "file_1",
    },
    {
        "point": "new movie releasing on jan of 2022",
        "chunk_id": "chunk-8901234567",
        "count": "1",
        "file_path": "file_3",
    },
]


class PointsScoreStrcture(BaseModel):
    point: str
    score: str
    chunk_id: str
    file_path: str


class GroupPointsStrcture(BaseModel):
    point: str
    count: int
    chunk_id: str
    file_path: str


class GroupPoints(BaseModel):
    points: list[GroupPointsStrcture]


class ScorePoints(BaseModel):
    points: list[PointsScoreStrcture]


class DeduplicatedHeaders(BaseModel):
    headers: list[str]


async def group_common_points(dat, openai_client):
    try:
        unq_files = []
        disnt_files = 0
        for c in dat:
            if c["file_path"] not in unq_files:
                unq_files.append(c["file_path"])

        disnt_files = len(unq_files)

        sys_p1 = (
            "I have a list of points, and I need your help grouping them by their content.\n"
            "Read through all the points and if you feel the same is availabe in another it means they are common\n"
            "For each unique point, count how many times it appears in the list.\n"
            "The input format is a list of dictionaries with 'point' as one of the keys for which you need to find the common with the count.\n\n"
        )
        sys_p2 = (
            "Please provide the output as a list of dictionaries with the structure:\n"
            '[{"points": "exact point content...", "count": X, "chunk_id":"chunk id of the point selected"}, ...]\n'
            "Where 'points' is the exact point content and 'count' is a number representing how many times it appears and 'chunk_id' is the chunk id of the point selected."
            f"***example-input:\n{json.dumps(inp_ex, indent=4)}\n\n"
            f"***expected output for example-input:\n{json.dumps(ex_out, indent=4)}\n\n"
        )

        user_p1 = f"##Input data:\n{json.dumps(dat, indent=4)}\n\n"

        completion = await openai_client.beta.chat.completions.parse(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": sys_p1},
                {"role": "user", "content": user_p1},
                {"role": "system", "content": sys_p2},
            ],
            response_format=GroupPoints,
        )

        completion_resp = completion.choices[0].message.parsed

        return completion_resp.dict(), disnt_files

    except Exception as e:
        return f"An error occurred: {str(e)}", 0


async def points_score(points, header_name, openai_client):

    prompt = (
        f"I have a list of points related to {header_name} section for conditions of approval a (MPUD) master plan unit development"
        # "remove the wrong point or things which does not look like a point and share the exact same json as output after cleaning it"
        "A point can be 'reference to a regulation' , 'condition to follow' , 'procedural statement'"
        "Make sure to send a score for all input points (0-100), a Higher Score mean its a valid point, be a little lineant on scoring only if it's too bad use lower score"
        '***Example input:[{"point":"Point data......", "chunk_id":"chunk-12843383", "file_path":"bucket/path"}, [].. ]'
        '***Example output:[{"point":"Point data......", "chunk_id":"chunk-12843383", "file_path":"bucket/path", "score":"0-100"} , []..]'
        "Here's the input:\n\n"
        f"{json.dumps(points, indent=4)}\n\n"
    )

    completion = await openai_client.beta.chat.completions.parse(
        model="gpt-4o-mini",
        messages=[
            {"role": "system", "content": prompt},
        ],
        response_format=ScorePoints,
    )

    llmtxt = completion.choices[0].message.parsed

    llm_resp = llmtxt.dict()["points"]
    above_60_points = []

    # for item in llmResp[header_name].keys() :
    for item in llm_resp:
        if int(item["score"]) >= 60:
            # Add points only, remove score key
            above_60_points.append(
                {
                    "point": item["point"],
                    "chunk_id": item["chunk_id"],
                    "file_path": item["file_path"],
                }
            )
        else:
            print("Not Included", item)

    # Extract the result from the response
    return above_60_points


async def remove_duplicates(points, header_name, openai_client):
    try:
        common_p = []
        points = await points_score(points, header_name, openai_client)
        grouped_points, disnt_files_count = await group_common_points(
            points, openai_client
        )
        llm_resp = grouped_points["points"]
        llm_resp_to_ret = []
        for resp in llm_resp:
            if resp["count"] > 1:
                if resp["count"] / disnt_files_count > 0.5:
                    common_p.append(resp)
                else:
                    llm_resp_to_ret.append(resp)
            else:
                llm_resp_to_ret.append(resp)
        return llm_resp_to_ret, common_p

    except BaseException:
        print("error:", "".join(traceback.format_exc()))
        return [], []


async def deduplicate_headers(headers, openai_client):
    """
    Deduplicate and normalize headers using OpenAI and return a list of deduplicated headers.
    """

    prompt = (
        "You are given a list of headers. Deduplicate them, ensuring headers with similar meanings "
        "or variations in capitalization are merged into a single, uniform format. Additionally, "
        "remove any headers with prefixes like '(a)', '1.', 'A.', '1)' that are often used for list numbering or "
        "organization. Only include valid header names in the final output. Also, exclude any headers titled 'General'.\n\n"
        "Only allow these in the list as it is, with proper `/` and capitalization [ \"Environmental\", \"Open Space/Buffering\", \"Transportation/Circulation\", \"Dedication of Right of Way\", \"Design/Construction Specifications\", \"Access Management\", \"Utilities/Water Service/Wastewater Disposal\", \"Stormwater\", \"Land Use\", \"Procedures\"]"
        "Make sure the `Transportation/Circulation`, `Dedication of Right of Way` , `Design/Construction Specifications`, `Access Management` are in this order (Do not interchange it),"
        "Example input:\n"
        "['(a) Environmental', '1. Design Specifications', 'Environmental', 'stormwater', 'General']\n\n"
        "Example output:\n"
        "['Environmental', 'Stormwater', 'Design Specifications']\n\n"
        f"Headers: {json.dumps(headers, indent=4)}"
    )

    try:
        # Call OpenAI API with a Pydantic response format
        completion = openai_client.beta.chat.completions.parse(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": prompt},
            ],
            response_format=DeduplicatedHeaders,
        )

        # Parse the response
        llmtxt = completion.choices[0].message.parsed

        # Ensure the parsed response has a `headers` field
        deduplicated_headers = llmtxt.dict()["headers"]

        return deduplicated_headers

    except Exception as e:
        print(f"DEBUG: Error in OpenAI API call: {e}")
        raise
