import json
import traceback
from pydantic import BaseModel, Field

inp_ex = [{"point": "developer shall submit a breeding season survey for nesting wading bird", "chunk_id": "chunk-123456789"}, {"point": "parcel developer shall submit a breeding season survey for the Florida Sandhill", "chunk_id": "chunk-567890123"}, {"point": "Staff shall review and finalize wetlands categories at or prior to the time of each preliminary development plan/preliminary site plan approval", "chunk_id": "chunk-8901234567"}]
ex_out = {"wetland": [{"point": "Staff shall review and finalize wetlands categories at or prior to the time of each preliminary development plan/preliminary site plan approval", "chunk_id": "chunk-8901234567"}], "breeding season": [{"point": "developer shall submit a breeding season survey for nesting wading bird", "chunk_id": "chunk-123456789"}, {"point": "parcel developer shall submit a breeding season survey for the Florida Sandhill", "chunk_id": "chunk-567890123"}]}


class Points(BaseModel):
    point: str = Field(description="The whole exact point")
    chunk_id: str = Field(description="ChunkId for the corresponding point")


class Category(BaseModel):
    category: str = Field(description="A category assigned to the list of points")
    points: list[Points]


class AssignSection(BaseModel):
    data: list[Category]


async def assign_section(points, header, openai_client):
    try:

        sys_p1 = (
            f"I have a list of regulatory points related to {header} considerations. "
            "Please group each point into sections like 'Wetland' and 'Breeding Season' and so on which ever you feel is suitable. "
            "The output should be a list of dictionaries where each dictionary has two keys: "
            "'category' for the label and 'points' for the corresponding text and the chunk_id for the point"
            "***Deduplicate the points"
        )
        sys_p2 = (
            "##Output format:\n"
            "{\"label\":[{\"points-title\": \"point content...\", \"chunk_id\":\"chunk-7777777777\"}], ...}"
            f"example-input:\n{json.dumps(inp_ex, indent=4)}\n\n"
            f"expected output for example-input:\n{json.dumps(ex_out, indent=4)}\n\n")

        user_p1 = (f"##Input data:\n{json.dumps(points, indent=4)}\n\n")

        completion = await openai_client.beta.chat.completions.parse(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": sys_p1},
                {"role": "user", "content": user_p1},
                {"role": "system", "content": sys_p2},
            ],
            response_format=AssignSection,
        )

        completion_resp = completion.choices[0].message.parsed.dict()
        output_data = {}

        for category_data in completion_resp['data'] :
            category_name = category_data['category'].lower().replace(" ", "_")
            points = []

            for point in category_data['points']:
                points.append({
                    'point': point['point'],
                    'chunk_id': point['chunk_id'],
                })

            output_data[category_name] = points

        return output_data

    except Exception as e:
        print(''.join(traceback.format_exc()))
        return f"An error occurred: {str(e)}"
