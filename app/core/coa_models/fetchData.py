import numpy as np
import json


async def mongo_search(mongo_collection, chunk_ids: list, keyword):
    """Fetch chunks from MongoDB."""
    if '/' in keyword:
        keyword = keyword.split('/')[-1]
    else:
        keyword = keyword[2:-2]
    chunks = {}
    for chunk in mongo_collection.find({'_id': {"$in": chunk_ids}, 'header.data': {'$regex': keyword, '$options': 'i'}}):
        chunks[chunk['_id']] = chunk
    return chunks


async def openai_embeddings(
    texts: list[str],
    openai_client: any,
    model: str = "text-embedding-3-small",
) -> np.ndarray:
    """Get embeddings from DeepAI."""

    response = await openai_client.embeddings.create(
        model=model,
        input=texts,
        encoding_format="float",
    )

    return np.array([dp.embedding for dp in response.data])


async def query(milvus_collection, openai_client, mongo_collection, query: str, keyword, cosine_threshold=0.2, top_k=200):
    """Query for nearest neighbors."""
    embedding = await openai_embeddings([query], openai_client)
    embedding = embedding[0]

    # Perform similarity search in Milvus
    results = milvus_collection.search(
        [embedding],
        "embedding",
        param={"metric_type": "COSINE", "params": {"nprobe": 10}},
        limit=top_k,
        expr=None,
        output_fields=["*"],
    )

    search_results = []
    chunk_ids = []

    for hits in results:
        for hit in hits:
            if hit.distance > cosine_threshold:
                chunk_ids.append(hit.id)
    mongo_recds = await mongo_search(mongo_collection, chunk_ids, keyword)

    for hits in results:
        for hit in hits:
            if hit.distance > cosine_threshold:
                mongo_recd = mongo_recds.get(hit.id, {})

                # Here you can customize the metadata as needed
                search_results.append({
                    **mongo_recd,
                    "id": str(hit.id),
                    "score": float(hit.distance),
                })

    return search_results


async def format_data_results(results):
    resp = []
    grouped_data = {}

    for each in results:
        file_name = each.get("file_name", "")
        chunk_id = each.get("id", "")
        page_no = each.get("text", {}).get("page", "")
        text_data = each.get("text", {}).get("data", "")
        coordinates = {
            "Width": each.get("text", {}).get("coordinates", {}).get("Width", 0),
            "Height": each.get("text", {}).get("coordinates", {}).get("Height", 0),
            "Left": each.get("text", {}).get("coordinates", {}).get("Left", 0),
            "Top": each.get("text", {}).get("coordinates", {}).get("Top", 0),
        }

        key = (file_name, page_no, text_data)

        # Check if the key (file_name, page_no) already exists
        if key not in grouped_data:
            # Create a new entry in grouped_data if it doesn't exist
            grouped_data[key] = {
                "title": "",
                "score": "",
                "conditions": [],
            }
            resp.append(grouped_data[key])

        # Append the current condition to the existing entry
        grouped_data[key]["conditions"].append({
            "text": text_data,
            "meta_data": {
                "file_name": file_name,
                "coordinates": coordinates,
                "page_no": page_no,
                "chunk_id": chunk_id
            },
        })

    return resp


async def struct_cont(structured_contents, openai_client):
    try:
        output_structure = {"answer": "",
                            "structured_content": [],
                            "steps": []}
        contents = ""
        '''
        "structured_content":
            [
                "title": should generated from
                "score":
                "conditions":
            ]

            conditions:
                [
                    "text": - available in result
                    "meta_data": - available in result
                ]

            "meta_data":
                {
                    "file_name": available in result
                    "coordinates": available in result
                    "chunk_id": available in result
                    "page_no": available in result
                }
        '''
        system_prompt_one_liner = '''
        You are an expert at creating concise one-liners and relevancy scoring.

        Task:
        - Create unique one-liner (max 5 words) relevant and most suited to the the given input
        - Score text's revelance to the question (0-100)

        output Format
        {
            "data": {
                "oneline": "text",
                "score": 0-100
            }
        }

        Important:
        - Return ONLY the JSON result
        '''
        for structure in structured_contents:
            inputs = ""
            for condition in structure["conditions"]:
                inputs += condition.get("text", "") + '\n'
            contents += inputs
            _query = f"""
            Question: Create one-liners and scores for each text in the following list.
            input: {inputs}

            question : {query}
            """
            response = await gpt_4o_mini_complete(
                _query,
                openai_client,
                system_prompt=system_prompt_one_liner,
            )
            response = json.loads(response).get("data", {})
            structure["title"] = response.get("oneline", "")
            structure["score"] = int(response["score"])
            if structure["score"] < 6:
                continue
            output_structure["structured_content"].append(structure)
            # yield output_structure
        system_prompt_summarizer = '''
        You are an expert at creating summary.

        Task
        - Provide overall summary (max 5 lines) covering all input texts

        '''
        output_structure["answer"] = ""
        response = await gpt_4o_mini_complete(
            f'''{contents}''',
            openai_client,
            system_prompt=system_prompt_summarizer)

        for chunk in response:
            output_structure["answer"] += chunk

        return output_structure
    except Exception as e:
        return ('error', e)


async def gpt_4o_mini_complete(
    prompt,
    openai_client,
    system_prompt=None,
    history_messages=[],
    **kwargs,
) -> str:

    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.extend(history_messages)
    messages.append({"role": "user", "content": prompt})

    response = await openai_client.chat.completions.create(
        model="gpt-4o-mini",  # "gpt-4o-mini",  # Use "gpt-3.5-turbo" for GPT-3.5 or "gpt-4" for GPT-4,
        messages=messages,
        temperature=0.0
    )

    return response.choices[0].message.content


async def cleanResp(resp):
    fi = []
    if 'structured_content' in resp :
        for co in resp['structured_content'] :
            t = {}
            if 'conditions' in co :
                for con in co['conditions'] :
                    t['point'] = con['text']
                    t['file_path'] = con['meta_data']['file_name']
                    t['chunk_id'] = con['meta_data']['chunk_id']

                fi.append(t)
    return fi


async def fetch_data(milvus_collection, openai_client, mongo_collection, user_query, section_keyword):
    try:
        results = await query(milvus_collection, openai_client, mongo_collection, user_query, section_keyword)
        res = await format_data_results(results)
        resp = await struct_cont(res , openai_client)
        resps = await cleanResp(resp)
        return (resps)
    except Exception as e:
        print(f'error: {e}')


