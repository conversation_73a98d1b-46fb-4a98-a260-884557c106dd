from app.core.coa_models.assignSection import assign_section
from app.core.coa_models.fetchData import fetch_data
from app.core.coa_models.clean_data import remove_duplicates
from openai import AsyncOpenAI
from app.core.config import settings
from pymongo import MongoClient
from pymilvus import connections, Collection
from app.crud.mongo_config import load_COA_header_config
import asyncio


class COAPointsGenerator:
    def __init__(self, mongo_db_name="lightrag", mongo_collection_name="text_chunks", milvus_collection_name="chunks", mongo_config_name="COA_header_config"):
        print('initilaizing COA_Points_Generator----')
        self.coa_data = {}
        try:
            self.openai_client = AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
            self.openai_model = 'gpt-4o-mini'
        except Exception as e:
            print(f"Error initializing OpenAI client: {e}")
            raise

        try:
            self.mongo_client = MongoClient(settings.MONGO_URL)
            self.mongo_collection = self.mongo_client[mongo_db_name][mongo_collection_name]
            self.mongo_coa_config = self.mongo_client[settings.MONGO_CONFIG_DATABASE][mongo_config_name]

        except Exception as e:
            print(f"Error initializing MongoDB client: {e}")
            raise

        try:
            connections.connect(uri=settings.MILVUS_URI, db_name=settings.MILVUS_DEFAULT_DATABASE, token=settings.MILVUS_TOKEN)
            self.milvus_collection = Collection(milvus_collection_name)
        except Exception as e:
            print(f"Error initializing Milvus connection: {e}")
            raise

    async def fetch_data(self, user_query, section_keyword):
        """Fetch data based on section."""
        data_fetched = await fetch_data(self.milvus_collection, self.openai_client, self.mongo_collection, user_query, section_keyword)
        return data_fetched

    async def remove_duplicates(self, data_fetched, header_name):
        """Remove duplicates from fetched data."""
        points, common_points = await remove_duplicates(data_fetched, header_name, self.openai_client)
        return points, common_points

    async def assign_section(self, points, section_keyword):
        """Assign section to points."""
        section_points = await assign_section(points, section_keyword, self.openai_client)
        return section_points

    async def process_header(self, header_name, fnd_config, coa_data):
        data_fetched = await self.fetch_data(f'list all the points under section {header_name}', header_name)
        if not data_fetched:
            coa_data[header_name] = {}
            return

        points, common_points = await self.remove_duplicates(data_fetched, header_name)
        section = await self.assign_section(points, header_name)
        section['common-points'] = common_points

        print('header_name\n', header_name)
        self.coa_data[header_name] = section

    async def process_all(self):
        """Main processing method."""
        try:
            print('starting with loading COA config..')
            # coa_data = {}
            tasks = []
            fnd_config = self.mongo_coa_config.find_one({})
            print('fnd_config', fnd_config)
            if not fnd_config :
                await load_COA_header_config()
            if 'headerList' in fnd_config :
                for header_name in fnd_config['headerList'] :
                    tasks.append(self.process_header(header_name, fnd_config, self.coa_data))
                await asyncio.gather(*tasks)

                print('coa_data', self.coa_data)

            return self.coa_data
        except Exception as e:
            print("error:", e)

