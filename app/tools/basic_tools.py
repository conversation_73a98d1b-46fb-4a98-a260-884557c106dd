from langchain_core.tools import tool
from app.routes.version_control_route import delete_rec_in_mpud_version, update_rec_in_mpud_version


@tool
def add(a: int, b: int) -> int:
    """Adds a and b.

    Args:
        a: first int
        b: second int
    """
    # print(f'inside tools add {a} {b}')
    return a + b


@tool
def multiply(a: int, b: int) -> int:
    """Multiplies a and b.

    Args:
        a: first int
        b: second int
    """
    # print(f'inside tools multiply {a} {b}')
    return a * b


@tool
def update_mpuid_data(record_id: str, rule: str, operation: str, update_data: str = None) :
    """Updates or deletes a rule based on the record_id provided.

    Args:
        record_id: The record ID to be updated.
        rule: The name of the rule to be updated or deleted.
        operation: The action to be performed: 'update' or 'delete'.
    """

    # print(f'inside update_mpuid_data-{record_id}--{rule}---{operation}++{update_data}')

    if operation == "delete":
        delete_resp = delete_rec_in_mpud_version(record_id, rule)
        return delete_resp['message']

    if operation == "update":
        update_resp = update_rec_in_mpud_version(record_id, rule, update_data)
        return update_resp["message"]


tools = [add, multiply, update_mpuid_data]
