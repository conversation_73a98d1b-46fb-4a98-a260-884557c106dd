from langchain_core.tools import tool
from langchain_core.runnables import RunnablePassthrough
from langchain_core.messages.ai import AIMessage
# from app.routes.version_control_route import (
#     delete_rec_in_mpud_version,
#     update_rec_in_mpud_version,
# )


@tool
def get_relevant_data_for_mpuid_from_brain(
    user_input: str, base_chain=RunnablePassthrough(),
) -> str:
    """
    Retrieves relevant data for MPUID (Master Planned Unit Development) by performing a similarity search
    in the knowledge base using the given user input. The function processes the query to identify the most
    related information that can be used for generating or understanding MPUIDs, and returns a JSON object
    containing both the relevant data and a title summarizing the response.

    Args:
        user_input (str): The user's query or input specifying the type of data needed for MPUID.

     Returns:
        str: A string of JSON object containing:
            - "key" (str): A title summarizing the context or relevance of the retrieved data.
            - "value" (str): The most relevant data related to MPUID based on the user's query.
    """
    chain = base_chain
    return chain.stream({"input": user_input})


@tool
def add_data_to_mpud(data: str, chat_id: str = None, user=None):
    """
    Adds the specified data as a json string to the Master Planned Unit Development (MPUD) after user approval.

    This function is called when the user has accepted the data to be added to the MPUD. It updates the
    corresponding record in the MPUD database and returns a streaming response to inform the user of the
    update status.

    Parameters:
        data (str): The json data to be added to the MPUD.
    """

    def streaming_resp():
        yield AIMessage(content="Updating...")
        # update_rec_in_mpud_version(
        #     chat_id, data, uuid.uuid4(), county_name=user["custom:county"], user=user
        # )
        yield AIMessage(content="Updated successfully \n")
        yield AIMessage(
            content="what else you needed to modify the MPUD futher, let me know you questions",
        )

    return streaming_resp()


@tool
def delete_data_from_mpud(
    input: str,  # Keep consistently named parameters
    chat_id: str,
    user: str,  # Fix the type annotation
    base_chain: RunnablePassthrough = RunnablePassthrough(),  # Provide default value
) -> AIMessage:  # Add return type annotation
    """
    Delete the specific title asked to be after the confirmation.
    This function is called when the user has accepted to delete the title from the mpud. It deletes the
    corresponding title in the MPUD database and returns a streaming response to inform the user of the
    delete status.

    Args:
        input (str): The title to be deleted from the MPUD
        chat_id (str): The ID of the chat session
        user (str): The user information
        base_chain (RunnablePassthrough, optional): The base chain for processing. Defaults to RunnablePassthrough()

    Returns:
        Generator[AIMessage, None, None]: A stream of AI messages indicating the deletion progress
    """
    def streaming_resp():
        base_chain.invoke({"input": input})
        yield AIMessage(content="Updating...")
        # delete_rec_in_mpud_version(chat_id, input, user["custom:county"], user=user)  # Uncomment when needed
        yield AIMessage(content="Deleted successfully \n")
        yield AIMessage(
            content="What else do you need to modify in the MPUD further? Let me know your questions",
        )
    return streaming_resp()


chat_tools = [
    get_relevant_data_for_mpuid_from_brain,
    add_data_to_mpud,
    delete_data_from_mpud,
]

tool_default_args = {
    "get_relevant_data_for_mpuid_from_brain": {
        "prompt": "chat/similarity_search.prompt",
        "context": True,
    },
    "add_data_to_mpud": {},
    "delete_data_from_mpud": {
        "prompt": "chat/title_finder.prompt",
    },
}
