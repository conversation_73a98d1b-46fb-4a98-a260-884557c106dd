# middleware/telemetry.py
from fastapi import FastAPI, Request
from opentelemetry import trace
import logging
from app.core.telemetry import setup_telemetry

logger = logging.getLogger(__name__)


class TelemetryMiddleware:
    def __init__(self):
        self.logger = None

    def init_app(self, app: FastAPI) -> None:
        """Initialize the telemetry middleware for FastAPI application"""
        # Use the existing telemetry setup
        self.logger = setup_telemetry()
        logger.info("Initializing telemetry middleware")

        @app.middleware("http")
        async def telemetry_middleware(request: Request, call_next):
            with trace.get_tracer(__name__).start_as_current_span("request_middleware") as span:
                span.set_attribute("http.method", request.method)
                span.set_attribute("http.url", str(request.url))
                span.set_attribute("http.route", request.url.path)

                # Add chat_id if present in request
                chat_id = request.query_params.get("chat_id") or request.path_params.get("chat_id")
                if chat_id:
                    span.set_attribute("chat.id", str(chat_id))

                # Get user information if available
                if hasattr(request.state, 'user'):
                    user = request.state.user
                    span.set_attribute("user.id", user.get('sub', 'unknown'))
                    span.set_attribute("user.name", user.get('name', 'unknown'))
                    span.set_attribute("user.role", user.get('custom:role', 'unknown'))

                try:
                    response = await call_next(request)
                    span.set_attribute("http.status_code", response.status_code)
                    return response
                except Exception as e:
                    span.set_attribute("error", True)
                    span.set_attribute("error.type", type(e).__name__)
                    span.set_attribute("error.message", str(e))
                    raise


# Create default instance
default_telemetry = TelemetryMiddleware()

__all__ = ['TelemetryMiddleware', 'default_telemetry']
