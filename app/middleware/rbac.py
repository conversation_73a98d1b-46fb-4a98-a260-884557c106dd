# middleware/rbac.py
from fastapi import Request, HTTPException
from typing import Dict, Optional, List
import yaml
import time
from functools import lru_cache
import os
from pathlib import Path
import logging

from app.utils.userconfig_utils import get_role_permissions

logger = logging.getLogger(__name__)


class RouteAccessControl:
    def __init__(self, config_directory: str = "rbac_configs", default_config: Dict = None):
        self.config_directory = self._resolve_config_path(config_directory)
        logger.info(f"Using config directory: {self.config_directory}")

        self.default_config = default_config or {
            "requires_auth": True,
            "allowed_roles": ["*"],
            "rate_limit": {"requests_per_minute": 30},
        }

        # Initialize storage
        self.endpoint_map = {}  # Initialize before loading configs
        self.endpoints = {}     # Initialize before loading configs
        self._rate_limit_store = {}

        # Load configurations
        self._load_all_configs()

    def _resolve_config_path(self, config_directory: str) -> str:
        """Resolve the absolute path of the config directory"""
        possible_paths = [
            os.path.join(os.getcwd(), "app", config_directory),
            os.path.join(os.getcwd(), config_directory),
            os.path.abspath(config_directory),
            os.path.join(Path(__file__).parent.parent, config_directory),
        ]

        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found config directory at: {path}")
                return path

        default_path = possible_paths[0]
        os.makedirs(default_path, exist_ok=True)
        return default_path

    def _load_all_configs(self) -> None:
        """Load all YAML configurations from the config directory"""
        if not os.path.exists(self.config_directory):
            logger.error(f"Config directory not found: {self.config_directory}")
            return

        logger.info(f"Loading YAML configs from: {self.config_directory}")

        yaml_files = [f for f in os.listdir(self.config_directory)
                      if f.endswith('.yaml')]

        for file in yaml_files:
            file_path = os.path.join(self.config_directory, file)

            logger.info(f"Loading config file: {file}")

            try:
                with open(file_path, 'r') as f:
                    config = yaml.safe_load(f)
                    if config and 'endpoints' in config:
                        for path, endpoint_config in config['endpoints'].items():
                            # Normalize the path
                            normalized_path = path if path.startswith('/') else f'/{path}'

                            # Store in endpoints dictionary
                            self.endpoints[normalized_path] = endpoint_config
                            logger.info(f"Loaded configuration for endpoint: {normalized_path}")

                            # Store in endpoint_map for method-specific lookup
                            for method, method_config in endpoint_config.items():
                                key = f"{normalized_path}:{method.lower()}"
                                self.endpoint_map[key] = method_config
                                logger.info(f"Loaded method configuration: {key}")

            except Exception as e:
                logger.error(f"Error loading {file}: {str(e)}")

    def get_endpoint_config(self, path: str, method: str) -> Optional[Dict]:
        """Get endpoint configuration based on path and method"""
        logger.debug(f"Getting config for original path: {path}, method: {method}")

        # Remove API prefix and clean path
        path_parts = path.split('/')
        if 'api' in path_parts:
            path_parts = path_parts[path_parts.index('api') + 1:]

        clean_path = '/' + '/'.join(filter(None, path_parts))
        method = method.lower()

        logger.debug(f"Cleaned path: {clean_path}")

        # Direct lookup in endpoint map
        key = f"{clean_path}:{method}"
        if key in self.endpoint_map:
            logger.debug(f"Found exact match for {key}")
            return self.endpoint_map[key]

        # Try parameterized version
        param_parts = []
        for part in path_parts:
            if part.isdigit() or (part and part.startswith('{')):
                param_parts.append("{param}")
            elif part:
                param_parts.append(part)

        param_path = '/' + '/'.join(filter(None, param_parts))
        param_key = f"{param_path}:{method}"

        if param_key in self.endpoint_map:
            logger.debug(f"Found parameterized match for {param_key}")
            return self.endpoint_map[param_key]

        # Try wildcard
        for stored_key in self.endpoint_map:
            path_part, method_part = stored_key.split(':')
            if '*' in path_part and method_part == method:
                wild_path = path_part.replace('/*', '')
                if clean_path.startswith(wild_path):
                    logger.debug(f"Found wildcard match with {stored_key}")
                    return self.endpoint_map[stored_key]

        logger.warning(f"No configuration found for {clean_path} {method}")
        return None

    def verify_role_access(self, allowed_roles: List[str], user_role: str) -> bool:
        """Verify if user role has access to endpoint"""
        logger.debug(f"Verifying role access: user_role={user_role}, allowed_roles={allowed_roles}")
        has_access = "*" in allowed_roles or user_role in allowed_roles
        logger.debug(f"Role access result: {has_access}")
        return has_access

    def check_rate_limit(self, user_id: str, endpoint: str, config: Dict) -> bool:
        """Check if request is within rate limits"""
        current_time = time.time()
        key = f"{user_id}:{endpoint}"
        rate_limit = config.get('rate_limit', {}).get('requests_per_minute', 30)

        if key in self._rate_limit_store:
            last_time, count = self._rate_limit_store[key]
            if current_time - last_time < 60:
                if count >= rate_limit:
                    logger.warning(f"Rate limit exceeded for {user_id} on {endpoint}")
                    return False
                self._rate_limit_store[key] = (last_time, count + 1)
            else:
                self._rate_limit_store[key] = (current_time, 1)
        else:
            self._rate_limit_store[key] = (current_time, 1)
        return True

    async def check_permissions(self, user_role: str, required_permissions: List[str]) -> bool:
        """Check if the user's role has all the required permissions"""
        try:
            # Get permissions for the user's role
            role_permissions = await get_role_permissions(user_role)

            if not role_permissions or 'permissions' not in role_permissions:
                logger.warning(f"No permissions found for role: {user_role}")
                return False

            permissions = role_permissions['permissions']

            # Check if all required permissions are granted
            return all(
                permissions.get(perm, False)
                for perm in required_permissions
            )
        except Exception as e:
            logger.error(f"Error checking permissions: {str(e)}")
            return False

    async def validate_request(self, request: Request, user_claims: dict, endpoint_config: Dict = None) -> bool:
        """Validate the request against RBAC rules with permission fallback"""
        path = request.url.path
        method = request.method

        logger.info(f"Validating request: path={path}, method={method}")

        config = endpoint_config or self.get_endpoint_config(path, method)
        if not config:
            logger.warning(f"No configuration found for {path} {method}, using default config")
            return True

        if not config.get('requires_auth', True):
            logger.debug("Authentication not required for this endpoint")
            return True

        user_role = user_claims.get("custom:role", "user")
        allowed_roles = config.get('allowed_roles', [])
        required_permissions = config.get('required_permissions', [])

        logger.info(f"Checking access: user_role={user_role}, allowed_roles={allowed_roles}")

        # First check: Direct role-based access
        has_role_access = self.verify_role_access(allowed_roles, user_role)

        # Second check: Permission-based access (only if role access failed)
        has_permission_access = False
        if not has_role_access and required_permissions:
            has_permission_access = await self.check_permissions(user_role, required_permissions)
            if has_permission_access:
                logger.info(f"Access granted for {user_role} based on permissions: {required_permissions}")

        # Grant access if either check passes
        has_access = has_role_access or has_permission_access

        if has_access:
            logger.info(f"Access granted for {user_role} to {path}")
        else:
            logger.warning(f"Access denied for {user_role} to {path}")
            logger.debug(f"Failed both role-based ({has_role_access}) and permission-based ({has_permission_access}) checks")

        if has_access and not self.check_rate_limit(user_claims['sub'], path, config):
            raise HTTPException(status_code=429, detail="Rate limit exceeded")

        return has_access


@lru_cache()
def get_access_control() -> RouteAccessControl:
    return RouteAccessControl()
