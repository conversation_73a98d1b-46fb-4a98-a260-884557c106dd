# middleware/__init__.py
from typing import Optional, Dict
from fastapi import FastAP<PERSON>
from enum import Enum
import logging
import os
import yaml
from .rbac import RouteAccessControl, get_access_control

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)


class DefaultAccess(str, Enum):
    ALLOW = "allow"
    DENY = "deny"
    LOG = "log"


class EnvEnum(str, Enum):
    """Enumeration for the the environment application is running in"""

    TEST = "testing"
    PROD = "production"


class RBACMiddleware:
    def __init__(
        self,
        config_directory: str = "rbac_configs",
        default_access: DefaultAccess = DefaultAccess.LOG,
    ):
        logger.info(f"Initializing RBAC Middleware with {default_access} default access")

        self.config_directory = config_directory
        self.default_access = default_access
        self.default_config = {
            "requires_auth": True,
            "allowed_roles": ["superadmin", "admin"] if default_access == DefaultAccess.DENY else ["*"],
            "rate_limit": {"requests_per_minute": 80},
        }

        self.access_control = RouteAccessControl(
            config_directory=config_directory,
            default_config=self.default_config,
        )

        # Log loaded configurations
        logger.info("Loaded endpoint configurations:")
        for path, config in self.access_control.endpoints.items():
            methods = list(config.keys())
            allowed_roles = set()
            for method_config in config.values():
                if isinstance(method_config, dict):
                    allowed_roles.update(method_config.get('allowed_roles', []))
            logger.info(f"- {path}: methods={methods}, roles={list(allowed_roles)}")

    def _handle_missing_config(self, path: str, method: str) -> Optional[Dict]:
        route_name = path.split('/')[2] if len(path.split('/')) > 2 else 'unknown'
        message = f"No RBAC configuration found for route: {method.upper()} {path}"

        yaml_example = {
            "endpoints": {
                "/users": {
                    method.lower(): {
                        "requires_auth": True,
                        "allowed_roles": ["superadmin", "admin", "user"],
                        "rate_limit": {"requests_per_minute": 30},
                    },
                },
            },
        }

        if self.default_access == DefaultAccess.ALLOW:
            logger.warning(f"{message}. Allowing access by default.")
            return {"requires_auth": True, "allowed_roles": ["*"]}
        elif self.default_access == DefaultAccess.DENY:
            logger.warning(f"{message}. Denying access by default.")
            return self.default_config
        else:  # LOG
            logger.warning(
                f"{message}. Using default configuration.\n"
                f"Consider creating or updating {self.config_directory}/{route_name}.yaml",
            )
            logger.info(f"Suggested YAML configuration:\n{yaml.dump(yaml_example, default_flow_style=False)}")
            return self.default_config

    def init_app(self, app: FastAPI) -> None:
        logger.info("Initializing RBAC middleware for FastAPI application")

        from fastapi import Request
        from fastapi.responses import JSONResponse
        from fastapi import HTTPException

        @app.middleware("http")
        async def rbac_middleware(request: Request, call_next):
            if request.method == "OPTIONS":
                return await call_next(request)

            if request.url.path.startswith("/api/auth"):
                return await call_next(request)

            # Check if this is an Accela endpoint that doesn't require authentication
            if request.url.path.startswith("/api/accela"):
                endpoint_config = self.access_control.get_endpoint_config(
                    request.url.path,
                    request.method.lower(),
                )

                # If no config found or authentication not required, allow access
                if not endpoint_config or not endpoint_config.get('requires_auth', True):
                    return await call_next(request)

                # If authentication is required, continue with normal flow

            if request.url.path.startswith("/api"):
                authorization_header = request.headers.get("Authorization")
                if authorization_header:
                    try:
                        from app.utils.auth_utils import verify_token_and_get_user_details
                        claims = verify_token_and_get_user_details(authorization_header)

                        endpoint_config = self.access_control.get_endpoint_config(
                            request.url.path,
                            request.method.lower(),
                        )

                        if not endpoint_config:
                            endpoint_config = self._handle_missing_config(
                                request.url.path,
                                request.method.lower(),
                            )

                        try:
                            is_allowed = await self.access_control.validate_request(
                                request,
                                claims,
                                endpoint_config,
                            )
                            if not is_allowed:
                                return JSONResponse(
                                    status_code=403,
                                    content={
                                        "message": "Access denied by RBAC policy",
                                        "details": "User role does not have permission for this endpoint",
                                    },
                                )
                        except HTTPException as e:
                            return JSONResponse(
                                status_code=e.status_code,
                                content={"message": str(e.detail)},
                            )

                        request.state.user = claims
                        return await call_next(request)

                    except HTTPException as e:
                        return JSONResponse(
                            status_code=e.status_code,
                            content={"message": str(e)},
                        )
                    except Exception as e:
                        return JSONResponse(
                            status_code=500,
                            content={"message": str(e)},
                        )
                else:
                    return JSONResponse(
                        status_code=401,
                        content={"message": "Authorization header is missing."},
                    )
            else:
                return await call_next(request)


# Create default instance
default_rbac = RBACMiddleware(default_access=DefaultAccess.LOG)
strict_rbac = RBACMiddleware(default_access=DefaultAccess.DENY)

__all__ = ['RBACMiddleware', 'default_rbac', 'DefaultAccess', 'strict_rbac']
