# --------------------------------------------------------------------------------
# Company Name: Pasco AI
#
# Confidential Information of Pasco AI
# NOTICE: All information contained herein is, and remains the property of Pasco AI.
# The intellectual and technical concepts contained herein are proprietary to Pasco AI
# and may be covered by U.S. and Foreign Patents, patents in process, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material is strictly forbidden unless prior written permission is obtained
# from Pasco AI. Access to this file is granted on the condition that it will not be used for any purpose other than as specifically authorized
# in writing by Pasco AI, and subject to the terms of any agreement governing such access and use. Access to this file may also require
# a signed Non-Disclosure Agreement.
#
# --------------------------------------------------------------------------------
from typing import AsyncGenerator
from fastapi import FastAPI, Request, Depends
from contextlib import asynccontextmanager
from fastapi.security import H<PERSON><PERSON><PERSON>earer
from app.core.config import Settings
from fastapi.middleware.cors import CORSMiddleware
from app.middleware.telemetry import default_telemetry
from app.routes import (
    authentication_route,
    base_route,
    ocr_route,
    permission_route,
    version_control_route,
    notification_route,
    users_route,
    chat_route,
    source_route,
    application_route,
    inspector_route,
    coa_route,
    knowledge_graph_route,
    chunk_store,
    accela_route
)
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.openai import OpenAIInstrumentor
from opentelemetry.instrumentation.anthropic import AnthropicInstrumentor
from app.core.telemetry import setup_telemetry
from app.routes.v2 import (
    application_route as application_route_v2,
    conditions_of_approval_route,
    layout_route,
    chat_route as chat_route_v2,
    project_route as project_route_v2
)
from app.utils.userconfig_utils import initialize_default_permissions
from app.middleware import default_rbac
import uvicorn
import asyncio
from enum import Enum
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
# This is a placeholder, update based on your DB logic.
from app.core.coa_models.main_coa import COAPointsGenerator
# from app.core.coa_models.main_coa.main_coa import COA_Points_Generator
from app.crud.mongo_config import get_config_from_db, load_KG_templates, load_COA_header_config, get_temp_coa_points_from_db, load_temp_coa_points


class EnvEnum(str, Enum):
    """Enumeration for the the environment application is running in"""

    TEST = "testing"
    PROD = "production"


settings = Settings()
security = HTTPBearer()
dependencies = [Depends(security)]
# Setup telemetry
loggers = setup_telemetry()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    # Startup code (previously in @app.on_event("startup"))
    await initialize_default_permissions()

    # Load configuration on startup
    global config_cache
    config_cache = await get_config_from_db()

    global COA_Points
    COA_Points = {}

    await load_KG_templates()
    await load_COA_header_config()
    # background task to periodically check for config updates
    if not COA_Points:
        coa_refresh_task = asyncio.create_task(refresh_coa_data_periodically())
    refresh_config_task = asyncio.create_task(refresh_config_periodically())
    yield  # This is where the application runs

    # Shutdown code (previously in @app.on_event("shutdown"))
    refresh_config_task.cancel()
    coa_refresh_task.cancel()
    try:
        await refresh_config_task
        await coa_refresh_task
    except asyncio.CancelledError:
        pass

# async def refresh_coa_data_periodically():
#     """Background task to refresh the configuration every 60 seconds"""
#     print('refresh_coa_data_periodically started')
#     global COA_Points
#     while True:
#         coa = COA_Points_Generator()
#         task = asyncio.create_task(coa.process())
#         task.add_done_callback(lambda t: globals().update({'COA_Points': t.result()}))
#         await asyncio.sleep(60 * 60 * 24)


async def refresh_coa_data_periodically():
    """Background task to refresh the COA data every 24 hours"""
    coa = COAPointsGenerator()
    resp = await get_temp_coa_points_from_db()

    def update_global_coa(future):
        try:
            result = future.result()
            global COA_Points
            COA_Points = [result]
            load_temp_coa_points(result)
            print("COA data successfully updated")
        except Exception as e:
            print(f"Error updating COA data: {str(e)}")

    if not resp or resp == [{}] :
        task = asyncio.create_task(coa.process_all())
        task.add_done_callback(update_global_coa)
    else:
        global COA_Points
        COA_Points = resp
    failure_count = 0
    while True:
        try:
            print("Starting COA data refresh")
            await asyncio.sleep(60 * 60 * 24)
            task = asyncio.create_task(coa.process())
            task.add_done_callback(update_global_coa)
        except Exception as e:
            print(f"Error in COA refresh cycle: {str(e)}, count:{failure_count}")
            failure_count += 1
            if failure_count >= 3:
                print("Failed 3 times consecutively. Stopping the refresh cycle.")
                break  # Exit the loop if 3 consecutive failures occur
            await asyncio.sleep(300)


async def refresh_config_periodically():
    """Background task to refresh the configuration every 60 seconds"""
    print('refresh_config_periodically started')
    global config_cache
    while True:
        await asyncio.sleep(60 * 60)  # Sleep for 1 hr
        new_config = await get_config_from_db()  # Fetch new config from DB
        if new_config != config_cache:  # Update the cache if the config has changed
            config_cache = new_config
            print("Config updated:", config_cache)


def get_application(env: EnvEnum = EnvEnum.PROD):
    """Create a new FastAPI application."""
    _app = FastAPI(
        title="Pasco AI GovTech",
        lifespan=lifespan,
        debug=False,
        swagger_ui_parameters={
            "defaultModelsExpandDepth": -1,
            "tagsSorter": "alpha",
        },
        openapi_schema={
            "components": {
                "securitySchemes": {
                    "BearerAuth": {
                        "type": "http",
                        "scheme": "bearer",
                        "bearerFormat": "JWT",
                    },
                },
            },
        },
    )
    origins = ["*"]
    _app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    # Only add telemetry and instrumentation for non-test environments
    if env != EnvEnum.TEST:
        default_telemetry.init_app(_app)
        # Initialize instrumentations
        LoggingInstrumentor().instrument(set_logging_format=True)
        RequestsInstrumentor().instrument()
        FastAPIInstrumentor.instrument_app(_app)
        OpenAIInstrumentor().instrument()
        AnthropicInstrumentor().instrument()
    # For testing environment, don't add auth middleware as we will be adding a custom one
    if env != EnvEnum.TEST:
        # Attaching the default RBAC middleware
        default_rbac.init_app(_app)

    # _app.include_router(base_route.router)
    _app.include_router(authentication_route.router, prefix="/api")
    _app.include_router(source_route.router, dependencies=dependencies, prefix="/api")
    _app.include_router(
        base_route.router,
        dependencies=dependencies,
        prefix="/api",
    )
    _app.include_router(ocr_route.router, prefix="/api", dependencies=dependencies)
    _app.include_router(
        version_control_route.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        notification_route.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        application_route.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        coa_route.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        users_route.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        chat_route.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        application_route_v2.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        conditions_of_approval_route.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        layout_route.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        chat_route_v2.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        project_route_v2.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        inspector_route.router,
        prefix="/api",
        dependencies=dependencies,
    )

    _app.include_router(
        knowledge_graph_route.router,
        prefix="/api",
        dependencies=dependencies,
    )

    _app.include_router(
        permission_route.router,
        prefix="/api",
        dependencies=dependencies,
    )
    _app.include_router(
        chunk_store.router,
        prefix="/api",
        dependencies=dependencies,
    )

    _app.include_router(
        accela_route.router,
        prefix="/api/accela",
    )

    return _app


def get_complete_application():
    """In production need to start and close db connection and some middleware."""

    _app = get_application()
    # _app.add_event_handler("startup", connect_llm)
    # _app.add_event_handler("startup", connect_node_db)
    # _app.add_event_handler("startup", connect_vector_db)
    # _app.add_event_handler("startup", lambda: connect_mongo_db(settings.MONGO_DB_NAME, 'tasks'))

    return _app


app = get_complete_application()


@app.get("/health/neo4j")
async def check_neo4j_health():
    try:
        from neo4j import GraphDatabase
        from app.core.config import settings

        driver = GraphDatabase.driver(
            settings.NEO4J_URI,
            auth=(settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD),
        )
        # Test connection by running simple query
        with driver.session() as session:
            result = session.run("RETURN 1")
            result.single()
        driver.close()
        return {"status": "healthy", "message": "Successfully connected to Neo4j"}
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Failed to connect to Neo4j: {str(e)}",
        }


# This for health check of the Connector #! DO NOT DELETE IT
@app.get("/")
def catch_all(__: Request):
    loggers["pasco"].info("Health check request received")
    return {"message": "Hey!, welcome to pasco-govtech-ai"}


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc: RequestValidationError):
    # Extract the field and error message from the validation errors
    error_details = exc.errors()
    concise_errors = []

    for error in error_details:
        field = error.get("loc", ["unknown"])[1]  # Get the field name
        message = error.get("msg", "Invalid input")  # Get the error message
        concise_errors.append(f"{field}: {message}")

    # Return a concise error response
    return JSONResponse(
        status_code=422,
        content={
            "error": "Invalid input format.",
            "details": concise_errors,
        },
    )


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
