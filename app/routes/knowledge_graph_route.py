import os
from fastapi import APIRouter, Depends, BackgroundTasks, Query
from app.crud.knowledge_graph_repository import KnowledgeGraphRepository
from app.crud.knowledge_graph_v2_repository import KnowledgeGraphRepositoryV2
from app.database.database_storage import get_mongo_db, Database
from app.utils.auth_utils import get_current_user
from app.crud.mongo.base_repository import BaseRepository as MongoRepo

# import asyncio

router = APIRouter(
    tags=["KnowledgeGraph"],
    responses={404: {"description": "Not found"}},
)

DOCUMENT_DETAILS_COLLECTION = MongoRepo("documents")


@router.post("/upload_file")
def upload_file(
    background_tasks: BackgroundTasks,
    s3location: str = Query(
        ...,
        description="If single_file is true provide path for the file else the bucket.",
    ),
    db: Database = Depends(get_mongo_db),
    cur_user=Depends(get_current_user),
    single_file: bool = True,
):
    if single_file:
        bucket_name = s3location.split("/")[0]
        location_name = "/".join(s3location.split("/")[1:])
    else:
        bucket_name = s3location
        location_name = ""
    repo = KnowledgeGraphRepository(db)
    background_tasks.add_task(
        repo.process_data,
        bucket_name,
        location_name,
        cur_user["custom:county"],
        cur_user["name"],
        single_file,
        False,
        None,
    )
    return {"status": "ok", "message": "extract running in background"}


@router.post("/upload_file_v2")
def upload_file_v2(
    background_tasks: BackgroundTasks,
    s3location: str = Query(
        ...,
        description="If single_file is true provide path for the file else the bucket.",
    ),
    db: Database = Depends(get_mongo_db),
    cur_user=Depends(get_current_user),
    single_file: bool = True,
):
    if single_file:
        bucket_name = s3location.split("/")[0]
        location_name = "/".join(s3location.split("/")[1:])
    else:
        bucket_name = s3location
        location_name = ""
    repo = KnowledgeGraphRepositoryV2(db)
    background_tasks.add_task(
        repo.process_data,
        bucket_name,
        location_name,
        cur_user["cognito:username"],
        cur_user["custom:county"],
        cur_user["name"],
        single_file,
        False,
        None,
    )
    return {"status": "ok", "message": "extract running in background"}


@router.post("/upload_file_v3")
def upload_file_v3(
    background_tasks: BackgroundTasks,
    s3location: str = Query(
        ...,
        description="If single_file is true provide path for the file else the bucket.",
    ),
    db: Database = Depends(get_mongo_db),
    user=Depends(get_current_user),
    single_file: bool = True,
):
    try:
        # Generate unique upload ID
        upload_id = os.urandom(16).hex()
        if single_file:
            bucket_name = s3location.split("/")[0]
            location_name = "/".join(s3location.split("/")[1:])
            repo = KnowledgeGraphRepositoryV2(db)
            background_tasks.add_task(
                repo.process_data,
                bucket_name,
                location_name,
                user.get("cognito:username"),
                user.get("custom:county"),
                user.get("name"),
                True,
                True,
                upload_id,
                documents_id=None,
            )
        else:
            bucket_name = s3location
            location_name = ""
            repo = KnowledgeGraphRepositoryV2(db)
            background_tasks.add_task(
                repo.process_data,
                bucket_name,
                location_name,
                user.get("cognito:username"),
                user.get("custom:county"),
                user.get("name"),
                False,
                False,
                upload_id,
                documents_id=None,
            )
        return {"status": "ok", "message": "extract running in background"}
    except Exception as e:
        print('error------------------', e)
