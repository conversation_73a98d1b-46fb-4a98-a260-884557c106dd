from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from app.utils.notification_utils import (
    get_all_notifications,
    create_notification,
    update_notification_read_status,
)
from app.utils.auth_utils import get_current_user

router = APIRouter(
    tags=["notification"],
    responses={404: {"description": "Not found"}},
)


@router.post("/notification")
def post_new_notification(
    title: str, description: str, url: str, user=Depends(get_current_user),
):
    insert_id, notification = create_notification(
        user["cognito:username"], title, description, url,
    )
    return {"insert_id": str(insert_id), "notification": notification}


@router.get("/notifications")
def get_all_user_notifications(user=Depends(get_current_user)):
    notifications = get_all_notifications(user["cognito:username"])
    return {"total_notifications": len(notifications), "notifications": notifications}


@router.patch("/notification")
def update_notification(
    notification_id: str = "", read_status: bool = True, user=Depends(get_current_user),
):
    result = update_notification_read_status(
        user["cognito:username"], notification_id=notification_id, read=read_status,
    )
    if result.acknowledged and result.raw_result['n'] > 0:
        if result.raw_result['nModified'] > 0:
            return JSONResponse(status_code=200, content={"message": "Successfully updated"})
        else:
            return JSONResponse(status_code=200, content={"message": "No changes were made; the document was already up-to-date."})
    else:
        return JSONResponse(status_code=404, content={"message": "Update failed or no documents matched the query."})
