from opentelemetry import trace
from fastapi import APIRouter, Query, Depends
from app.utils.base_utils import get_latest_n_messages_from_chat, create_new_message
from app.crud.inspector_repository import InspectorRepository
from app.core.lightragv2.lightrag_fine_tuned import CustomRag
from lightrag.lightrag import QueryParam
from fastapi.responses import StreamingResponse
import json
from app.utils.auth_utils import get_current_user

_REPO = InspectorRepository()

router = APIRouter(
    prefix="/inspector",
    tags=["inspector"],
    responses={404: {"description": "Not found"}},
)

lightrag_trace = trace.get_tracer("lightrag")


@router.post("/chat/v2")
def chat_v2(message: str):
    # return _REPO.inspector(message, [])
    return _REPO.inspector(
        message,
        [
            {
                "human": "Hello, I am Ajay",
                "ai": {
                    "answer": "Hello Ajay! How can I assist you today? If you have any questions or need information from the Neo4j graph database, please let me know!",
                    "structured_content": [],
                },
            },
        ],
    )


# @router.post("/chat/v3")
# def chat_v3(message: str, mode: str = Query(
#         "local",
#         title="Mode",
#         description="Select a mode",
#         enum=["naive", "local", "global", "hybrid"],
#     )):
#     rag = CustomRag()
#     resp = rag.query(message, param=QueryParam(mode=mode, only_need_context=True),)
#     print(resp)
#     return resp


@router.post("/chat/v3")
async def chat_v3(
    message: str,
    application_id: str,
    chat_id: str = None,
    model_name: str = "gpt-4o-mini",
    mode: str = Query(
        "local",
        title="Mode",
        description="Select a mode",
        enum=["naive", "local", "global", "hybrid"],
    ),
    user=Depends(get_current_user),
) -> StreamingResponse:
    with trace.get_tracer(__name__).start_as_current_span("chat_v3"):
        history, chat_id, chat_title = get_latest_n_messages_from_chat(
            chat_id,
            user["cognito:username"],
            application_id=application_id,
        )

        async def generate_response():
            with trace.get_tracer(__name__).start_as_current_span("chat_completion") as span:
                span.set_attribute("chat_id", chat_id)
                span.set_attribute("application_id", application_id)
                span.set_attribute("model_name", model_name)
                span.set_attribute("mode", mode)
                rag = CustomRag()
                final_response = None
                async for resp in rag.stream_query(message, param=QueryParam(mode=mode, only_need_context=True, top_k=5)):
                    format_response = {"content": resp, "chat_id": chat_id}
                    yield f"data: {json.dumps(format_response)}\n\n"
                    final_response = resp
                if "answer" in final_response and "structured_content" in final_response:
                    answer = final_response["answer"]
                    content = final_response["structured_content"]
                    create_new_message(chat_id, answer, message, content, [])

        return StreamingResponse(
            generate_response(),
            media_type="text/event-stream",
        )
