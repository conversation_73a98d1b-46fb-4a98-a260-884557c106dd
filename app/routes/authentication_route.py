import boto3
from fastapi import APIRouter, HTTPException
from botocore.exceptions import ClientError, ParamValidationError
from app.core.config import Settings
from fastapi import Query, Body, BackgroundTasks
from app.models.auth_model import CognitoUser, SignUpUser
from app.core.telemetry import get_logger
from opentelemetry import trace

settings = Settings()

_SHOW_NAME = "auth"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}},
)
logger = get_logger("pasco.ai.connector")
tracer = trace.get_tracer(__name__)

client = boto3.client(
    "cognito-idp",
    region_name=settings.AWS_REGION,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
)
user_pool_id = settings.AWS_USER_POOL_ID
client_id = settings.AWS_COGNITO_APP_CLIENT_ID


@router.post("/login", summary="Login for existing users")
async def login(user: CognitoUser):
    with tracer.start_as_current_span("user_login_application") as span:
        logger.info(f"Logging in user: {user.email}")
        try:
            response = client.initiate_auth(
                ClientId=client_id,
                AuthFlow='USER_PASSWORD_AUTH',
                AuthParameters={
                    'USERNAME': user.email,
                    'PASSWORD': user.password,
                },
            )
            id_token = response['AuthenticationResult'].pop('IdToken')
            refresh_token = response['AuthenticationResult'].pop('RefreshToken')

            response_to_return = {
                "message": "Login successful",
                "id_token": id_token,
                "refresh_token": refresh_token,
                **response["AuthenticationResult"],
            }
            return response_to_return

        except client.exceptions.NotAuthorizedException as e:
            span.record_exception(e)
            logger.error(f"Invalid username or password for user: {user.email}")
            raise HTTPException(status_code=401, detail="Invalid username or password")
        except Exception as e:
            span.record_exception(e)
            logger.error(f"Error logging in user: {user.email}, error: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))


@router.post("/signup", summary="Sign up a new user")
async def signup(user: SignUpUser):
    try:
        response = client.sign_up(
            ClientId=client_id,
            Username=user.email,
            Password=user.password,
            UserAttributes=[
                {"Name": "email", "Value": user.email},
                {"Name": "name", "Value": user.name},
                {"Name": "custom:county", "Value": user.county},
                {"Name": "custom:role", "Value": "enforcer"},
            ],
            ValidationData=[  # Optionally add validation data (e.g., email verification)
                {"Name": "email", "Value": user.email},
            ],
        )
        return {
            "message": "User created successfully. Verification code sent.",
            "user_sub": response["UserSub"],
        }
    except client.exceptions.UsernameExistsException:
        raise HTTPException(status_code=400, detail="Username (email) already exists")
    except client.exceptions.InvalidPasswordException:
        raise HTTPException(
            status_code=400,
            detail="Invalid password. Password must meet Cognito requirements.",
        )
    except ParamValidationError as pve:  # More specific error for parameter validation
        raise HTTPException(
            status_code=400, detail=f"Parameter validation error: {pve}"
        )
    except ClientError as ce:  # General boto3 ClientError for other AWS issues
        raise HTTPException(status_code=500, detail=f"Cognito API error: {ce}")
    except Exception as e:  # Catch-all for unexpected errors
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")


@router.post("/confirm_signup")
async def confirm_signup(
    username: str = Query(..., description="The user's email address (username)"),
    confirmation_code: str = Query(
        ..., description="The confirmation code sent to the user's email"
    ),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    try:
        client.confirm_sign_up(
            ClientId=client_id,
            Username=username,
            ConfirmationCode=confirmation_code,
        )
        #  In Backgroud, we can add user to the database
        return {"message": "Signup confirmed successfully."}

    except client.exceptions.CodeMismatchException:
        raise HTTPException(status_code=400, detail="Invalid confirmation code.")
    except client.exceptions.ExpiredCodeException:
        raise HTTPException(status_code=400, detail="Confirmation code has expired.")
    except ClientError as e:
        raise HTTPException(
            status_code=400, detail=e.response["Error"]["Message"]
        )  # More informative error message
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/resend_confirmation_code")
async def resend_confirmation_code(
    username: str = Query(..., description="The user's email address (username)"),
):
    try:
        client.resend_confirmation_code(
            ClientId=client_id,
            Username=username,
        )
        return {"message": "Confirmation code resent successfully."}
    except client.exceptions.LimitExceededException:
        raise HTTPException(
            status_code=429, detail="Too many requests. Please try again later."
        )
    except client.exceptions.UserNotFoundException:
        raise HTTPException(status_code=404, detail="User not found.")
    except ClientError as e:
        raise HTTPException(
            status_code=400, detail=e.response["Error"]["Message"]
        )  # More informative error message
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/forgot_password")
async def forgot_password(
    email: str = Query(..., description="The user's email address"),
):
    try:
        client.forgot_password(
            ClientId=client_id,
            Username=email,
        )
        return {"message": "Password reset code sent to your email."}
    except client.exceptions.UserNotFoundException:
        raise HTTPException(status_code=404, detail="User not found.")
    except client.exceptions.LimitExceededException:
        raise HTTPException(
            status_code=429, detail="Too many requests. Please try again later."
        )
    except ClientError as e:  # Catch boto3 errors for better debugging
        raise HTTPException(status_code=400, detail=e.response["Error"]["Message"])
    except Exception as e:  # General exception catch
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/confirm_forgot_password")
async def confirm_forgot_password(
    user: CognitoUser,
    confirmation_code: str = Query(..., description="Code from email"),
):
    try:
        client.confirm_forgot_password(
            ClientId=client_id,
            Username=user.email,
            ConfirmationCode=confirmation_code,
            Password=user.password,
        )
        return {"message": "Password reset successful."}
    except client.exceptions.CodeMismatchException:
        raise HTTPException(status_code=400, detail="Invalid confirmation code.")
    except client.exceptions.ExpiredCodeException:
        raise HTTPException(status_code=400, detail="Confirmation code has expired.")
    except ClientError as e:
        raise HTTPException(status_code=400, detail=e.response["Error"]["Message"])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/refresh_token", summary="Refresh expired ID Token using refresh token")
async def refresh_token(refresh_token: str = Body(..., embed=True)):
    try:
        # No need to fetch from session since it's now in the body
        if not refresh_token:
            raise HTTPException(status_code=400, detail="Refresh token not found.")

        response = client.initiate_auth(
            ClientId=client_id,
            AuthFlow="REFRESH_TOKEN_AUTH",
            AuthParameters={
                "REFRESH_TOKEN": refresh_token,
            },
        )

        id_token = response["AuthenticationResult"]["IdToken"]

        # (Optionally) You might want to update the session's refresh token here if Cognito issues a new one

        return {"message": "ID token refreshed successfully.", "id_token": id_token}
    except client.exceptions.NotAuthorizedException:
        raise HTTPException(status_code=401, detail="Invalid refresh token.")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/logout")
async def logout(
    user_id: str,
    refresh_token: str = Body(..., embed=True, default_factory=""),
):
    """Logs out a user by invalidating their tokens."""
    try:
        # Globally sign out to invalidate all tokens (using ID token)
        client.admin_user_global_sign_out(
            UserPoolId=user_pool_id,
            Username=user_id,
        )

        # Revoke the refresh token (optional, but recommended for security)

        client.revoke_token(
            Token=refresh_token,
            ClientId=client_id,
        )

        return {"message": "Logout successful. ID and refresh tokens invalidated."}

    except client.exceptions.InvalidParameterException as e:
        raise HTTPException(status_code=400, detail=f"Invalid parameter: {e}")

    except client.exceptions.NotAuthorizedException:
        raise HTTPException(
            status_code=401, detail="Unauthorized: ID token may be invalid or expired."
        )

    except ClientError as ce:
        raise HTTPException(status_code=500, detail=f"Cognito API error: {ce}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")
