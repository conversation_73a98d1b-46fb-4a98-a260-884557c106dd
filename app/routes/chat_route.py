from fastapi import APIRout<PERSON>, Depends, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.responses import StreamingResponse, JSONResponse
from app.utils.streaming_decorator import stream_generator
from app.utils.async_streaming_decorator import async_stream_generator
from app.core.config import settings
from app.utils.auth_utils import get_current_user
from app.utils.llm_interface import LLMInterface
from app.crud.chat_repository import ChatRepository
from app.database.neo4j_db import Neo4jDB
from app.crud.knowledge_graph_v2_repository import KnowledgeGraphRepositoryV2
from app.crud.mongo.base_repository import BaseRepository
from pymongo import MongoClient

import traceback
import json
import re
import tempfile
import boto3
import os
import asyncio
import uuid
import queue
from datetime import datetime
from typing import List, Optional
from collections import defaultdict

from agno.agent import Agent
from agno.models.openai import OpenAIChat
from pymilvus import Collection, connections
from voyageai import Client as VoyageClient

from app.core.COADelete import COADelete
from app.core.COAEdit import COAEdit
from app.core.interactive_discussion import DiscussionController
from app.core.telemetry.chat_logger import get_chat_logger

logger = get_chat_logger("pasco.ai.chat")


# from app.core.lightragv2.lightrag_fine_tuned import CustomRag
# from app.database.neo4j_db import Neo4jDB
# import json

router = APIRouter(
    tags=["chat"],
    responses={404: {"description": "Not found"}},
)


@router.post("/chat/v2")
@stream_generator
def chat_v2(
    message: str,
    chat_id: str = None,
    user=Depends(get_current_user),
    model_name: str = "gpt-4o-mini",
):
    llm_interface = LLMInterface(
        api_key=settings.OPENAI_API_KEY,
        model_name=model_name,
        instance_name="chat_v2",
    )
    conversation = llm_interface.chat_interaction_wrapper(
        query=message,
        user=user,
        chat_id=chat_id,
    )
    return conversation


@router.post("/extraction_chat/v3")
@async_stream_generator
async def interactive_chat_module(
    message: str = None,
    chat_type: str = None,
    chat_id: str = None,
    user=Depends(get_current_user),
    stream: bool = True,
    discussion_type=None,
):
    try:
        logger.set_chat_id(str(chat_id) if chat_id else "unknown")
        logger.info(f"[chat_module] message---->{message}")
        discussion = DiscussionController(
            chat_id=chat_id,
            chat_type=chat_type,
            message=message,
            stream=stream,
            user_id=user["cognito:username"],
            user=user,
            discussion_type=discussion_type,
        )
        result = await discussion.init_discussion()
        return result

    except Exception as e:
        error_trace = "".join(traceback.format_exc())
        logger.error(error_trace)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/chat/v3sample")
async def chat_v3sample():
    import asyncio

    async def event_stream():
        for i in range(10):
            yield f"data: {{\"message\": \"hello world {i + 1}\"}}\n\n"
            await asyncio.sleep(2)
    return StreamingResponse(event_stream(), media_type="text/event-stream")


@router.post("/chat/v3")
@async_stream_generator
async def interactive_chat(
    message: str = None,
    chat_type: str = None,
    chat_id: str = None,
    user=Depends(get_current_user),
    stream: bool = True,
    discussion_type=None,
):
    try:
        logger.set_chat_id(str(chat_id) if chat_id else "unknown")
        print("-----------------------------------------------------------Chat V3")
        logger.info(f"message---->{message}")
        discussion = DiscussionController(
            chat_id=chat_id,
            chat_type=chat_type,
            message=message,
            stream=stream,
            user_id=user["cognito:username"],
            user=user,
            discussion_type=discussion_type,
        )
        result = await discussion.init_discussion()
        print("result---->", result)
        return result

    except Exception as e:
        error_trace = "".join(traceback.format_exc())
        logger.error(error_trace)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


class SimpleDocument:
    def __init__(self, content: str, metadata: dict):
        self.page_content = content
        self.metadata = metadata

    def to_dict(self):
        return {
            "page_content": self.page_content,
            "metadata": self.metadata
        }


class MilvusKnowledgeWrapper:
    def __init__(self, milvus_config, voyage_api_key: str):
        self.milvus_config = milvus_config
        self.voyage_client = VoyageClient(api_key=voyage_api_key)
        connections.connect(
            alias="default",
            token=milvus_config.get("token"),
            uri=milvus_config.get("uri"),
            db_name=milvus_config.get("db_name"),
        )

    def _embed_query(self, query: str) -> List[float]:
        """Generate embedding for the query using Voyage AI."""
        response = self.voyage_client.embed([query], model="voyage-3-large")
        return response.embeddings[0]

    def search(self, query: str, **kwargs) -> List[SimpleDocument]:
        limit = kwargs.get('num_documents', 3)
        try:
            limit = int(limit) if limit is not None else 10
            limit = max(1, min(limit, 20))
        except (TypeError, ValueError):
            limit = 10
        query_embedding = self._embed_query(query)
        target_filename = self._extract_filename_from_query(query)
        target_section = self._extract_section(query)

        collection = Collection("Mpud_knowledge")
        collection.load()

        # Search parameters
        search_params = {
            "metric_type": "COSINE",  # Inner Product for cosine similarity
            "params": {"nprobe": 1024}
        }

        results = collection.search(
            data=[query_embedding],
            anns_field="embedding",
            param=search_params,
            limit=70,
            output_fields=["content", "source", "page", "doc_id", "section"]
        )  # results[0] contains the hits

        documents = []
        for hits in results:
            for hit in hits:
                # Safely access entity properties
                entity = hit.entity
                properties = {
                    "content": entity.content if hasattr(entity, 'content') else "",
                    "source": entity.source if hasattr(entity, 'source') else "",
                    "page": entity.page if hasattr(entity, 'page') else 0,
                    "doc_id": entity.doc_id if hasattr(entity, 'doc_id') else "",
                    "section": entity.section if hasattr(entity, 'section') else ""
                }

                # Filter based on filename
                if target_filename:
                    normalized_query = re.sub(r'[^a-z0-9]', '', target_filename.lower())
                    normalized_source = re.sub(r'[^a-z0-9]', '', properties["source"].lower())
                    if normalized_query not in normalized_source:
                        print(f"Skipping - filename mismatch: {properties['source']}")
                        continue

                # Filter based on section
                if target_section and properties["section"]:
                    if target_section.lower() != properties["section"].lower():
                        print(f"Skipping - section mismatch: {properties['section']}")
                        continue

                documents.append(SimpleDocument(
                    content=properties["content"],
                    metadata={
                        "source": properties["source"],
                        "page": properties["page"],
                        "doc_id": properties["doc_id"],
                        "section": properties["section"]
                    }
                ))

                if len(documents) >= limit:
                    break

        return documents

    def _extract_filename_from_query(self, query: str) -> Optional[str]:
        """
        Robust filename extraction from user queries with proper regex patterns.
        Handles cases like "301 East" and "RiverRidge MPUD file".
        """
        print(f"Raw query input: '{query}'")
        query = query.strip()

        patterns = [
            # Pattern for "under/in/about [filename]"
            r'\b(?:under|in|about|from|on|of|for|regarding)\s+([a-zA-Z0-9\s\-]+?)(?=\s+(?:mpud|file|document|section|area|report|plan)\b|\s*$)',

            # Pattern for "[filename] MPUD/file/document"
            r'\b([a-zA-Z0-9\s\-]+?)\s+mpud(?:\s*(?:file|document)?)?\b',

            # Standalone pattern for numbers followed by words
            r'\b([\d]+\s+[A-Z][a-z]+)\b',

            # Simple number (fallback)
            r'\b(\d+)\b',

            # Capitalized name fallback (e.g., RiverRidge)
            r'\b([A-Z][a-zA-Z0-9]+(?:\s+[A-Z][a-zA-Z0-9]+)*)\b'
        ]

        for pattern in patterns:
            try:
                match = re.search(pattern, query, re.IGNORECASE)
                if match:
                    filename = match.group(1).strip()
                    filename = re.sub(r'[^a-zA-Z0-9\s\-]', '', filename)
                    filename = re.sub(r'\s+', ' ', filename)
                    return filename.title()
            except re.error as e:
                print(f"Regex error in pattern '{pattern}': {str(e)}")
                continue

        print("No filename patterns matched")
        return None

    def _extract_section(self, query: str) -> Optional[str]:
        section_keywords = {
            "environmental": "Environmental",
            "transportation": "Transportation/Circulation",
            "open space": "Open Space/Buffering",
            "utilities": "Utilities/Water Service/Wastewater Disposal",
            "stormwater": "Stormwater",
            "land use": "Land Use",
            "access management": "Access Management",
            "dedication of right-of-way": "Dedication of Right-of-Way",
            "design": "Design/Construction Specifications",
            "construction": "Design/Construction Specifications",
            "emergency management": "Emergency Management",
            "procedures": "Procedures",
            "general": "General",
            "basic": "General",
            "intro": "General",
            "overview": "General",
            "summary": "General"
        }
        query_lower = query.lower()
        for key, value in section_keywords.items():
            if key in query_lower:
                return value
        return None


@router.post("/chat/v2/ai-expert")
async def ai_expert_chat(
    message: str,
    chat_id: str = None,
    user=Depends(get_current_user),
):
    async def generate():
        try:
            milvus_config = {
                "token": settings.MILVUS_TOKEN,
                "uri": settings.MILVUS_URI,
                "db_name": settings.MILVUS_DEFAULT_DATABASE,
            }
            knowledge = MilvusKnowledgeWrapper(
                milvus_config=milvus_config,
                voyage_api_key=settings.VOYAGE_API_KEY
            )

            agent = Agent(
                name="Mpud Docs Expert",
                model=OpenAIChat(
                    api_key=settings.OPENAI_API_KEY,
                    id="gpt-4o",
                    max_tokens=16000,
                ),
                knowledge=knowledge,
                instructions="You are an expert in MPUD documents. Answer the questions based on the content of the documents. Do not manipulate the original query, pass it to the search function as it is. The user might add 'mpud file' as a suffix to query, you may ignore that. When the query of the user is very basic or does not contain file name or you did not find any search results, dont reply as i dont have the info, instead analyze the knowledge base as a whole and reply as point wise.",
            )

            response_stream = agent.run(message, stream=True)

            for chunk in response_stream:
                formatted_chunk = {
                    'role': 'assistant',
                    'content': chunk.content,
                    'is_stream_complete': False
                }
                yield f"data: {json.dumps(formatted_chunk)}\n\n"

            # Send final complete message
            final_chunk = {
                'role': 'assistant',
                'content': '',
                'is_stream_complete': True
            }
            yield f"data: {json.dumps(final_chunk)}\n\n"

        except Exception as e:
            # Handle errors
            error_chunk = {
                'role': 'assistant',
                'content': f"Error occurred: {str(e)}",
                'is_stream_complete': True
            }
            yield f"data: {json.dumps(error_chunk)}\n\n"

    # Return a streaming response
    return StreamingResponse(
        generate(),
        media_type="text/event-stream"
    )


# To test the lightrag by passsing textract and all
# @router.post("/chat/test")
# async def test_rag():
#     try:
#         with open('tests/data/textract_resp.json') as f:
#             layout_data = json.load(f)

#         rag = CustomRag()
#         await rag.ainsert(layout_data[0]["layout_data"], "test-mannual")
#         return {"status": "ok"}
#     except Exception:
#         print(traceback.format_exc())


@router.delete("/v2/discussion/chunk")
async def delete_discussion_chunk(
    chat_id: int,
    discussion_type: str,
    target_chunk_id: str,

    user=Depends(get_current_user),
):
    """
    Delete a specific chunk from a discussion.
    """
    try:
        result = COADelete.run_query(
            chat_id=chat_id,
            discussion_type=discussion_type,
            target_chunk_id=target_chunk_id
        )

        return {"status": "success", "message": result}

    except Exception as e:
        error_trace = "".join(traceback.format_exc())
        print(error_trace)
        raise HTTPException(status_code=500, detail=f"Error deleting chunk: {str(e)}")


@router.post("/v2/discussion/chunk")
async def edit_discussion_chunk(
    chat_id: int,
    discussion_type: str,
    target_chunk_id: str,
    chat_message: str,
    user=Depends(get_current_user),
):
    try:
        result = COAEdit.run_query(
            chat_id=chat_id,
            discussion_type=discussion_type,
            target_chunk_id=target_chunk_id,
            chat_message=chat_message
        )

        if result["status"] == "error":
            raise HTTPException(status_code=404, detail=result["message"])

        return result

    except Exception as e:
        error_trace = "".join(traceback.format_exc())
        logger.error(error_trace)
        raise HTTPException(status_code=500, detail=f"Error updating chunk: {str(e)}")


class ProgressReporter:
    def __init__(self):
        self.messages = queue.Queue()

    def report(self, status, message):
        self.messages.put({"status": status, "message": message})

    def get_message(self):
        return self.messages.get()


@router.post("/{project_id}/extract_mpud")
async def extract_mpud_coa(
    project_id: str,
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    # Generate unique asset ID for this upload
    asset_id = str(uuid.uuid4())

    # Validate file
    if not file.filename:
        return JSONResponse(
            status_code=400,
            content={"status": "error", "message": "No file provided"}
        )

    # Check file extension
    if not file.filename.lower().endswith('.pdf'):
        return JSONResponse(
            status_code=400,
            content={"status": "error", "message": "Only PDF files are supported"}
        )

    # Validate project_id
    if not project_id:
        return JSONResponse(
            status_code=400,
            content={"status": "error", "message": "Project ID is required"}
        )

    # Create temporary file immediately (but defer reading to background)
    try:
        # Create temp file and copy the upload content
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp:
            content = await file.read()
            if not content or len(content) == 0:
                return JSONResponse(
                    status_code=400,
                    content={"status": "error", "message": "Empty file content"}
                )
            tmp.write(content)
            tmp.flush()
            tmp_path = tmp.name
    except Exception as file_error:
        logger.error(f"File processing error: {str(file_error)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"Failed to process file: {str(file_error)}"}
        )

    # Configure S3 paths
    s3_bucket = f"pasco-ocr-files-{settings.STAGE}"
    s3_key = f"uploads/{asset_id}_{file.filename}"  # Use asset_id in S3 key for uniqueness
    filename = file.filename

    # Start the background task immediately (with temp file path)
    background_tasks.add_task(
        process_mpud_extraction_background_with_temp_file,
        project_id,
        asset_id,
        tmp_path,
        filename,
        len(content),
        s3_bucket,
        s3_key,
        mongo_db_name=settings.MONGO_DEFAULT_DATABASE
    )

    # Return success response immediately with asset_id for tracking
    return JSONResponse(
        status_code=200,
        content={
            "status": "success",
            "message": "MPUD file extraction started",
            "project_id": project_id,
            "asset_id": asset_id,
            "filename": filename,
            "extraction_status": "started"
        }
    )


@router.get("/{project_id}/extraction_status_stream")
async def stream_extraction_status(
    project_id: str,
    user=Depends(get_current_user)
):
    """Stream the extraction status for a project_id, updating every 1 second if processing"""

    async def status_stream():
        mongo_client = None
        try:
            # Initialize MongoDB connection and variables
            mongo_client = MongoClient(settings.MONGO_URL)
            mongo_db = mongo_client[settings.MONGO_DEFAULT_DATABASE]
            extraction_repo = BaseRepository("extraction")

            # Initialize tracking variables
            found_records = False
            last_state = {
                "status": None,
                "progress": None,
                "message": None,
                "updated_at": None
            }

            # Add timeout mechanism
            start_time = datetime.now()
            max_duration = 30 * 60  # 30 minutes timeout
            stall_timeout = 300  # 5 minutes stall detection

            while True:
                try:
                    # Check for overall timeout
                    current_duration = (datetime.now() - start_time).total_seconds()
                    if current_duration > max_duration:
                        timeout_msg = {
                            'status': 'error',
                            'message': f'Extraction timeout after {max_duration // 60} minutes',
                            'error_type': 'timeout'
                        }
                        yield f"data: {json.dumps(timeout_msg)}\n\n"
                        break

                    # Fetch current status with explicit fields
                    try:
                        logger.debug(f"Querying MongoDB for project_id: {project_id}")
                        results = extraction_repo.get_all_by_projection(
                            querry={"project_id": project_id},  # Using 'querry' to match the parameter name
                            project={
                                "_id": 0,
                                "status": 1,
                                "progress": 1,
                                "message": 1,
                                "updated_at": 1,
                                "error_type": 1,
                                "error_trace": 1,
                                "result": 1
                            },
                            db=mongo_db,
                            limit=1,
                            sort_key="updated_at",
                            sort_direction=-1
                        )
                        logger.debug(f"MongoDB query returned {len(results) if results else 0} results")
                    except TypeError as type_error:
                        # Handle parameter mismatch error
                        logger.error(f"Database query parameter error: {str(type_error)}")
                        error_msg = {
                            'status': 'error',
                            'message': 'Database query configuration error',
                            'error_type': 'config_error',
                            'error_detail': str(type_error)
                        }
                        yield f"data: {json.dumps(error_msg)}\n\n"
                        break
                    except Exception as query_error:
                        logger.error(f"MongoDB query error: {str(query_error)}")
                        error_msg = {
                            'status': 'error',
                            'message': f'Database query failed: {str(query_error)}',
                            'error_type': 'query_error',
                            'error_detail': str(query_error)
                        }
                        yield f"data: {json.dumps(error_msg)}\n\n"
                        break

                    # Handle no results case
                    if not results:
                        if not found_records:
                            not_found_msg = {
                                'status': 'not_found',
                                'message': 'No extraction job found for this project',
                                'project_id': project_id
                            }
                            yield f"data: {json.dumps(not_found_msg)}\n\n"
                            break
                        await asyncio.sleep(1)
                        continue

                    found_records = True
                    current_status = results[0]
                    current_time = datetime.now()
                    current_status["stream_timestamp"] = current_time.isoformat()

                    # Extract current state
                    current_state = {
                        "status": current_status.get("status"),
                        "progress": current_status.get("progress"),
                        "message": current_status.get("message"),
                        "updated_at": current_status.get("updated_at")
                    }

                    # Check for stalled process
                    if current_state["updated_at"]:
                        try:
                            last_update_time = datetime.fromisoformat(
                                current_state["updated_at"].replace('Z', '+00:00')
                            )
                            stall_duration = (current_time - last_update_time).total_seconds()

                            if (stall_duration > stall_timeout
                                    and current_state["status"] in ["processing", "uploading"]):
                                stall_msg = {
                                    'status': 'error',
                                    'message': f'Process stalled at {current_state["progress"]}% for {stall_duration // 60:.1f} minutes',
                                    'error_type': 'stall',
                                    'last_status': current_state["status"],
                                    'last_progress': current_state["progress"]
                                }
                                yield f"data: {json.dumps(stall_msg)}\n\n"
                                break
                        except (ValueError, TypeError) as e:
                            logger.error(f"Error parsing timestamp: {e}")

                    # Handle error status
                    if current_state["status"] == "error":
                        error_response = {
                            'status': 'error',
                            'message': current_state["message"],
                            'error_type': current_status.get("error_type", "unknown"),
                            'error_trace': current_status.get("error_trace"),
                            'progress': current_state["progress"]
                        }
                        yield f"data: {json.dumps(error_response)}\n\n"
                        break

                    # Send updates on change
                    should_send_update = any(
                        last_state[key] != current_state[key]
                        for key in last_state
                    )

                    if should_send_update:
                        logger.info(
                            f"[STREAM] Update: status={current_state['status']}, "
                            f"progress={current_state['progress']}, "
                            f"message='{current_state['message']}'"
                        )
                        yield f"data: {json.dumps(current_status)}\n\n"
                        last_state.update(current_state)

                    # Check for completion
                    if current_state["status"] not in ["processing", "uploading"]:
                        completion_response = {
                            'status': 'stream_complete',
                            'message': 'Processing completed',
                            'final_status': current_state["status"],
                            'final_progress': current_state["progress"],
                            'result': current_status.get("result")
                        }
                        yield f"data: {json.dumps(completion_response)}\n\n"
                        break

                    await asyncio.sleep(1)

                except Exception as fetch_error:
                    logger.error(f"Status fetch error: {str(fetch_error)}")
                    logger.error(traceback.format_exc())

                    fetch_error_msg = {
                        'status': 'error',
                        'message': f'Error fetching status: {str(fetch_error)}',
                        'error_type': 'fetch_error',
                        'error_detail': traceback.format_exc()
                    }
                    yield f"data: {json.dumps(fetch_error_msg)}\n\n"
                    break  # Exit on error instead of retrying

        except Exception as e:
            logger.error(f"Stream error: {str(e)}")
            logger.error(traceback.format_exc())
            stream_error_msg = {
                'status': 'error',
                'message': f'Streaming error: {str(e)}',
                'error_type': 'stream_error',
                'error_detail': traceback.format_exc()
            }
            yield f"data: {json.dumps(stream_error_msg)}\n\n"

        finally:
            if mongo_client:
                try:
                    mongo_client.close()
                except Exception as close_error:
                    logger.error(f"MongoDB connection close error: {str(close_error)}")

    return StreamingResponse(
        status_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
            "X-Accel-Buffering": "no"  # Disable proxy buffering
        }
    )


def check_mongodb_connection(mongo_client, db_name: str):
    """
    Check if MongoDB connection is healthy and reconnect if necessary
    """
    try:
        # Ping the database to check connection
        mongo_client.admin.command('ping')
        logger.debug("MongoDB connection is healthy")
        return True
    except Exception as e:
        logger.error(f"MongoDB connection check failed: {str(e)}")
        return False


async def update_extraction_status_with_retry(
    extraction_repo,
    asset_id: str,
    project_id: str,
    status_update: dict,
    db,
    mongo_client=None,
    max_retries: int = 3
):
    """
    Update extraction status with retry logic to handle MongoDB connection issues
    """
    for attempt in range(max_retries):
        try:
            # Check connection health before retry
            if attempt > 0 and mongo_client:
                connection_healthy = check_mongodb_connection(mongo_client, db.name)
                if not connection_healthy:
                    logger.warning(f"MongoDB connection unhealthy on retry attempt {attempt + 1}")

            result = extraction_repo.update_one_by_query_upsert(
                {"asset_id": asset_id, "project_id": project_id},
                status_update,
                db
            )
            logger.info(f"Successfully updated status: {status_update.get('message', '')} - Progress: {status_update.get('progress', 'N/A')}%")
            return result
        except Exception as e:
            logger.error(f"MongoDB update attempt {attempt + 1} failed: {str(e)}")
            if attempt == max_retries - 1:
                logger.error(f"Failed to update status after {max_retries} attempts: {status_update}")
                raise e
            await asyncio.sleep(2 ** attempt)  # Exponential backoff


async def process_mpud_extraction_background_with_temp_file(
    project_id: str,
    asset_id: str,
    tmp_path: str,
    filename: str,
    file_size_bytes: int,
    s3_bucket: str,
    s3_key: str,
    mongo_db_name: str
):
    """Complete MPUD extraction processing using temp file"""

    # Initialize MongoDB client and repositories
    mongo_client = MongoClient(settings.MONGO_URL)
    db = mongo_client[mongo_db_name]
    extraction_repo = BaseRepository("extraction")

    try:
        # Step 1: Initialize MongoDB record
        try:
            initial_document = {
                "asset_id": asset_id,
                "project_id": project_id,
                "filename": filename,
                "s3_bucket": s3_bucket,
                "s3_key": s3_key,
                "status": "uploading",
                "progress": 5,
                "message": "Starting file upload and processing",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "file_size_bytes": file_size_bytes,
            }
            extraction_repo.create(initial_document, db)
            logger.info(f"Successfully created initial MongoDB document for asset_id: {asset_id}")
        except Exception as mongo_error:
            logger.error(f"MongoDB initialization error: {str(mongo_error)}")
            raise ValueError(f"Failed to initialize database record: {str(mongo_error)}")

        # Step 2: Upload to S3
        extraction_repo.update_one_by_query_upsert(
            {"asset_id": asset_id, "project_id": project_id},
            {
                "status": "uploading",
                "message": "Uploading file to S3.",
                "progress": 10,
                "updated_at": datetime.now().isoformat(),
            },
            db
        )

        try:
            s3_client = boto3.client("s3")
            s3_client.upload_file(tmp_path, s3_bucket, s3_key)
            logger.info(f"Successfully uploaded file to S3: {s3_bucket}/{s3_key}")
        except Exception as s3_error:
            logger.error(f"S3 upload error: {str(s3_error)}")
            raise ValueError(f"Failed to upload file to S3: {str(s3_error)}")
        finally:
            # Clean up temp file after upload
            if tmp_path and os.path.exists(tmp_path):
                try:
                    os.remove(tmp_path)
                except Exception as cleanup_error:
                    logger.error(f"Error removing temp file: {str(cleanup_error)}")

        # Step 3: Update status and continue processing
        extraction_repo.update_one_by_query_upsert(
            {"asset_id": asset_id, "project_id": project_id},
            {
                "status": "processing",
                "message": "File uploaded to S3. Starting extraction.",
                "progress": 20,
                "updated_at": datetime.now().isoformat(),
            },
            db
        )

        # Continue with existing processing logic
        await process_mpud_extraction_background(
            project_id, asset_id, filename, s3_bucket, s3_key, mongo_db_name
        )

    except Exception as e:
        logger.error(f"Error in temp file background extraction: {str(e)}")
        logger.error(traceback.format_exc())

        # Update MongoDB with error status (or create if initial creation failed)
        try:
            extraction_repo.update_one_by_query_upsert(
                {"asset_id": asset_id, "project_id": project_id},
                {
                    "status": "error",
                    "message": str(e),
                    "error_type": type(e).__name__,
                    "error_trace": traceback.format_exc(),
                    "updated_at": datetime.now().isoformat(),
                },
                db
            )
        except Exception as error_update_error:
            logger.error(f"Failed to update error status in MongoDB: {str(error_update_error)}")

        # Clean up temp file if error occurs
        if tmp_path and os.path.exists(tmp_path):
            try:
                os.remove(tmp_path)
            except Exception as cleanup_error:
                logger.error(f"Error removing temp file after error: {str(cleanup_error)}")
    finally:
        # Close MongoDB connection
        if 'mongo_client' in locals():
            mongo_client.close()


async def process_mpud_extraction_background_complete(
    project_id: str,
    asset_id: str,
    content: bytes,
    filename: str,
    s3_bucket: str,
    s3_key: str,
    mongo_db_name: str
):
    """Complete MPUD extraction processing including all file operations"""

    # Initialize MongoDB client and repositories
    mongo_client = MongoClient(settings.MONGO_URL)
    db = mongo_client[mongo_db_name]
    extraction_repo = BaseRepository("extraction")
    tmp_path = None

    try:
        # Step 1: Validate file content (already read in main endpoint)
        if not content or len(content) == 0:
            raise ValueError("Empty file content")
        file_size_bytes = len(content)

        # Step 2: Initialize MongoDB record
        try:
            initial_document = {
                "asset_id": asset_id,
                "project_id": project_id,
                "filename": filename,
                "s3_bucket": s3_bucket,
                "s3_key": s3_key,
                "status": "uploading",
                "progress": 5,
                "message": "Starting file upload and processing",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "file_size_bytes": file_size_bytes,
            }
            extraction_repo.create(initial_document, db)
            logger.info(f"Successfully created initial MongoDB document for asset_id: {asset_id}")
        except Exception as mongo_error:
            logger.error(f"MongoDB initialization error: {str(mongo_error)}")
            raise ValueError(f"Failed to initialize database record: {str(mongo_error)}")

        # Step 3: Create temp file
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp:
                tmp.write(content)
                tmp.flush()
                tmp_path = tmp.name
        except Exception as tmp_error:
            logger.error(f"Temp file error: {str(tmp_error)}")
            raise ValueError(f"Failed to create temporary file: {str(tmp_error)}")

        # Step 4: Upload to S3
        extraction_repo.update_one_by_query_upsert(
            {"asset_id": asset_id, "project_id": project_id},
            {
                "status": "uploading",
                "message": "Uploading file to S3.",
                "progress": 10,
                "updated_at": datetime.now().isoformat(),
            },
            db
        )

        try:
            s3_client = boto3.client("s3")
            s3_client.upload_file(tmp_path, s3_bucket, s3_key)
            logger.info(f"Successfully uploaded file to S3: {s3_bucket}/{s3_key}")
        except Exception as s3_error:
            logger.error(f"S3 upload error: {str(s3_error)}")
            raise ValueError(f"Failed to upload file to S3: {str(s3_error)}")
        finally:
            # Clean up temp file after upload
            if tmp_path and os.path.exists(tmp_path):
                try:
                    os.remove(tmp_path)
                except Exception as cleanup_error:
                    logger.error(f"Error removing temp file: {str(cleanup_error)}")

        # Step 5: Update status and continue processing
        extraction_repo.update_one_by_query_upsert(
            {"asset_id": asset_id, "project_id": project_id},
            {
                "status": "processing",
                "message": "File uploaded to S3. Starting extraction.",
                "progress": 20,
                "updated_at": datetime.now().isoformat(),
            },
            db
        )

        # Continue with existing processing logic
        await process_mpud_extraction_background(
            project_id, asset_id, filename, s3_bucket, s3_key, mongo_db_name
        )

    except Exception as e:
        logger.error(f"Error in complete background extraction: {str(e)}")
        logger.error(traceback.format_exc())

        # Update MongoDB with error status (or create if initial creation failed)
        try:
            extraction_repo.update_one_by_query_upsert(
                {"asset_id": asset_id, "project_id": project_id},
                {
                    "status": "error",
                    "message": str(e),
                    "error_type": type(e).__name__,
                    "error_trace": traceback.format_exc(),
                    "updated_at": datetime.now().isoformat(),
                },
                db
            )
        except Exception as error_update_error:
            logger.error(f"Failed to update error status in MongoDB: {str(error_update_error)}")

        # Clean up temp file if error occurs
        if tmp_path and os.path.exists(tmp_path):
            try:
                os.remove(tmp_path)
            except Exception as cleanup_error:
                logger.error(f"Error removing temp file after error: {str(cleanup_error)}")
    finally:
        # Close MongoDB connection
        if 'mongo_client' in locals():
            mongo_client.close()


async def process_mpud_extraction_background_with_upload_and_init(
    project_id: str,
    asset_id: str,
    filename: str,
    tmp_path: str,
    s3_bucket: str,
    s3_key: str,
    file_size_bytes: int,
    mongo_db_name: str
):
    """Process MPUD extraction in the background including MongoDB initialization and S3 upload"""

    # Initialize MongoDB client and repositories
    mongo_client = MongoClient(settings.MONGO_URL)
    db = mongo_client[mongo_db_name]  # Get the actual database object
    extraction_repo = BaseRepository("extraction")

    try:
        # First, initialize extraction status in MongoDB
        try:
            # Insert initial document in the extraction collection
            initial_document = {
                "asset_id": asset_id,
                "project_id": project_id,
                "filename": filename,
                "s3_bucket": s3_bucket,
                "s3_key": s3_key,
                "status": "uploading",
                "progress": 5,
                "message": "Starting file upload and processing",
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "file_size_bytes": file_size_bytes,
            }

            # Insert the document first
            extraction_repo.create(initial_document, db)
            logger.info(f"Successfully created initial MongoDB document for asset_id: {asset_id}")

        except Exception as mongo_error:
            logger.error(f"MongoDB initialization error: {str(mongo_error)}")
            raise ValueError(f"Failed to initialize database record: {str(mongo_error)}")

        # Then, upload file to S3
        extraction_repo.update_one_by_query_upsert(
            {"asset_id": asset_id, "project_id": project_id},
            {
                "status": "uploading",
                "message": "Uploading file to S3.",
                "progress": 10,
                "updated_at": datetime.now().isoformat(),
            },
            db
        )

        try:
            s3_client = boto3.client("s3")
            s3_client.upload_file(tmp_path, s3_bucket, s3_key)
            logger.info(f"Successfully uploaded file to S3: {s3_bucket}/{s3_key}")
        except Exception as s3_error:
            logger.error(f"S3 upload error: {str(s3_error)}")
            raise ValueError(f"Failed to upload file to S3: {str(s3_error)}")
        finally:
            # Clean up temp file after upload
            if tmp_path and os.path.exists(tmp_path):
                try:
                    os.remove(tmp_path)
                except Exception as cleanup_error:
                    logger.error(f"Error removing temp file: {str(cleanup_error)}")

        extraction_repo.update_one_by_query_upsert(
            {"asset_id": asset_id, "project_id": project_id},
            {
                "status": "processing",
                "message": "File uploaded to S3. Starting extraction.",
                "progress": 20,
                "updated_at": datetime.now().isoformat(),
            },
            db
        )

        # Continue with existing processing logic
        await process_mpud_extraction_background(
            project_id, asset_id, filename, s3_bucket, s3_key, mongo_db_name
        )

    except Exception as e:
        logger.error(f"Error in background extraction with upload and init: {str(e)}")
        logger.error(traceback.format_exc())

        # Update MongoDB with error status (or create if initial creation failed)
        try:
            extraction_repo.update_one_by_query_upsert(
                {"asset_id": asset_id, "project_id": project_id},
                {
                    "status": "error",
                    "message": str(e),
                    "error_type": type(e).__name__,
                    "error_trace": traceback.format_exc(),
                    "updated_at": datetime.now().isoformat(),
                },
                db
            )
        except Exception as error_update_error:
            logger.error(f"Failed to update error status in MongoDB: {str(error_update_error)}")

        # Clean up temp file if error occurs
        if tmp_path and os.path.exists(tmp_path):
            try:
                os.remove(tmp_path)
            except Exception as cleanup_error:
                logger.error(f"Error removing temp file after error: {str(cleanup_error)}")
    finally:
        # Close MongoDB connection
        if 'mongo_client' in locals():
            mongo_client.close()


async def process_mpud_extraction_background_with_upload(
    project_id: str,
    asset_id: str,
    filename: str,
    tmp_path: str,
    s3_bucket: str,
    s3_key: str,
    mongo_db_name: str
):
    """Process MPUD extraction in the background including S3 upload"""

    # Initialize MongoDB client and repositories
    mongo_client = MongoClient(settings.MONGO_URL)
    db = mongo_client[mongo_db_name]  # Get the actual database object
    extraction_repo = BaseRepository("extraction")

    try:
        # First, upload file to S3
        extraction_repo.update_one_by_query_upsert(
            {"asset_id": asset_id, "project_id": project_id},
            {
                "status": "uploading",
                "message": "Uploading file to S3.",
                "progress": 10,
                "updated_at": datetime.now().isoformat(),
            },
            db
        )

        try:
            s3_client = boto3.client("s3")
            s3_client.upload_file(tmp_path, s3_bucket, s3_key)
            logger.info(f"Successfully uploaded file to S3: {s3_bucket}/{s3_key}")
        except Exception as s3_error:
            logger.error(f"S3 upload error: {str(s3_error)}")
            raise ValueError(f"Failed to upload file to S3: {str(s3_error)}")
        finally:
            # Clean up temp file after upload
            if tmp_path and os.path.exists(tmp_path):
                try:
                    os.remove(tmp_path)
                except Exception as cleanup_error:
                    logger.error(f"Error removing temp file: {str(cleanup_error)}")

        extraction_repo.update_one_by_query_upsert(
            {"asset_id": asset_id, "project_id": project_id},
            {
                "status": "processing",
                "message": "File uploaded to S3. Starting extraction.",
                "progress": 20,
                "updated_at": datetime.now().isoformat(),
            },
            db
        )

        # Continue with existing processing logic
        await process_mpud_extraction_background(
            project_id, asset_id, filename, s3_bucket, s3_key, mongo_db_name
        )

    except Exception as e:
        logger.error(f"Error in background extraction with upload: {str(e)}")
        logger.error(traceback.format_exc())

        # Update MongoDB with error status
        extraction_repo.update_one_by_query_upsert(
            {"asset_id": asset_id, "project_id": project_id},
            {
                "status": "error",
                "message": str(e),
                "error_type": type(e).__name__,
                "error_trace": traceback.format_exc(),
                "updated_at": datetime.now().isoformat(),
            },
            db
        )
        # Clean up temp file if error occurs
        if tmp_path and os.path.exists(tmp_path):
            try:
                os.remove(tmp_path)
            except Exception as cleanup_error:
                logger.error(f"Error removing temp file after error: {str(cleanup_error)}")
    finally:
        # Close MongoDB connection
        if 'mongo_client' in locals():
            mongo_client.close()


async def process_mpud_extraction_background(
    project_id: str,
    asset_id: str,
    filename: str,
    s3_bucket: str,
    s3_key: str,
    mongo_db_name: str
):
    """Process MPUD extraction in the background"""

    # Initialize MongoDB client and repositories
    mongo_client = MongoClient(settings.MONGO_URL)
    db = mongo_client[mongo_db_name]  # Get the actual database object
    extraction_repo = BaseRepository("extraction")

    try:
        # Initialize repo with MongoDB database
        repo = KnowledgeGraphRepositoryV2(db)

        # Update status to processing
        await update_extraction_status_with_retry(
            extraction_repo, asset_id, project_id,
            {
                "status": "processing",
                "message": "Extracting PDF layout data.",
                "progress": 25,
                "updated_at": datetime.now().isoformat(),
            },
            db,
            mongo_client=mongo_client
        )

        # Stage 2: PDF layout extraction (20-40%)
        try:
            _, _, _, _, _, _, layout_data, _ = repo._read_pdf(s3_bucket, s3_key, county="", user_name="")

            # Validate layout_data
            if not layout_data or not isinstance(layout_data, list):
                raise ValueError(f"Invalid layout data format: {type(layout_data)}")

            logger.info(f"Successfully extracted layout data: {len(layout_data)} items")
        except TimeoutError as timeout_error:
            logger.error(f"AWS Textract timeout during PDF layout extraction: {str(timeout_error)}")
            raise ValueError(f"PDF processing timed out: {str(timeout_error)}")
        except Exception as layout_error:
            logger.error(f"Error extracting PDF layout: {str(layout_error)}")
            logger.error(traceback.format_exc())
            raise ValueError(f"Failed to extract PDF layout: {str(layout_error)}")

        await update_extraction_status_with_retry(
            extraction_repo, asset_id, project_id,
            {
                "status": "processing",
                "message": "PDF layout data extracted.",
                "progress": 40,
                "updated_at": datetime.now().isoformat(),
            },
            db,
            mongo_client=mongo_client
        )

        # Stage 3: Page processing (40-80%)
        # Group layout_data by real page number
        pages = defaultdict(list)
        for item in layout_data:
            if isinstance(item, dict):
                page_num = item.get("Page") or item.get("page") or "?"
                pages[page_num].append(item)

        total_pages = len(pages)
        if total_pages == 0:
            logger.warning("No pages found in layout data")

        page_progress_start = 40
        page_progress_range = 40  # 40-80%

        for idx, page_num in enumerate(sorted(pages, key=lambda x: int(x) if str(x).isdigit() else x)):
            try:
                # Calculate progress for this page (ensure it always increments)
                page_base_progress = page_progress_start + int((idx * page_progress_range) / max(1, total_pages))

                # First update: page extracting
                try:
                    extraction_repo.update_one_by_query_upsert(
                        {"asset_id": asset_id, "project_id": project_id},
                        {
                            "status": "processing",
                            "message": f"Page {page_num} extracting",
                            "progress": page_base_progress,
                            "updated_at": datetime.now().isoformat(),
                        },
                        db
                    )
                    logger.info(f"Updated progress to {page_base_progress}% for page {page_num}")
                except Exception as update_error:
                    logger.error(f"Failed to update progress for page {page_num} extraction: {str(update_error)}")
                    # Continue processing even if update fails

                await asyncio.sleep(1)

                # Second update: page points extracted
                try:
                    final_page_progress = page_base_progress + int(page_progress_range / (max(1, total_pages) * 2))
                    extraction_repo.update_one_by_query_upsert(
                        {"asset_id": asset_id, "project_id": project_id},
                        {
                            "status": "processing",
                            "message": f"Page {page_num} points are extracted from condition",
                            "progress": final_page_progress,
                            "updated_at": datetime.now().isoformat(),
                        },
                        db
                    )
                    logger.info(f"Updated progress to {final_page_progress}% for page {page_num} completion")
                except Exception as update_error:
                    logger.error(f"Failed to update progress for page {page_num} completion: {str(update_error)}")
                    # Continue processing even if update fails

                await asyncio.sleep(1)

            except Exception as page_error:
                logger.error(f"Error processing page {page_num}: {str(page_error)}")
                # Continue with next page even if this page fails
                continue

        # Stage 4: COA extraction and transformation (80-95%)
        await update_extraction_status_with_retry(
            extraction_repo, asset_id, project_id,
            {
                "status": "processing",
                "message": "Extracting COA sections from layout data.",
                "progress": 85,
                "updated_at": datetime.now().isoformat(),
            },
            db,
            mongo_client=mongo_client
        )

        try:
            flat_list = repo.extract_coa_from_pdf(layout_data, pdf_bucket=s3_bucket, pdf_path=s3_key)
            if not flat_list and isinstance(flat_list, list):
                logger.warning("No COA sections extracted from layout data")
        except Exception as coa_error:
            logger.error(f"Error extracting COA sections: {str(coa_error)}")
            logger.error(traceback.format_exc())
            raise ValueError(f"Failed to extract COA sections: {str(coa_error)}")

        await update_extraction_status_with_retry(
            extraction_repo, asset_id, project_id,
            {
                "status": "processing",
                "message": "Transforming COA sections to minimal schema.",
                "progress": 90,
                "updated_at": datetime.now().isoformat(),
            },
            db,
            mongo_client=mongo_client
        )

        # Get Accela token from settings or environment
        accela_token = getattr(settings, 'ACCELA_TOKEN', None)
        if not accela_token:
            logger.warning("No ACCELA_TOKEN found in settings")

        try:
            formatted = await repo.transform_coa_to_minimal_schema(flat_list, accela_token=accela_token)
            if not formatted or not isinstance(formatted, dict):
                logger.warning(f"Invalid formatted data: {type(formatted)}")
        except Exception as transform_error:
            logger.error(f"Error transforming COA sections: {str(transform_error)}")
            logger.error(traceback.format_exc())
            raise ValueError(f"Failed to transform COA sections: {str(transform_error)}")

        # Stage 4.5: Extract MPUD project information (90-95%)
        await update_extraction_status_with_retry(
            extraction_repo, asset_id, project_id,
            {
                "status": "processing",
                "message": "Extracting MPUD project information.",
                "progress": 92,
                "updated_at": datetime.now().isoformat(),
            },
            db,
            mongo_client=mongo_client
        )

        # Extract MPUD information from the document
        try:
            mpud_info = extract_mpud_project_info(layout_data, s3_bucket, s3_key)
            if not mpud_info or not isinstance(mpud_info, dict):
                logger.warning(f"Invalid MPUD info: {type(mpud_info)}")
        except Exception as mpud_error:
            logger.error(f"Error extracting MPUD info: {str(mpud_error)}")
            logger.error(traceback.format_exc())
            # Continue even if MPUD info extraction fails
            mpud_info = {"error": str(mpud_error)}

        # Merge MPUD info with the existing formatted data
        if mpud_info and isinstance(mpud_info, dict) and "error" not in mpud_info:
            if "mpud_info" not in formatted:
                formatted["mpud_info"] = {}
            formatted["mpud_info"].update(mpud_info)

        await update_extraction_status_with_retry(
            extraction_repo, asset_id, project_id,
            {
                "status": "processing",
                "message": "MPUD project information extracted.",
                "progress": 95,
                "updated_at": datetime.now().isoformat(),
            },
            db,
            mongo_client=mongo_client
        )

        # Stage 5: Store data in Neo4j (95-100%)
        await update_extraction_status_with_retry(
            extraction_repo, asset_id, project_id,
            {
                "status": "processing",
                "message": "Storing extracted data in database.",
                "progress": 97,
                "updated_at": datetime.now().isoformat(),
            },
            db,
            mongo_client=mongo_client
        )

        # Store the extracted data in Neo4j
        try:
            await store_extracted_data_in_neo4j(project_id, formatted, filename, asset_id)
        except Exception as neo4j_error:
            logger.error(f"Error storing data in Neo4j: {str(neo4j_error)}")
            logger.error(traceback.format_exc())
            raise ValueError(f"Failed to store data in Neo4j: {str(neo4j_error)}")

        # Stage 6: Success message for Neo4j storage
        await update_extraction_status_with_retry(
            extraction_repo, asset_id, project_id,
            {
                "status": "processing",
                "message": "Data successfully stored in Neo4j database.",
                "progress": 98,
                "updated_at": datetime.now().isoformat(),
            },
            db,
            mongo_client=mongo_client
        )

        # Final success status with results
        await update_extraction_status_with_retry(
            extraction_repo, asset_id, project_id,
            {
                "status": "success",
                "message": "Extraction completed successfully.",
                "progress": 100,
                "result": formatted,
                "updated_at": datetime.now().isoformat(),
            },
            db,
            mongo_client=mongo_client
        )

    except Exception as e:
        logger.error(f"Error in background extraction process: {str(e)}")
        logger.error(traceback.format_exc())

        error_message = str(e)
        error_type = type(e).__name__

        # Update MongoDB with detailed error status using robust update
        try:
            await update_extraction_status_with_retry(
                extraction_repo, asset_id, project_id,
                {
                    "status": "error",
                    "message": error_message,
                    "error_type": error_type,
                    "error_trace": traceback.format_exc(),
                    "updated_at": datetime.now().isoformat(),
                },
                db,
                mongo_client=mongo_client
            )
        except Exception as update_error:
            logger.error(f"Failed to update error status in MongoDB: {str(update_error)}")
            # As a fallback, try the direct update method
            try:
                extraction_repo.update_one_by_query_upsert(
                    {"asset_id": asset_id, "project_id": project_id},
                    {
                        "status": "error",
                        "message": f"Processing failed: {error_message}. Update error: {str(update_error)}",
                        "error_type": error_type,
                        "error_trace": traceback.format_exc(),
                        "updated_at": datetime.now().isoformat(),
                    },
                    db
                )
            except Exception as final_error:
                logger.error(f"Complete failure to update MongoDB: {str(final_error)}")
    finally:
        # Close MongoDB connection
        if 'mongo_client' in locals():
            mongo_client.close()


async def store_extracted_data_in_neo4j(chat_id: str, formatted_data: dict, filename: str, asset_id: str):
    """
    Store the extracted MPUD and conditions data in Neo4j
    """
    try:
        # Initialize Neo4j connection
        neo4j_db = Neo4jDB()
        neo4j_db.connect()

        # Create a mock user for the chat repository (you might want to pass actual user)
        mock_user = {"name": "system", "cognito:username": "system"}

        # Initialize chat repository
        chat_repo = ChatRepository(user=mock_user, db=neo4j_db)

        # Extract mpud_info and conditions from formatted data
        mpud_info = formatted_data.get("mpud_info", {})
        conditions = formatted_data.get("conditions", {})

        # Prepare the data for storage
        update_data = {
            "mpud_info": json.dumps(mpud_info),
            "conditions": json.dumps(conditions),
            "extracted_at": datetime.now().isoformat(),
            "filename": filename,
            "asset_id": asset_id
        }

        # Update the chat node with extracted data
        result = chat_repo.update_chat(int(chat_id), update_data)

        # Create discussion nodes for each condition section
        await create_discussion_nodes_for_conditions(chat_id, conditions, neo4j_db)

        # Add success message to chat history
        await add_extraction_success_message_to_history(chat_id, filename, neo4j_db)

        logger.info(f"Successfully stored extracted data for chat_id: {chat_id}")

    except Exception as e:
        logger.error(f"Error storing extracted data in Neo4j: {str(e)}")
        raise e
    finally:
        if 'neo4j_db' in locals():
            neo4j_db.close()


async def add_extraction_success_message_to_history(chat_id: str, filename: str, neo4j_db: Neo4jDB):
    """
    Add a success message to the chat history after MPUD extraction is completed
    """
    try:
        # Create the success message
        success_message = {
            "role": "assistant",
            "created_at": datetime.now().isoformat(),
            "content": f"✅ **MPUD Extraction Completed Successfully!**\n\nThe MPUD document '{filename}' has been processed and the extracted conditions are now visible in the right panel.",
            "discussion_type": "info"
        }

        # First, verify the DiscussionRoot exists
        verify_query = """
        MATCH (root:DiscussionRoot)
        WHERE ID(root) = toInteger($chat_id)
        RETURN root
        """
        result = neo4j_db.execute_query(verify_query, {"chat_id": chat_id})

        if not result:
            logger.error(f"DiscussionRoot node not found for chat_id: {chat_id}")
            return

        # Check for existing discussion node with discussion_so_far
        discussion_query = """
        MATCH (root:DiscussionRoot)-[:HAS_CHILD]->(d:Discussion)
        WHERE ID(root) = toInteger($chat_id) AND d.discussion_so_far IS NOT NULL
        RETURN d.discussion_so_far as discussion_so_far, ID(d) as node_id
        ORDER BY d.step ASC
        LIMIT 1
        """
        discussion_result = neo4j_db.execute_query(discussion_query, {"chat_id": chat_id})

        if discussion_result and discussion_result[0]["discussion_so_far"]:
            # Update existing discussion node
            existing_discussion = discussion_result[0]["discussion_so_far"]
            discussion_node_id = discussion_result[0]["node_id"]

            try:
                discussion_history = json.loads(existing_discussion)
            except json.JSONDecodeError:
                discussion_history = []

            # Add the success message to the discussion history
            discussion_history.append(success_message)

            # Update the discussion node with the new history
            update_query = """
            MATCH (d:Discussion)
            WHERE ID(d) = toInteger($node_id)
            SET d.discussion_so_far = $updated_discussion
            """

            neo4j_db.execute_query(
                update_query,
                {
                    "node_id": discussion_node_id,
                    "updated_discussion": json.dumps(discussion_history)
                }
            )

            logger.info(f"Successfully added extraction success message to existing chat history for chat_id: {chat_id}")
        else:
            # No existing discussion with discussion_so_far, find any discussion node or create one
            any_discussion_query = """
            MATCH (root:DiscussionRoot)-[:HAS_CHILD]->(d:Discussion)
            WHERE ID(root) = toInteger($chat_id)
            RETURN ID(d) as node_id
            ORDER BY d.step ASC
            LIMIT 1
            """
            any_discussion_result = neo4j_db.execute_query(any_discussion_query, {"chat_id": chat_id})

            if any_discussion_result:
                # Update the first discussion node
                discussion_node_id = any_discussion_result[0]["node_id"]

                # Initialize with success message
                initial_discussion = [success_message]

                update_query = """
                MATCH (d:Discussion)
                WHERE ID(d) = toInteger($node_id)
                SET d.discussion_so_far = $updated_discussion
                """

                neo4j_db.execute_query(
                    update_query,
                    {
                        "node_id": discussion_node_id,
                        "updated_discussion": json.dumps(initial_discussion)
                    }
                )

                logger.info(f"Successfully initialized chat history with extraction success message for chat_id: {chat_id}")
            else:
                # Create a new discussion node if none exists
                discussion_properties = {
                    "id": str(uuid.uuid4()),
                    "step": 1,
                    "discussion_so_far": json.dumps([success_message]),
                    "created_at": datetime.now().isoformat(),
                    "discussion_type": "info"
                }

                discussion_node = neo4j_db.create_node("Discussion", discussion_properties)

                # Create relationship from DiscussionRoot to Discussion
                neo4j_db.create_edge(
                    int(chat_id),
                    discussion_node["node_id"],
                    "HAS_CHILD",
                    {"id": str(uuid.uuid4())}
                )

                logger.info(f"Successfully created new discussion node with extraction success message for chat_id: {chat_id}")

    except Exception as e:
        logger.error(f"Error adding success message to chat history: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        # Don't raise the exception here as it's not critical for the main flow


async def create_discussion_nodes_for_conditions(chat_id: str, conditions: dict, neo4j_db: Neo4jDB):
    """
    Create discussion nodes for each condition section
    """
    try:
        step_counter = 1
        for section_name, section_conditions in conditions.items():
            if not section_conditions:  # Skip empty sections
                continue

            # Create discussion node for this section
            discussion_properties = {
                "id": str(uuid.uuid4()),  # Required 'id' property
                "discussion_type": f"layout_{section_name.lower()}",
                "step": step_counter,
                "modifications": json.dumps({
                    "modified_node": {
                        "Points": section_conditions
                    }
                }),
                "common_points": json.dumps([{
                    "message": {
                        "modifications": [{
                            "modified_node": {
                                "Points": section_conditions
                            }
                        }]
                    }
                }]),
                "created_at": datetime.now().isoformat(),
                "section_name": section_name
            }

            # Create the discussion node
            discussion_node = neo4j_db.create_node("Discussion", discussion_properties)

            # Create relationship from DiscussionRoot to Discussion
            neo4j_db.create_edge(
                int(chat_id),
                discussion_node["node_id"],
                "HAS_CHILD",
                {"id": str(uuid.uuid4())}
            )

            logger.info(f"Created discussion node for section: {section_name}")
            step_counter += 1

    except Exception as e:
        logger.error(f"Error creating discussion nodes: {str(e)}")
        raise e


def extract_mpud_project_info(layout_data, s3_bucket, s3_key):
    """
    Extract MPUD project information from layout data
    """
    try:
        from app.core.detail_extractor import analyze_mpud_document

        # Combine all text from layout data for analysis
        combined_text = ""

        # Add defensive checks
        if not layout_data or not isinstance(layout_data, list):
            logger.warning(f"Invalid layout_data format: {type(layout_data)}")
            return {"error": "Invalid layout data format"}

        for item in layout_data:
            if not isinstance(item, dict):
                # Skip non-dictionary items
                continue

            try:
                # Extract text from various possible fields
                text_fields = ['Text', 'text', 'LAYOUT_TEXT', 'layout_text', 'content', 'Content']
                for field in text_fields:
                    if field in item and item.get(field):
                        combined_text += str(item.get(field, "")) + " "

                # Also check for header information
                header_fields = ['Header', 'header', 'LAYOUT_HEADER', 'layout_header', 'title', 'Title']
                for field in header_fields:
                    if field in item and item.get(field):
                        combined_text += str(item.get(field, "")) + " "
            except Exception as field_error:
                # Log the error but continue processing other items
                logger.error(f"Error processing layout item: {str(field_error)}")
                continue

        # Use the existing MPUD document analyzer
        mpud_analysis = analyze_mpud_document(combined_text)

        # Extract additional information using regex patterns
        additional_info = extract_additional_mpud_info(combined_text)

        # Combine the analysis results
        mpud_info = {
            "project_details": mpud_analysis,
            "additional_info": additional_info,
            "source_file": f"{s3_bucket}/{s3_key}",
            "extraction_timestamp": datetime.now().isoformat()
        }

        logger.info(f"Extracted MPUD project information: {mpud_info}")
        return mpud_info

    except Exception as e:
        logger.error(f"Error extracting MPUD project info: {str(e)}")
        logger.error(traceback.format_exc())
        return {}


def extract_additional_mpud_info(text):
    """
    Extract additional MPUD information using regex patterns
    """
    import re

    additional_info = {}

    # Extract file number
    file_no_pattern = r'FILE NO\.?:\s*([A-Z0-9\-_]+)'
    file_match = re.search(file_no_pattern, text, re.IGNORECASE)
    if file_match:
        additional_info["file_number"] = file_match.group(1).strip()

    # Extract subject/title
    subject_pattern = r'SUBJECT:\s*(.*?)(?=\n|REFERENCES:|REFERENCE:|$)'
    subject_match = re.search(subject_pattern, text, re.IGNORECASE | re.DOTALL)
    if subject_match:
        additional_info["subject"] = subject_match.group(1).strip()

    # Extract developer information
    developer_patterns = [
        r'DEVELOPER:\s*(.*?)(?=\n|$)',
        r'APPLICANT:\s*(.*?)(?=\n|$)',
        r'OWNER:\s*(.*?)(?=\n|$)'
    ]
    for pattern in developer_patterns:
        dev_match = re.search(pattern, text, re.IGNORECASE)
        if dev_match:
            additional_info["developer"] = dev_match.group(1).strip()
            break

    # Extract location information
    location_patterns = [
        r'LOCATION:\s*(.*?)(?=\n|$)',
        r'ADDRESS:\s*(.*?)(?=\n|$)',
        r'PROPERTY:\s*(.*?)(?=\n|$)'
    ]
    for pattern in location_patterns:
        loc_match = re.search(pattern, text, re.IGNORECASE)
        if loc_match:
            additional_info["location"] = loc_match.group(1).strip()
            break

    # Extract commission district
    district_pattern = r'COMMISSION DISTRICT:\s*([A-Z0-9\-_]+)'
    district_match = re.search(district_pattern, text, re.IGNORECASE)
    if district_match:
        additional_info["commission_district"] = district_match.group(1).strip()

    # Extract date
    date_pattern = r'DATE:\s*(\d{1,2}/\d{1,2}/\d{2,4})'
    date_match = re.search(date_pattern, text, re.IGNORECASE)
    if date_match:
        additional_info["date"] = date_match.group(1).strip()

    return additional_info
