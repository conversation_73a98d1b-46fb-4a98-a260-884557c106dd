from fastapi import APIRouter, Depends, Body
from app.utils.auth_utils import get_current_user
from app.database.neo4j_db import Neo4jDB
from typing import List
from app.crud.layout_repository import LayoutRepository
from app.models.layout_model import LayoutInsert, LayoutView, LayoutQueryParams

router = APIRouter(
    tags=["layout_v2"],
    responses={404: {"description": "Not found"}},
    prefix="/v2",
)

_REPO = LayoutRepository


@router.post("/layout", response_model=LayoutView)
def create_new_layout(
    query: LayoutQueryParams = Depends(),
    body: LayoutInsert = Body(...),
    db=Depends(Neo4jDB().get),
    user=Depends(get_current_user),
):
    body.user_name = user["name"]
    return _REPO(user, db).create_layout(
        application_id=query.application_id,
        body=body.model_dump(),
    )


@router.get("/layout", response_model=List[LayoutView])
def get_all_layout(
    query: LayoutQueryParams = Depends(),
    db=Depends(Neo4jDB().get),
    user=Depends(get_current_user),
):
    return _REPO(user, db).get_all_layout(
        application_id=query.application_id,
    )
