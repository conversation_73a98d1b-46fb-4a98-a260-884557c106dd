from fastapi import APIRouter, Depends, Body, HTTPException, UploadFile, File
from app.utils.auth_utils import get_current_user
from app.database.neo4j_db import Neo4jDB
from app.crud.project_repository import ProjectRepository
from typing import List, Dict
from app.utils.base_utils import application_error_handler
from datetime import datetime
from app.core.config import settings
from app.utils.assert_s3 import AssertS3
from fastapi.responses import JSONResponse
import traceback
from app.core.interactive_discussion import DiscussionController
from app.utils.async_streaming_decorator import async_stream_generator
from fastapi import BackgroundTasks
import asyncio
from app.utils.project_utils import calculate_token
import json
from app.models.document import UploadStatus
from app.crud.knowledge_graph_v2_repository import KnowledgeGraphRepositoryV2
import uuid
from pymongo import MongoClient
from app.core.telemetry import get_logger

router = APIRouter(
    tags=["project_v2"],
    responses={404: {"description": "Not found"}},
    prefix="/v2",
)

_REPO = ProjectRepository
log = get_logger("pasco.ai.connector")


@router.post("/project")
async def create_project(
    title: str = Body(...),
    creator_name: str = Body(...),
    db=Depends(Neo4jDB().get),
    user=Depends(get_current_user),
):
    """Create a new project and initialize its S3 folders"""
    try:
        # Create project in Neo4j
        project = _REPO(user, db).create_project(title, creator_name)

        try:
            # Initialize S3 folders using the node ID
            assert_s3 = AssertS3(project["node_id"])
            await assert_s3.initialize_folders()
        except Exception as s3_error:
            log.warning(f"S3 folder initialization failed: {str(s3_error)}")
            # Continue execution as this is non-critical

        return project

    except Exception as e:
        log.error(f"Project creation error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/projects")
@application_error_handler
def get_all_projects(
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
):
    return _REPO(user, db).get_all_projects()


@router.get("/project/{project_id}")
@application_error_handler
def get_project(
    project_id: str,
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
):
    project = _REPO(user, db).get_project_by_id(project_id)
    if not project:
        raise HTTPException(
            status_code=404,
            detail="Project not found",
        )
    return project


@router.patch("/project/{project_id}")
@application_error_handler
def update_project(
    project_id: str,
    title: str = Body(None),
    description: str = Body(None),
    db=Depends(Neo4jDB().get),
    user=Depends(get_current_user),
):
    return _REPO(user, db).update_project(project_id, title, description)


@router.delete("/project/{project_id}")
@application_error_handler
def delete_project(
    project_id: str,
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
):
    result = _REPO(user, db).delete_project(project_id)
    if not result:
        raise HTTPException(
            status_code=404,
            detail="Project not found or not deleted",
        )
    return {"message": "Project deleted successfully"}


@router.post("/project/{project_id}/asset")
async def upload_asset(
    project_id: str,
    file: UploadFile = File(...),
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    """Upload an asset file to the project"""
    try:
        # Convert project_id to integer for Neo4j query
        project_id_int = int(project_id)

        # Verify project exists and user has access
        project = _REPO(user, db).get_project_by_id(project_id_int)
        if not project:
            raise HTTPException(
                status_code=404,
                detail="Project not found",
            )

        # Check file size (100MB limit)
        MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB in bytes

        # Read a small chunk to get file size
        content = await file.read()
        file_size = len(content)
        await file.seek(0)  # Reset file pointer

        if file_size > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,  # Request Entity Too Large
                detail=f"File size ({file_size / (1024 * 1024):.2f}MB) exceeds maximum allowed size (30MB)",
            )

        try:
            assert_s3 = AssertS3(project_id)

            # Check if file already exists in extracted folder
            extracted_files = await assert_s3.list_extracted()
            existing_file = next(
                (f for f in extracted_files if f["filename"] == file.filename), None
            )

            if existing_file:
                # Use existing extracted content
                log.info(f"Using existing extracted content for {file.filename}")
                extracted_text = existing_file["content"]
                document_id = str(uuid.uuid4())

                # Update project assets in Neo4j with existing content
                try:
                    project_data = _REPO(user, db).get_project_by_id(project_id_int)
                    current_assets = json.loads(project_data.get("assets", "[]"))
                    current_document_ids = json.loads(
                        project_data.get("document_ids", "[]")
                    )

                    new_asset = {
                        "filename": file.filename,
                        "content": extracted_text,
                        "type": "extracted",
                        "document_id": document_id,
                    }
                    current_assets.append(new_asset)
                    current_document_ids.append(document_id)

                    _REPO(user, db).update_project(
                        project_id_int,
                        {
                            "assets": json.dumps(current_assets),
                            "document_ids": json.dumps(current_document_ids),
                        },
                    )

                    return JSONResponse(
                        content={
                            "message": f"File {file.filename} processed using existing content",
                            "document_id": document_id,
                        },
                        status_code=200,
                    )

                except Exception as e:
                    log.error(f"Failed to update project with existing content: {str(e)}")
                    raise HTTPException(status_code=500, detail=str(e))

            # If file doesn't exist, proceed with normal upload and processing
            result = await assert_s3.upload_file(
                file.filename, file.file, file.content_type
            )

            # Initialize knowledge graph repository with MongoDB client
            mongo_client = MongoClient(settings.MONGO_URL)
            kg_repo = KnowledgeGraphRepositoryV2(
                mongo_client[settings.MONGO_DEFAULT_DATABASE]
            )

            # Generate document ID first
            document_id = str(uuid.uuid4())

            # Create document entry
            kg_repo.ocr.insert_to_documents_collection(
                file.filename,
                assert_s3.bucket_name,
                f"{assert_s3.uploaded_path}{file.filename}",
                user["cognito:username"],  # Use cognito username from user dict
                user["cognito:username"],  # Use cognito username from user dict
                "default",
                document_id,
                UploadStatus.OCR,
                mongo_client[settings.MONGO_DEFAULT_DATABASE],
            )

            # Update initial file status
            kg_repo.documents_repo.update_one_by_query_upsert(
                {"_id": document_id},
                {
                    "status": "Picked for OCR",
                    "upload_initiator": "system",
                    "upload_status": UploadStatus.OCR,
                },
                mongo_client[settings.MONGO_DEFAULT_DATABASE],
            )

            file_ext = file.filename.lower().split(".")[-1]

            if file_ext in ["jpg", "jpeg", "png"]:
                # Process image file with Textract
                import boto3

                textract = boto3.client(
                    "textract",
                    region_name=settings.AWS_REGION,
                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID_S3_Textract,
                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY_S3_Textract,
                )

                # Update status to OCR in progress
                kg_repo.documents_repo.update_one_by_query_upsert(
                    {"_id": document_id},
                    {"status": "OCR in progress", "upload_status": UploadStatus.OCR},
                    mongo_client[settings.MONGO_DEFAULT_DATABASE],
                )

                response = textract.detect_document_text(Document={"Bytes": content})

                extracted_text = ""
                for item in response["Blocks"]:
                    if item["BlockType"] == "LINE":
                        extracted_text += item["Text"] + "\n"

                # Add logging for image processing
                log.info(
                    f"Extracted text from image: {extracted_text[:100]}..."
                )  # Show first 100 chars

                try:
                    # Load existing assets with integer ID
                    project_data = _REPO(user, db).get_project_by_id(project_id_int)
                    current_assets = json.loads(project_data.get("assets", "[]"))
                    current_document_ids = json.loads(
                        project_data.get("document_ids", "[]")
                    )

                    new_asset = {
                        "filename": file.filename,
                        "content": extracted_text,
                        "type": "extracted",
                        "document_id": document_id,
                    }
                    current_assets.append(new_asset)
                    current_document_ids.append(document_id)

                    # Update project with integer ID and document IDs
                    update_result = _REPO(user, db).update_project(
                        project_id_int,
                        {
                            "assets": json.dumps(current_assets),
                            "document_ids": json.dumps(current_document_ids),
                        },
                    )
                    log.info(f"Asset update result: {update_result}")

                    # Update final status
                    kg_repo.documents_repo.update_one_by_query_upsert(
                        {"_id": document_id},
                        {
                            "status": "OCR completed",
                            "upload_status": UploadStatus.COMPLETED,
                            "inserted_time": datetime.now(),
                        },
                        mongo_client[settings.MONGO_DEFAULT_DATABASE],
                    )

                except Exception as asset_error:
                    log.error(f"Failed to update assets in Neo4j: {str(asset_error)}")
                    traceback.print_exc()
                    kg_repo.documents_repo.update_one_by_query_upsert(
                        {"_id": document_id},
                        {
                            "status": str(asset_error),
                            "upload_status": UploadStatus.ERROR,
                        },
                        mongo_client[settings.MONGO_DEFAULT_DATABASE],
                    )

            elif file_ext == "pdf":
                # Process PDF with Textract
                import boto3

                textract = boto3.client(
                    "textract",
                    region_name=settings.AWS_REGION,
                    aws_access_key_id=settings.AWS_ACCESS_KEY_ID_S3_Textract,
                    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY_S3_Textract,
                )

                # Update status to OCR in progress
                kg_repo.documents_repo.update_one_by_query_upsert(
                    {"_id": document_id},
                    {"status": "OCR in progress", "upload_status": UploadStatus.OCR},
                    mongo_client[settings.MONGO_DEFAULT_DATABASE],
                )

                # Start async job since PDFs require async processing
                response = textract.start_document_text_detection(
                    DocumentLocation={
                        "S3Object": {
                            "Bucket": assert_s3.bucket_name,
                            "Name": f"{assert_s3.uploaded_path}{file.filename}",
                        }
                    }
                )

                job_id = response["JobId"]

                async def process_textract_job(
                    job_id: str, assert_s3: AssertS3, filename: str, document_id: str
                ):
                    log.info(
                        f"Starting Textract job processing for job_id: {job_id}, filename: {filename}"
                    )
                    # Add timeout counter
                    timeout_seconds = 180  # 3 minutes timeout
                    start_time = datetime.now()

                    # Wait for job completion
                    while True:
                        response = textract.get_document_text_detection(JobId=job_id)
                        status = response["JobStatus"]
                        log.info(f"Job status: {status}")

                        if status in ["SUCCEEDED", "FAILED"]:
                            break

                        # Check for timeout
                        if (
                            datetime.now() - start_time
                        ).total_seconds() > timeout_seconds:
                            log.info(
                                f"Textract job timed out after {timeout_seconds} seconds"
                            )
                            kg_repo.documents_repo.update_one_by_query_upsert(
                                {"_id": document_id},
                                {
                                    "status": "OCR timeout",
                                    "upload_status": UploadStatus.ERROR,
                                },
                                mongo_client[settings.MONGO_DEFAULT_DATABASE],
                            )
                            return

                        await asyncio.sleep(5)

                    if status == "SUCCEEDED":
                        log.info(f"Job {job_id} completed successfully")

                        # Get first page
                        log.info("Retrieving first page...")
                        response = textract.get_document_text_detection(JobId=job_id)
                        full_text = ""

                        # Process first page
                        for item in response["Blocks"]:
                            if item["BlockType"] == "LINE":
                                full_text += item["Text"] + "\n"

                        # Only check for additional pages if NextToken exists
                        if "NextToken" in response:
                            while "NextToken" in response:
                                log.info("Retrieving next page...")
                                response = textract.get_document_text_detection(
                                    JobId=job_id, NextToken=response["NextToken"]
                                )
                                for item in response["Blocks"]:
                                    if item["BlockType"] == "LINE":
                                        full_text += item["Text"] + "\n"

                        # Save complete text to single file
                        log.info("Uploading extracted text to S3...")
                        try:
                            # Load previous assets from Neo4j Project node
                            try:
                                project_data = _REPO(user, db).get_project_by_id(
                                    project_id_int
                                )
                                previous_assets = json.loads(
                                    project_data.get("assets", "[]")
                                )
                            except Exception as e:
                                log.info(
                                    f"Failed to load previous assets, using empty list: {str(e)}"
                                )
                                previous_assets = []

                            previous_assets_content = ""
                            for asset in previous_assets:
                                previous_assets_content += asset["content"] + "\n"
                            previous_assets_content_token = calculate_token(
                                previous_assets_content
                            )["tokens"]
                            # Slice full_text to stay within 8000 token limit
                            remaining_tokens = 8000 - previous_assets_content_token
                            if remaining_tokens > 0:
                                # Calculate tokens for full text
                                full_text_tokens = calculate_token(full_text)["tokens"]

                                if full_text_tokens > remaining_tokens:
                                    # Slice text by newlines to avoid cutting words
                                    lines = full_text.split("\n")
                                    sliced_text = ""
                                    current_tokens = 0

                                    for line in lines:
                                        line_tokens = calculate_token(line + "\n")[
                                            "tokens"
                                        ]
                                        if (
                                            current_tokens + line_tokens
                                            <= remaining_tokens
                                        ):
                                            sliced_text += line + "\n"
                                            current_tokens += line_tokens
                                        else:
                                            break

                                    full_text = sliced_text
                            if (
                                calculate_token(full_text)["tokens"]
                                + previous_assets_content_token
                                > 8000
                            ):
                                log.info("Token count exceeds 8000, skipping upload")
                                kg_repo.documents_repo.update_one_by_query_upsert(
                                    {"_id": document_id},
                                    {
                                        "status": "Token limit exceeded",
                                        "upload_status": UploadStatus.ERROR,
                                    },
                                    mongo_client[settings.MONGO_DEFAULT_DATABASE],
                                )
                            else:
                                await assert_s3.upload_file(
                                    f"{filename}.txt",
                                    full_text.encode(),
                                    "text/plain",
                                    folder="extracted",
                                )

                                # Update project assets in Neo4j
                                new_asset = {
                                    "filename": f"{filename}.txt",
                                    "content": full_text,
                                    "type": "extracted",
                                    "document_id": document_id,
                                }
                                try:
                                    project_data = _REPO(user, db).get_project_by_id(
                                        project_id_int
                                    )
                                    current_assets = json.loads(
                                        project_data.get("assets", "[]")
                                    )
                                    current_document_ids = json.loads(
                                        project_data.get("document_ids", "[]")
                                    )
                                    current_assets.append(new_asset)
                                    current_document_ids.append(document_id)
                                    _REPO(user, db).update_project(
                                        project_id_int,
                                        {
                                            "assets": json.dumps(current_assets),
                                            "document_ids": json.dumps(
                                                current_document_ids
                                            ),
                                        },
                                    )
                                    log.info(
                                        "Successfully updated project assets in Neo4j"
                                    )

                                    # Update final status
                                    kg_repo.documents_repo.update_one_by_query_upsert(
                                        {"_id": document_id},
                                        {
                                            "status": "OCR completed",
                                            "upload_status": UploadStatus.COMPLETED,
                                            "inserted_time": datetime.now(),
                                        },
                                        mongo_client[settings.MONGO_DEFAULT_DATABASE],
                                    )

                                except Exception as e:
                                    log.error(
                                        f"Failed to update project assets in Neo4j: {str(e)}"
                                    )
                                    kg_repo.documents_repo.update_one_by_query_upsert(
                                        {"_id": document_id},
                                        {
                                            "status": str(e),
                                            "upload_status": UploadStatus.ERROR,
                                        },
                                        mongo_client[settings.MONGO_DEFAULT_DATABASE],
                                    )
                                log.info("Successfully uploaded extracted text")
                        except Exception as e:
                            log.error(f"Failed to upload extracted text: {str(e)}")
                            kg_repo.documents_repo.update_one_by_query_upsert(
                                {"_id": document_id},
                                {"status": str(e), "upload_status": UploadStatus.ERROR},
                                mongo_client[settings.MONGO_DEFAULT_DATABASE],
                            )
                    else:
                        log.info(f"Job {job_id} failed with status: {status}")
                        kg_repo.documents_repo.update_one_by_query_upsert(
                            {"_id": document_id},
                            {
                                "status": f"Textract job failed: {status}",
                                "upload_status": UploadStatus.ERROR,
                            },
                            mongo_client[settings.MONGO_DEFAULT_DATABASE],
                        )

                # Add job to background tasks for processing
                background_tasks.add_task(
                    process_textract_job, job_id, assert_s3, file.filename, document_id
                )

            if result:
                # Verify assets were actually saved using integer ID
                try:
                    updated_project = _REPO(user, db).get_project_by_id(project_id_int)
                    current_assets = json.loads(updated_project.get("assets", "[]"))
                    log.info(f"Current assets after upload: {len(current_assets)} items")
                except Exception as verify_error:
                    log.info(f"Failed to verify assets: {str(verify_error)}")

                return JSONResponse(
                    content={
                        "message": f"File {file.filename} uploaded successfully",
                        "document_id": document_id,
                    },
                    status_code=200,
                )
        except Exception as e:
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=str(e))
    except ValueError as ve:
        log.info(f"Upload asset error: {str(ve)}")
        raise HTTPException(
            status_code=400,
            detail=f"Invalid project ID format: {project_id}. Must be an integer.",
        )
    except Exception as e:
        log.info(f"Upload asset error: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/project/{project_id}/assets")
async def list_assets(
    project_id: str,
    folder: str = None,  # Optional query parameter: 'uploaded' or 'extracted'
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
) -> Dict[str, List[Dict]]:
    """List all assets in the project"""
    # Verify project exists and user has access
    project = _REPO(user, db).get_project_by_id(project_id)
    if not project:
        raise HTTPException(
            status_code=404,
            detail="Project not found",
        )

    try:
        assert_s3 = AssertS3(project_id)
        if folder == "uploaded":
            files = await assert_s3.list_uploaded()
            return {"files": files}
        elif folder == "extracted":
            files = await assert_s3.list_extracted()
            return {"files": files}
        else:
            files = await assert_s3.list_all_files()
            return files
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to list files: {str(e)}")


@router.get("/project/{project_id}/asset/{filename}")
@application_error_handler
async def get_extracted_asset(
    project_id: str,
    filename: str,
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
):
    """Get the extracted data for a specific asset"""
    # Verify project exists and user has access
    project = _REPO(user, db).get_project_by_id(project_id)
    if not project:
        raise HTTPException(
            status_code=404,
            detail="Project not found",
        )

    try:
        assert_s3 = AssertS3(project_id)
        extracted_data = await assert_s3.get_extracted_assert(filename)
        if extracted_data is None:
            raise HTTPException(status_code=404, detail="Extracted data not found")
        return extracted_data
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get extracted data: {str(e)}"
        )


@router.delete("/project/{project_id}/asset/{filename}")
@application_error_handler
async def delete_asset(
    project_id: str,
    filename: str,
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
):
    """Delete an asset from the project"""
    # Verify project exists and user has access
    project = _REPO(user, db).get_project_by_id(project_id)
    if not project:
        raise HTTPException(
            status_code=404,
            detail="Project not found",
        )

    try:
        assert_s3 = AssertS3(project_id)
        await assert_s3.delete_assert(filename)
        return {"message": f"File {filename} deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete file: {str(e)}")


@router.post("/project/{project_id}/chat/v3")
@async_stream_generator
async def interactive_chat(
    message: str = None,
    chat_id: str = None,
    user=Depends(get_current_user),
    stream: bool = True,
    discussion_type=None,
    project_id: str = None,
):
    try:
        log.info(
            "-----------------------------------------------------------Chat V3",
            project_id,
        )
        log.info("message---->", message)
        import logging

        logger = logging.getLogger(__name__)
        logger.info(f"Chat V3, project_id: {project_id}, message: {message}")
        with open("/home/<USER>/skillrank/civicsight/data.txt", "a") as f:
            f.write(f"Chat V3, project_id: {project_id}, message: {message}\n")
        discussion = DiscussionController(
            chat_id=chat_id,
            message=message,
            stream=stream,
            user_id=user["cognito:username"],
            user=user,
            project_id=project_id,
            discussion_type=discussion_type,
        )
        result = await discussion.init_discussion()
        log.info("result---->", result)
        return result

    except Exception as e:
        error_trace = "".join(traceback.format_exc())
        log.error(error_trace)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/project/{project_id}/assets")
async def get_assets(
    project_id: str,
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
):
    return _REPO(user, db).get_assets(project_id)


@router.get("/project/{project_id}/assets/{document_id}")
async def get_asset(
    project_id: str,
    document_id: str,
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
):
    return _REPO(user, db).get_asset(project_id, document_id)


@router.get("/project/{project_id}/chatid")
async def get_chatid(
    project_id: str,
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
):
    return _REPO(user, db).get_chatid(project_id)
