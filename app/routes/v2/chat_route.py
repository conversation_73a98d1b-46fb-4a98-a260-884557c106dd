from fastapi import APIRouter, Depends
from app.utils.auth_utils import get_current_user
from app.database.neo4j_db import Neo4jDB
from app.crud.chat_repository import ChatRepository
from app.models.chat_model import ChatView, ChatQueryParams
from app.utils.base_utils import application_error_handler
from app.database.database_storage import get_one_mongo_db, get_mongo_db
from app.utils.assert_s3 import AssertS3
import asyncio
from fastapi import BackgroundTasks
from fastapi import UploadFile, File, Form
from fastapi import HTTPException
from fastapi.responses import JSONResponse
from datetime import datetime
from pymongo import MongoClient
from app.core.config import settings
# from app.crud.knowledge_graph_v2_repository import KnowledgeGraphRepositoryV2
from app.utils.project_utils import calculate_token
from app.models.document import UploadStatus
import json
import uuid
import traceback
import boto3
from app.core.telemetry.chat_logger import get_chat_logger


router = APIRouter(
    tags=["chat_v2"],
    responses={404: {"description": "Not found"}},
    prefix="/v2",
)

_REPO = ChatRepository
log = get_chat_logger("pasco.ai.connector")


@router.get("/chat_history", response_model=ChatView)
@application_error_handler
def get_chat_details(
    query: ChatQueryParams = Depends(),
    db=Depends(Neo4jDB().get),
    user=Depends(get_current_user),
):
    return _REPO(user, db).get_chat_details(
        chat_id=query.chat_id,
    )


@router.get("/chat_data")
@application_error_handler
def get_chat_data(
    query: ChatQueryParams = Depends(),
    db=Depends(Neo4jDB().get),
    user=Depends(get_current_user),
    mongo_db=Depends(lambda: get_one_mongo_db("lightrag")),
):
    return _REPO(user, db).get_chat_data(chat_id=query.chat_id, mongo_db=mongo_db)


@router.get("/extracted_data")
@application_error_handler
def get_extracted_data(
    query: ChatQueryParams = Depends(),
    user=Depends(get_current_user),
    mongo_db=Depends(get_mongo_db),
):
    """Get extraction data for a specific chat/project"""
    try:
        # Get extraction data from the default database
        extraction_collection = mongo_db["extraction"]

        # Query extraction collection by project_id (using chat_id)
        extraction_data = list(extraction_collection.find(
            {"project_id": str(query.chat_id)},
            {"_id": 0, "result": 0}  # Exclude _id field from results
        ))

        return {"extraction_data": extraction_data}

    except Exception as e:
        log.error(f"Error fetching extraction data for chat {query.chat_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/asset/{chat_id}")
async def upload_asset(
    chat_id: str,
    file: UploadFile = File(...),
    title: str = Form(...),
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    try:
        log.set_chat_id(chat_id)
        chat_id_int = int(chat_id)

        # Initial validation
        assets_result = _REPO(user, db).get_assets(chat_id_int)
        if not assets_result:
            raise HTTPException(status_code=404, detail="Chat not found")

        # Check file size
        MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
        content = await file.read()
        if len(content) > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413,
                detail="File size exceeds maximum allowed size (100MB)",
            )
        await file.seek(0)

        # Generate document ID
        document_id = str(uuid.uuid4())

        # Update document_ids in Neo4j immediately
        try:
            current_doc_ids = []
            if assets_result[0].get("n.document_ids"):
                current_doc_ids = json.loads(assets_result[0]["n.document_ids"])
            current_doc_ids.append(document_id)

            _REPO(user, db).update_chat(
                chat_id_int, {"document_ids": json.dumps(current_doc_ids)}
            )
        except Exception as e:
            log.error(f"Error updating document_ids: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to update document IDs")

        # Rest of the background processing remains the same
        async def process_file_in_background(
            file_content: bytes,
            filename: str,
            content_type: str,
            chat_id_int: int,
            document_id: str,
            title: str,
            user: dict,
            db: Neo4jDB,
        ):
            try:

                await create_asset_document(document_id, filename, "", "", user, title)

                assert_s3 = AssertS3(chat_id_int)
                # Upload to S3
                await assert_s3.upload_file(filename, file_content, content_type)

                # Create initial document entry
                # await update_asset_status(
                #     document_id,
                #     "Upload complete",
                #     UploadStatus.COMPLETE
                # )

                file_ext = filename.lower().split(".")[-1]

                if file_ext in ["jpg", "jpeg", "png"]:
                    # Process image with Textract
                    textract = boto3.client(
                        "textract",
                        region_name=settings.AWS_REGION,
                        aws_access_key_id=settings.AWS_ACCESS_KEY_ID_S3_Textract,
                        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY_S3_Textract,
                    )

                    # Update status to OCR in progress
                    await update_asset_status(
                        document_id, "OCR in progress", UploadStatus.OCR
                    )

                    response = textract.detect_document_text(
                        Document={"Bytes": content}
                    )

                    extracted_text = ""
                    for item in response["Blocks"]:
                        if item["BlockType"] == "LINE":
                            extracted_text += item["Text"] + "\n"

                    # Add logging for image processing
                    log.info(f"Extracted text from image: {extracted_text[:100]}...")  # Show first 100 chars

                    try:
                        # Get fresh assets data again before updating
                        assets_result = _REPO(user, db).get_assets(chat_id_int)
                        if assets_result and assets_result[0]:
                            current_assets = json.loads(
                                assets_result[0].get("n.assets", "[]")
                            )
                            current_document_ids = json.loads(
                                assets_result[0].get("n.document_ids", "[]")
                            )

                        new_asset = {
                            "filename": filename,
                            "content": extracted_text,
                            "type": "extracted",
                            "document_id": document_id,
                            "title": title,
                        }
                        current_assets.append(new_asset)
                        current_document_ids.append(document_id)
                        # Update chat with integer ID and document IDs
                        update_result = _REPO(user, db).update_chat(
                            chat_id_int,
                            {
                                "assets": json.dumps(current_assets),
                                "document_ids": json.dumps(current_document_ids),
                            },
                        )
                        log.info(f"Asset update result: {update_result}")

                        # Update discussion_so_far with all assets content
                        assets_content = "\n".join(
                            [asset.get("content", "") for asset in current_assets]
                        )
                        _REPO(user, db).update_discussion_so_far(
                            chat_id_int, assets_content
                        )

                        # Update final status
                        await update_asset_status(
                            document_id, "OCR completed", UploadStatus.COMPLETED
                        )

                    except Exception as asset_error:
                        log.error(f"Failed to update assets in Neo4j: {str(asset_error)}")
                        traceback.print_exc()
                        await update_asset_status(
                            document_id, str(asset_error), UploadStatus.ERROR
                        )
                elif file_ext == "pdf":
                    # Process PDF with Textract
                    textract = boto3.client(
                        "textract",
                        region_name=settings.AWS_REGION,
                        aws_access_key_id=settings.AWS_ACCESS_KEY_ID_S3_Textract,
                        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY_S3_Textract,
                    )

                    # Update status to OCR in progress
                    await update_asset_status(
                        document_id, "OCR in progress", UploadStatus.OCR
                    )

                    # Start async job since PDFs require async processing
                    response = textract.start_document_text_detection(
                        DocumentLocation={
                            "S3Object": {
                                "Bucket": assert_s3.bucket_name,
                                "Name": f"{assert_s3.uploaded_path}{filename}",
                            }
                        }
                    )

                    job_id = response["JobId"]

                    # Start the Textract job processing
                    await process_textract_job(
                        job_id,
                        assert_s3,
                        filename,
                        document_id,
                        chat_id_int,
                        title,
                        user,
                        db,
                    )

            except Exception as e:
                log.error(f"Background task error: {str(e)}")
                traceback.print_exc()
                # Update error status
                await update_asset_status(document_id, str(e), UploadStatus.ERROR)

        # Add file processing to background tasks
        background_tasks.add_task(
            process_file_in_background,
            content,
            file.filename,
            file.content_type,
            chat_id_int,
            document_id,
            title,
            user,
            db,
        )

        return JSONResponse(
            content={
                "message": f"File {file.filename} upload initiated",
                "document_id": document_id,
            },
            status_code=202,  # Accepted
        )

    except ValueError as ve:
        log.error(f"Upload asset error: {str(ve)}")
        raise HTTPException(
            status_code=400,
            detail=f"Invalid chat ID format: {chat_id}. Must be an integer.",
        )
    except Exception as e:
        log.error(f"Upload asset error: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/text_asset/{chat_id}")
async def add_text_asset(
    chat_id: str,
    text_content: str = Form(...),
    title: str = Form(...),
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
    mongo_db=Depends(lambda: get_one_mongo_db("lightrag")),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    """Add a text asset to the chat"""
    try:
        log.set_chat_id(chat_id)
        chat_id_int = int(chat_id)

        # Verify chat exists and user has access
        chat = _REPO(user, db).get_chat_details(chat_id=chat_id_int)
        if not chat:
            raise HTTPException(
                status_code=404,
                detail="Chat not found",
            )

        # Get current assets
        assets_result = _REPO(user, db).get_assets(chat_id_int)
        current_assets = []
        if assets_result and assets_result[0]["n.assets"]:
            try:
                current_assets = json.loads(assets_result[0]["n.assets"])
                if not isinstance(current_assets, list):
                    current_assets = []
            except (json.JSONDecodeError, TypeError) as e:
                log.error(f"Error parsing existing assets: {e}")
                current_assets = []

        log.info(f"Current assets loaded: {len(current_assets)} items")

        # Calculate tokens
        existing_content = ""
        for asset in current_assets:
            if isinstance(asset, dict) and "content" in asset:
                existing_content += asset["content"] + "\n"
        existing_tokens = calculate_token(existing_content)["tokens"]
        new_text_tokens = calculate_token(text_content)["tokens"]

        if existing_tokens + new_text_tokens > 8000:
            raise HTTPException(
                status_code=400,
                detail="Adding this text would exceed the 8000 token limit",
            )

        # Generate document ID
        document_id = str(uuid.uuid4())
        filename = f"{title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        # Update document_ids in Neo4j immediately
        try:
            current_doc_ids = []
            if assets_result and assets_result[0].get("n.document_ids"):
                current_doc_ids = json.loads(assets_result[0]["n.document_ids"])
            if document_id not in current_doc_ids:
                current_doc_ids.append(document_id)

            _REPO(user, db).update_chat(
                chat_id_int, {"document_ids": json.dumps(current_doc_ids)}
            )
        except Exception as e:
            print(f"Error updating document_ids: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to update document IDs")

        # Create MongoDB document for the text asset
        await create_asset_document(document_id, filename, "", "", user, title)

        # Update status to completed immediately since no processing is needed
        await update_asset_status(
            document_id, "Text asset added", UploadStatus.COMPLETED
        )

        # Create new asset
        new_asset = {
            "filename": filename,
            "content": text_content,
            "type": "text",
            "document_id": document_id,
            "title": title,
        }

        # Properly append new asset to existing assets
        current_assets.append(new_asset)
        log.info(f"Updated assets list now contains {len(current_assets)} items")

        # Get current document IDs and append new one
        try:
            current_doc_ids = json.loads(chat.get("document_ids", "[]"))
            if not isinstance(current_doc_ids, list):
                current_doc_ids = []
        except (json.JSONDecodeError, TypeError):
            current_doc_ids = []

        current_doc_ids.append(document_id)

        # Prepare update data
        update_data = {
            "assets": json.dumps(current_assets),
        }

        log.info(f"Updating chat with {len(current_assets)} total assets")

        # Update the chat
        _REPO(user, db).update_chat(chat_id_int, update_data)

        # Verify the update
        updated_assets = _REPO(user, db).get_assets(chat_id_int)
        if updated_assets and updated_assets[0]["n.assets"]:
            updated_assets_list = json.loads(updated_assets[0]["n.assets"])
            log.info(
                f"Verification - total assets after update: {len(updated_assets_list)}"
            )

        # After successfully adding the text asset, update discussion_so_far
        assets_content = "\n".join(
            [asset.get("content", "") for asset in current_assets]
        )
        _REPO(user, db).update_discussion_so_far(chat_id_int, assets_content)

        return JSONResponse(
            content={
                "message": "Text asset added successfully",
                "document_id": document_id,
                "total_assets": len(current_assets),
            },
            status_code=200,
        )

    except ValueError as ve:
        log.error(f"Add text asset error: {str(ve)}")
        raise HTTPException(
            status_code=400,
            detail=f"Invalid chat ID format: {chat_id}. Must be an integer.",
        )
    except Exception as e:
        log.error(f"Add text asset error: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{chat_id}/assets")
async def list_assets(
    chat_id: str,
    user: dict = Depends(get_current_user),
    db: Neo4jDB = Depends(Neo4jDB().get),
):
    try:
        log.set_chat_id(chat_id)
        chat_id_int = int(chat_id)
        result = _REPO(user, db).get_assets(chat_id_int)

        if not result:
            raise HTTPException(status_code=404, detail=f"Chat {chat_id} not found")

        assets = []
        document_ids = set()

        # Parse assets and document_ids
        existing_assets = []
        if result[0]["n.assets"]:
            existing_assets = json.loads(result[0]["n.assets"])
        if result[0]["n.document_ids"]:
            document_ids = set(json.loads(result[0]["n.document_ids"]))

        # Create a set to track processed document_ids
        processed_docs = set()

        # First, process all documents from MongoDB
        client, assets_collection = get_mongo_assets_collection()

        try:
            for doc_id in document_ids:
                processed_docs.add(doc_id)
                doc = assets_collection.find_one({"_id": doc_id})
                asset_content = ""

                # Find content and metadata from existing assets if available
                for existing_asset in existing_assets:
                    if existing_asset.get("document_id") == doc_id:
                        asset_content = existing_asset.get("content", "")
                        break

                if doc:
                    status = doc.get("status", "unknown")
                    upload_status = doc.get("upload_status", "unknown")
                    created_by = doc.get("created_by", "")
                    filename = doc.get("filename", "")

                    final_status = "completed"
                    if status == "OCR in progress" or upload_status == UploadStatus.OCR:
                        final_status = "processing"
                    elif status != "OCR completed":
                        final_status = status

                    # Add file_path calculation
                    file_path = f"pasco-ocr-files-{settings.STAGE}/assets/{chat_id_int}/uploaded/{filename}"

                    asset = {
                        "document_id": doc_id,
                        "title": doc.get("title", ""),
                        "filename": filename,
                        "status": final_status,
                        "content": asset_content,
                        "created_by": created_by,
                        "file_path": file_path
                    }
                else:
                    # If document not found in MongoDB, use metadata from existing assets
                    for existing_asset in existing_assets:
                        if existing_asset.get("document_id") == doc_id:
                            filename = existing_asset.get("filename", "")
                            # Add file_path calculation
                            file_path = f"pasco-ocr-files-{settings.STAGE}/assets/{chat_id_int}/uploaded/{filename}"
                            asset = {
                                "document_id": doc_id,
                                "title": existing_asset.get("title", ""),
                                "filename": filename,
                                "status": "completed",
                                "content": existing_asset.get("content", ""),
                                "created_by": "",  # Default empty since not in MongoDB
                                "file_path": file_path
                            }
                            break
                    else:
                        # If no matching asset found
                        asset = {
                            "document_id": doc_id,
                            "title": "",
                            "filename": "",
                            "status": "unknown",
                            "content": "",
                            "created_by": "",
                            "file_path": ""
                        }
                assets.append(asset)

            # Process all existing assets (including text assets)
            for existing_asset in existing_assets:
                doc_id = existing_asset.get("document_id")

                # Skip if we've already processed this document
                if doc_id and doc_id in processed_docs:
                    continue

                # Handle text assets
                if existing_asset.get("type") == "text":
                    # Try to get created_by from MongoDB if available
                    created_by = ""
                    filename = existing_asset.get("filename", "")
                    if doc_id:
                        doc = assets_collection.find_one({"_id": doc_id})
                        if doc:
                            created_by = doc.get("created_by", "")
                    # Add file_path for text assets too
                    file_path = f"pasco-ocr-files-{settings.STAGE}/assets/{chat_id_int}/uploaded/{filename}"
                    asset = {
                        "document_id": doc_id if doc_id else "",
                        "title": existing_asset.get("title", "Text Asset"),
                        "filename": filename,
                        "status": "completed",
                        "content": existing_asset.get("content", ""),
                        "created_by": created_by,
                        "file_path": file_path
                    }
                    assets.append(asset)

        finally:
            client.close()

        return JSONResponse(
            content=assets,
            status_code=200,
        )

    except ValueError as ve:
        log.error(f"List assets error: {str(ve)}")
        raise HTTPException(
            status_code=400,
            detail=f"Invalid chat ID format: {chat_id}. Must be an integer.",
        )
    except Exception as e:
        log.error(f"List assets error: {str(e)}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


async def process_textract_job(
    job_id: str,
    assert_s3: AssertS3,
    filename: str,
    document_id: str,
    chat_id_int: int,
    title: str,
    user: dict,
    db: Neo4jDB,
):
    log.set_chat_id(chat_id_int)
    log.debug(
        f"Starting Textract job processing for job_id: {job_id}, filename: {filename}"
    )

    # Create a new MongoDB client for this job
    mongo_client = MongoClient(settings.MONGO_URL)

    try:
        # Initialize Textract client
        textract = boto3.client(
            "textract",
            region_name=settings.AWS_REGION,
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID_S3_Textract,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY_S3_Textract,
        )

        log.info(f"Waiting for Textract job {job_id} to complete...")

        # Wait for job completion
        while True:
            response = textract.get_document_text_detection(JobId=job_id)
            status = response["JobStatus"]
            log.info(f"Current job status: {status}")

            if status in ["SUCCEEDED", "FAILED"]:
                break

            await asyncio.sleep(5)  # Wait 5 seconds before checking again

        if status == "SUCCEEDED":
            log.info(f"Job {job_id} completed successfully, processing pages...")

            # Initialize empty text
            extracted_text = ""

            # Get all pages
            pages = []
            next_token = None

            while True:
                if next_token:
                    response = textract.get_document_text_detection(
                        JobId=job_id, NextToken=next_token
                    )
                else:
                    response = textract.get_document_text_detection(JobId=job_id)

                pages.extend(response["Blocks"])

                if "NextToken" in response:
                    next_token = response["NextToken"]
                    log.debug(f"Processing next page, token: {next_token[:20]}...")
                else:
                    break

            # Extract text from all blocks
            for block in pages:
                if block["BlockType"] == "LINE":
                    extracted_text += block["Text"] + "\n"

            log.debug(f"Extracted text length: {len(extracted_text)} characters")
            log.debug(f"First 200 characters of extracted text: {extracted_text[:200]}")

            try:
                # Get fresh assets data
                assets_result = _REPO(user, db).get_assets(chat_id_int)
                current_assets = []
                current_document_ids = []

                if assets_result and assets_result[0]:
                    if assets_result[0].get("n.assets"):
                        current_assets = json.loads(assets_result[0]["n.assets"])
                    if assets_result[0].get("n.document_ids"):
                        current_document_ids = json.loads(
                            assets_result[0]["n.document_ids"]
                        )

                # Create new asset
                new_asset = {
                    "filename": filename,
                    "content": extracted_text,
                    "type": "extracted",
                    "document_id": document_id,
                    "title": title,
                }

                log.info(f"Adding new asset for document {document_id}")
                current_assets.append(new_asset)

                if document_id not in current_document_ids:
                    current_document_ids.append(document_id)

                # Update chat with new assets
                update_result = _REPO(user, db).update_chat(
                    chat_id_int,
                    {
                        "assets": json.dumps(current_assets),
                        "document_ids": json.dumps(current_document_ids),
                    },
                )
                log.info(f"Chat update result: {update_result}")

                # Update discussion_so_far with all assets content
                assets_content = "\n".join(
                    [asset.get("content", "") for asset in current_assets]
                )
                _REPO(user, db).update_discussion_so_far(chat_id_int, assets_content)

                # Update final status in MongoDB
                await update_asset_status(
                    document_id, "OCR completed", UploadStatus.COMPLETED
                )
                log.info(f"Document {document_id} status updated to completed")

            except Exception as asset_error:
                log.error(f"Failed to update assets: {str(asset_error)}")
                traceback.print_exc()
                await update_asset_status(
                    document_id, str(asset_error), UploadStatus.ERROR
                )

        else:
            error_message = f"Textract job failed with status: {status}"
            log.error(error_message)
            await update_asset_status(document_id, error_message, UploadStatus.ERROR)

    except Exception as e:
        error_message = f"Error in process_textract_job: {str(e)}"
        log.error(error_message)
        traceback.print_exc()
        await update_asset_status(document_id, error_message, UploadStatus.ERROR)
    finally:
        mongo_client.close()
        log.info(f"Finished processing job {job_id} for document {document_id}")


def get_mongo_assets_collection():
    """Get MongoDB assets collection connection"""
    client = MongoClient(settings.MONGO_URL)
    db = client[settings.MONGO_DEFAULT_DATABASE]
    return client, db["assets"]


async def update_asset_status(
    document_id: str, status: str, upload_status: UploadStatus
):
    """Update asset status in MongoDB"""
    client, collection = get_mongo_assets_collection()
    try:
        collection.update_one(
            {"_id": document_id},
            {
                "$set": {
                    "status": status,
                    "upload_status": upload_status,
                    "updated_at": datetime.now(),
                }
            },
            upsert=True,
        )
    finally:
        client.close()


async def create_asset_document(
    document_id: str, filename: str, bucket: str, path: str, user: dict, title: str
):
    """Create new asset document in MongoDB"""
    client, collection = get_mongo_assets_collection()
    try:
        document_data = {
            "_id": document_id,
            "filename": filename,
            "bucket": bucket,
            "path": path,
            "created_by": user.get("name", ""),
            "updated_by": user.get("name", ""),
            "type": "default",
            "status": "OCR in progress",
            "upload_status": UploadStatus.OCR,
            "title": title,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
        }
        collection.insert_one(document_data)
    finally:
        client.close()
