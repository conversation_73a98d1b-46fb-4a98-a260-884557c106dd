from fastapi import APIRouter, Depends, Body
from app.utils.auth_utils import get_current_user
from app.database.neo4j_db import Neo4jDB
from typing import List
from app.crud.conditions_of_approval_repository import (
    ConditionsOfApprovalRepository,
)
from app.models.conditions_of_approval import (
    ConditionsOfApprovalInsert,
    ConditionsOfApprovalView,
    ConditionsQueryParams,
)
from app.utils.base_utils import application_error_handler

router = APIRouter(
    tags=["conditions_of_approval_v2"],
    responses={404: {"description": "Not found"}},
    prefix="/v2",
)

_REPO = ConditionsOfApprovalRepository


@router.post("/conditions_of_approval")
@application_error_handler
def create_new_conditions_of_approval(
    query: ConditionsQueryParams = Depends(),
    body: ConditionsOfApprovalInsert = Body(...),
    db=Depends(Neo4jDB().get),
    user=Depends(get_current_user),
):
    body.user_name = user["name"]
    return _REPO(user, db).create_conditions_of_approval(
        layout_id=query.layout_id,
        body=body.model_dump(),
    )


@router.get("/conditions_of_approval", response_model=List[ConditionsOfApprovalView])
@application_error_handler
def get_all_conditions_of_approval(
    query: ConditionsQueryParams = Depends(),
    db=Depends(Neo4jDB().get),
    user=Depends(get_current_user),
):
    return _REPO(user, db).get_all_conditions_of_approval(
        layout_id=query.layout_id,
    )
