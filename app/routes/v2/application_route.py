from fastapi import APIRouter, Depends, Body
from app.utils.auth_utils import get_current_user
from app.models.application import ApplicationInsert, ApplicationView, StatusEnum
from app.database.neo4j_db import Neo4jDB
from app.crud.application_repository import ApplicationRepository
from typing import List
from app.utils.base_utils import application_error_handler


router = APIRouter(
    tags=["application_v2"],
    responses={404: {"description": "Not found"}},
    prefix="/v2",
)

_REPO = ApplicationRepository


@router.post("/application")
@application_error_handler
def create_new_application(
    mpud: ApplicationInsert = Body(...),
    db=Depends(Neo4jDB().get),
    user=Depends(get_current_user),
):
    mpud.user_name = user["name"]
    return _REPO(user, db).create_application(mpud.model_dump())


@router.get("/applications", response_model=List[ApplicationView])
@application_error_handler
def get_all_applications(
    user=Depends(get_current_user),
    db=Depends(Neo4jDB().get),
):
    return _REPO(user, db).get_all_applications()


@router.get("/debug_user")
def debug_user(user=Depends(get_current_user)):
    return {"user_id": user.get("cognito:username")}


@router.patch("/application/{application_id}")
@application_error_handler
def update_application(
    application_id: str,
    status: dict = Body(...),
    db=Depends(Neo4jDB().get),
    user=Depends(get_current_user),
):
    status_enum = StatusEnum(status.get("status"))
    return _REPO(user, db).update_application_status(application_id, status_enum)

