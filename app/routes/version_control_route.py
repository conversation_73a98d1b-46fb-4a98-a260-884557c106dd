import traceback
from fastapi import APIRouter, Depends, Query
from fastapi.responses import JSONResponse, StreamingResponse
from app.crud.chat_repository import ChatRepository
from app.crud.gen_pdf_repository import create_pdf
from app.database.database_storage import (
    get_mongo_db,
    get_milvus_client,
    Database,
    get_one_mongo_db,
)
from app.crud.version_control_repository import (
    get_mpud_versions,
    get_file_data,
    create_files_version_and_return_blob,
    get_mpud_version_data,
    insert_mpud_version_data,
    delete_rec_in_mpud_version_data,
    update_rec_in_mpud_version_data,
    reject_rec_in_mpud_version_data,
    get_mpud_document_preview_data,
)

from app.database.neo4j_db import Neo4jDB
from app.models.version_control_model import VersionControlList, createMpudVersion
from app.crud.milvus.base_repository import BaseRepository as MilvusRepo
from app.utils.auth_utils import get_current_user
import ast
from app.utils.notification_utils import create_notification

_MODEL_versionControlList = VersionControlList
_MODEL_createMpudVersion = createMpudVersion
_CHAT_REPO = ChatRepository

router = APIRouter(
    prefix="/versionControl",
    tags=["version_control"],
    responses={404: {"description": "Not found"}},
)

# connect_milvus_db("version_data")


def get_version_data_milvus_client():
    return get_milvus_client("version_data")


_MILVUS_Version_Coll = MilvusRepo("version_data")


@router.get("/get_version_for_docs", response_model=_MODEL_versionControlList)
async def get_version_for_docs(
    county_name: str,
    user=Depends(get_current_user),
    db: Database = Depends(get_mongo_db),
):
    resp = get_mpud_versions(user['custom:county'], user["cognito:username"], db)
    if resp["versions"] == []:
        resp["message"] = "no versions available"
        return resp
    return resp


@router.get("/get_file_data_for_version")
async def get_file_data_for_version(
    mpud_versions_id: str,
    user=Depends(get_current_user),
    db: Database = Depends(get_mongo_db),
):

    resp = get_file_data(mpud_versions_id, user["cognito:username"], db)

    if "fileData" in resp and "fileName" in resp:
        return StreamingResponse(
            resp["fileData"],
            media_type="application/pdf",
            headers={"Content-Disposition": f"attachment; filename={resp['fileName']}"},
        )
    else:
        return resp


@router.post("/create_file_version_for_county_and_get_blob")  # notify
async def create_file_version_for_county(
    county_name: str,
    file_name: str,
    chat_id: str,
    user=Depends(get_current_user),
    db: Database = Depends(get_mongo_db),
    milvusDB=Depends(get_version_data_milvus_client),
):  # file: UploadFile = File(...)
    # pdf_data = await file.read()
    resp = create_files_version_and_return_blob(
        user['custom:county'],
        user["cognito:username"],
        file_name,
        chat_id,
        _MILVUS_Version_Coll,
        db,
        milvusDB,
    )

    if "fileData" in resp and "fileName" in resp:
        create_notification(
            user["cognito:username"],
            "New file was created",
            f"New file '{resp['fileName']}' was created by {user['email']} in a conversation you are part of.",
            chat_id,
            chat_id,
        )
        return StreamingResponse(
            resp["fileData"],
            media_type="application/pdf",
            headers={"Content-Disposition": f"attachment; filename={resp['fileName']}"},
        )
    else:
        return resp


@router.get("/get_mpud_version_data")
async def get_mpud_version_data_from_db(
    chat_id: str,
    county_name: str,
    user=Depends(get_current_user),
    db: Database = Depends(get_mongo_db),
    milvusDb=Depends(get_version_data_milvus_client),
):
    resp = get_mpud_version_data(
        chat_id,
        user["cognito:username"],
        user['custom:county'],
        _MILVUS_Version_Coll,
        db,
        milvusDb,
    )
    if not resp:
        return {"data": resp, "message": "No data available"}
    return {"data": resp[0]}


@router.get("/get_mpud_document_preview_data")
async def get_mpud_document_preview_data_from_db(
    chat_id: str, user=Depends(get_current_user), db: Database = Depends(get_mongo_db),
):
    resp = get_mpud_document_preview_data(chat_id, user["cognito:username"], db)
    if resp:
        return {"data": resp}
    else:
        return {"data": resp, "message": "no data available for chatId"}


@router.post("/post_mpud_version_data")  # notify
async def post_mpud_version_data_based_to_version(
    chat_id: str,
    county_name: str,
    version_data: _MODEL_createMpudVersion,
    rule_ids: str = Query(
        ..., description="list of ids in quotes separated by commas enclosed  in []",
    ),
    user=Depends(get_current_user),
    db: Database = Depends(get_mongo_db),
    milvusDB=Depends(get_version_data_milvus_client),
):
    version_data = version_data.model_dump()
    rule_ids = ast.literal_eval(rule_ids)
    resp, title_rules = insert_mpud_version_data(
        chat_id,
        version_data,
        user["cognito:username"],
        user['custom:county'],
        rule_ids,
        _MILVUS_Version_Coll,
        db,
        milvusDB,
    )
    create_notification(
        user["cognito:username"],
        f"New rules were accepted by {user['email']}",
        f"New rules were accepted: {''.join(title_rules)}",
        chat_id,
        chat_id,
    )
    return resp


@router.delete("/delete_rec_in_mpud_version_data")
async def delete_rec_in_mpud_version(
    chat_id: str, rule_ids: str, db: Database = Depends(get_mongo_db),
):
    upsert_result = delete_rec_in_mpud_version_data(
        chat_id, _MILVUS_Version_Coll, rule_ids, get_version_data_milvus_client(), db,
    )
    return upsert_result


@router.patch("/update_rec_in_mpud_version_data")  # notify
def update_rec_in_mpud_version(
    chat_id: str,
    update_data: str,
    rule_id: str,
    db: Database = Depends(get_mongo_db),
    user=Depends(get_current_user),
):
    db = get_mongo_db()
    updated_result = update_rec_in_mpud_version_data(
        chat_id,
        update_data,
        user['custom:county'],
        user["cognito:username"],
        _MILVUS_Version_Coll,
        get_version_data_milvus_client(),
        db,
        rule_id,
    )
    create_notification(
        user["cognito:username"],
        "Rules were updated",
        f"Rules were updated by {user['email']} in a conversation you are part of.",
        chat_id,
        chat_id,
    )
    return updated_result


@router.put("/reject_rec_in_mpud_version_data")  # notify
async def reject_rec_in_mpud_version(
    chat_id: str,
    rule_ids: str = Query(
        ...,
        description="A string representing list of ids in quotes separated by commas enclosed  in []",
    ),
    db: Database = Depends(get_mongo_db),
    user=Depends(get_current_user),
):
    upsert_result = reject_rec_in_mpud_version_data(rule_ids, db)
    create_notification(
        user["cognito:username"],
        "Rules were rejected",
        f"Some rules were rejected by by {user['email']} in a conversation you are part of.",
        chat_id,
        chat_id,
    )
    return upsert_result


@router.get("/generate_pdf", summary="Generate COA PDF")
def generate_pdf(
        chat_id=Query(..., description="Chat id of the Application"),
        db=Depends(Neo4jDB().get),
        user=Depends(get_current_user),
        mongo_db=Depends(lambda: get_one_mongo_db("lightrag")),
):
    try:
        data = _CHAT_REPO(user, db).get_chat_data(chat_id=chat_id, mongo_db=mongo_db)
        pdf_data = create_pdf(data, chat_id=chat_id)
        return StreamingResponse(
            pdf_data,
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=MPUD_{chat_id}.pdf",
            },
        )
    except Exception as e:
        error_traceback = traceback.format_exc()
        return JSONResponse(
            status_code=500,
            content={
                "message": f"An error occurred: {str(e)}",
                "traceback": error_traceback,
            },
        )
