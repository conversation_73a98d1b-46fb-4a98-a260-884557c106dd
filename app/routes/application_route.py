from fastapi import APIRouter, Depends, Body, HTTPException

from app.utils.application_utils import (
    create_application,
    get_all_applications,
    update_application_fields,
    get_application_by_id,
    delete_application,
)
from app.utils.base_utils import get_all_chats
from app.models.application import PriorityEnum, StatusEnum
from app.utils.auth_utils import get_current_user
from datetime import datetime
from typing import Optional
from fastapi.responses import JSONResponse

router = APIRouter(
    tags=["application"],
    responses={404: {"description": "Not found"}},
)


@router.post("/application")
def create_new_application(
    summary: str = Body(..., description="Summary of the application"),
    assignee: str = Body("", description="ID of the assignee"),
    due_date: Optional[datetime] = Body(
        None,
        description="Due date of the application.",
    ),
    priority: PriorityEnum = Body(
        PriorityEnum.LOW,
        description="Priority of the application, one of: 'high', 'mid', or 'low'.",
    ),
    status: StatusEnum = Body(
        StatusEnum.TODO,
        description="Status of the application, one of: 'todo', 'in-progress', or 'completed'.",
    ),
    user=Depends(get_current_user),
):
    """
    Create a new application.
    """
    county = user["custom:county"]
    application_id, application = create_application(
        summary,
        due_date,
        assignee,
        priority,
        status,
        county,
    )
    return {"application_id": str(application_id), "application": application}


@router.get("/applications")
def fetch_all_applications(user=Depends(get_current_user)):
    """
    Fetch all applications assigned to the current user.
    """
    county = user["custom:county"]
    applications = get_all_applications(county)
    return {"total_applications": len(applications), "applications": applications}


@router.get("/applications_status_count")
def application_status_count(user=Depends(get_current_user)):
    """
    Count applications by status ('todo', 'in progress', 'completed') and return total applications.
    """
    county = user["custom:county"]
    applications = get_all_applications(county)
    status_counts = {"todo": 0, "in_progress": 0, "completed": 0}
    total_applications = len(applications)
    for app in applications:
        status = app.get("status", "")
        if status == "todo":
            status_counts["todo"] += 1
        elif status == "In progress":
            status_counts["in_progress"] += 1
        elif status == "Completed":
            status_counts["completed"] += 1

    status_counts["total_applications"] = total_applications

    return status_counts


@router.get("/application/{application_id}")
def fetch_application(application_id: str, user=Depends(get_current_user)):
    """
    Fetch a specific application by its ID.
    """
    application = get_application_by_id(application_id)
    if not application:
        raise HTTPException(
            status_code=404,
            detail="Application not found",
        )
    return application


@router.patch("/application/{application_id}")
def update_application(
    application_id: str,
    updated_fields: dict = Body(..., description="Fields to update in COA"),
    user=Depends(get_current_user),
):
    """
    Update a specific field in an application.
    """
    result = update_application_fields(application_id, updated_fields)
    if result.acknowledged :
        return JSONResponse(status_code=200, content={"message": "Successfully updated"})
    else :
        return JSONResponse(status_code=500, content={"message": "An error occurred with updating field"})


@router.delete("/application/{application_id}")
def delete_application_route(application_id: str, user=Depends(get_current_user)):
    """
    Delete an application by its ID.
    """
    result = delete_application(application_id)
    if result.deleted_count == 0:
        raise HTTPException(
            status_code=404,
            detail="Application not found or not deleted",
        )
    return {"message": "Application deleted successfully"}


@router.get("/application/{application_id}/chats")
def get_all_user_chats(application_id: str, user=Depends(get_current_user)):
    all_chats = get_all_chats(user["cognito:username"], application_id=application_id)
    return {"total_chats": len(all_chats), "chats": all_chats}
