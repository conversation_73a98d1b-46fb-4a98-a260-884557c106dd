from fastapi import (
    APIRouter,
    Body,
    HTTPException,
    Path,
)
from app.core.config import Settings
from app.models.permission_model import RolePermissionResponse, RolePermissionUpdate
from app.models.user_model import Roles

from app.utils.userconfig_utils import get_all_permissions, get_role_permissions, update_role_permissions


_SHOW_NAME = "permissions"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}},
)
settings = Settings()


@router.get("", response_model=RolePermissionResponse)
async def get_permissions():
    """Get all role permissions."""
    try:
        permissions = await get_all_permissions()
        return RolePermissionResponse(
            message="Permissions retrieved successfully",
            data={"permissions": permissions},
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{role}", response_model=RolePermissionResponse)
async def get_permissions_by_role(
    role: Roles = Path(..., description="The role to retrieve"),
):
    """Get permissions for a specific role."""
    try:
        permissions = await get_role_permissions(role)
        if not permissions:
            raise HTTPException(status_code=404, detail="Role not found")
        return RolePermissionResponse(
            message="Role permissions retrieved successfully",
            data=permissions,
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{role}", response_model=RolePermissionResponse)
async def update_permissions(
        role: Roles = Path(..., description="The role to update"),
        permissions: RolePermissionUpdate = Body(..., description="The new permissions for the role")):
    """Update permissions for a specific role."""
    try:
        if role == Roles.superadmin:
            raise HTTPException(
                status_code=403,
                detail="Superadmin permissions cannot be modified",
            )
        success = await update_role_permissions(role, permissions.permissions)
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Role not found or permissions not modified",
            )

        return RolePermissionResponse(
            message="Role permissions updated successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
