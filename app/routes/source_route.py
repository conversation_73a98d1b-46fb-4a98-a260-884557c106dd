from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, <PERSON>
from typing import Optional
from fastapi.responses import StreamingResponse, Response
import boto3
from botocore.exceptions import ClientError
from app.core.config import settings
from app.crud.ocr.main import Textract
from app.utils.auth_utils import get_current_user
from app.database.database_storage import get_mongo_db
import fitz  # PyMuPDF
from docx import Document
from io import BytesIO


textract = Textract()

router = APIRouter(
    prefix="/source",
    tags=["source"],
    responses={404: {"description": "Not found"}},
)

BUCKET_NAME = f"pasco-ocr-files-{settings.STAGE}"


@router.get("/file/{file_path:path}")
async def view_pdf(file_path: str):
    """
    Stream a PDF file from an S3 bucket. This is a public route that doesn't require authentication.
    """
    bucket_name = BUCKET_NAME
    s3_client = boto3.client(
        's3',
        region_name=settings.AWS_REGION,
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    )

    try:
        # Get the file object from S3
        response = s3_client.get_object(Bucket=bucket_name, Key=file_path)

        # Check if the file is a PDF
        content_type = response['ContentType']
        if content_type != 'application/pdf':
            raise HTTPException(status_code=400, detail="The requested file is not a PDF")

        # Create a generator to stream the file content
        def iterate_bytes():
            for chunk in response['Body'].iter_chunks(chunk_size=8192):
                yield chunk

        # Extract the filename from the path
        filename = file_path.split('/')[-1]

        # Return a StreamingResponse
        return StreamingResponse(
            iterate_bytes(),
            media_type='application/pdf',
            headers={
                'Content-Disposition': f'inline; filename="{filename}"',
            },
        )

    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            raise HTTPException(status_code=404, detail="File not found")
        elif e.response['Error']['Code'] == 'AccessDenied':
            raise HTTPException(status_code=403, detail="Access denied to the file")
        else:
            raise HTTPException(status_code=500, detail=f"Error retrieving file: {str(e)}")


@router.get("/page/{file_path:path}")
async def view_file_page(
    file_path: str,
    page_number: int = Query(..., ge=1),
    user=Depends(get_current_user),
):
    """
    View a specific page of a PDF or DOCX file as an image. This is a public route that doesn't require authentication.
    """
    bucket_name = BUCKET_NAME
    s3_client = boto3.client(
        's3',
        region_name=settings.AWS_REGION,
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    )

    try:
        # Get the file object from S3
        response = s3_client.get_object(Bucket=bucket_name, Key=file_path)

        # Check file type
        content_type = response['ContentType']
        if content_type == 'application/pdf':
            return await process_pdf_page(response, page_number)
        elif content_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            return await process_docx_page(response, page_number)
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")

    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            raise HTTPException(status_code=404, detail="File not found")
        elif e.response['Error']['Code'] == 'AccessDenied':
            raise HTTPException(status_code=403, detail="Access denied to the file")
        else:
            raise HTTPException(status_code=500, detail=f"Error retrieving file: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")


async def process_pdf_page(response, page_number):
    # Read the PDF file
    pdf_data = response['Body'].read()
    pdf_document = fitz.open(stream=pdf_data, filetype="pdf")

    # Check if the requested page number is valid
    if page_number > len(pdf_document):
        raise HTTPException(status_code=400, detail="Invalid page number")

    # Get the requested page
    page = pdf_document[page_number - 1]  # PyMuPDF uses 0-based indexing

    # Render the page as an image
    zoom = 2  # to increase resolution
    mat = fitz.Matrix(zoom, zoom)
    pix = page.get_pixmap(matrix=mat)

    # Convert the pixmap to PNG image data
    img_data = pix.tobytes("png")

    # Close the PDF document
    pdf_document.close()

    # Return the image
    return Response(content=img_data, media_type="image/png")


async def process_docx_page(response, page_number):
    # Read the DOCX file
    docx_data = response['Body'].read()
    doc = Document(BytesIO(docx_data))

    # Check if the requested page number is valid
    if page_number > len(doc.paragraphs):
        raise HTTPException(status_code=400, detail="Invalid page number")

    # Create a new document with only the requested page
    new_doc = Document()
    for i in range(page_number - 1, min(page_number * 10, len(doc.paragraphs))):
        new_doc.add_paragraph(doc.paragraphs[i].text)

    # Save the new document to a BytesIO object
    docx_output = BytesIO()
    new_doc.save(docx_output)
    docx_output.seek(0)

    # Convert DOCX to image (you may need to implement this part)
    # For simplicity, we'll return the DOCX file instead of an image
    return StreamingResponse(docx_output, media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document")


class PageModel(BaseModel):
    file_path: str = Field(..., description="The path of the file in the S3 bucket")
    page_number: int = Field(..., ge=1, description="The page number to render as an image")
    source_text: str = Field(..., description="Source text associated with the PDF page request")
    width: Optional[int] = Field(None, ge=1, description="Optional width of the rendered image")
    height: Optional[int] = Field(None, ge=1, description="Optional height of the rendered image")


@router.post("/page/")
async def post_pdf_page(page_data: PageModel, db=Depends(get_mongo_db)):
    s3_client = boto3.client(
        's3',
        region_name=settings.AWS_REGION,
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    )

    try:
        # Fetch coordinates for text highlighting
        coords = textract.find_text_coordinates(page_data.page_number, page_data.source_text, page_data.file_path, db)
        print(coords)
        if not coords:
            raise HTTPException(status_code=404, detail="Coordinates not found for the specified text")

        # Get the file object from S3
        response = s3_client.get_object(Bucket=BUCKET_NAME, Key=page_data.file_path)

        # Ensure the file is a PDF
        content_type = response['ContentType']
        if content_type != 'application/pdf':
            raise HTTPException(status_code=400, detail="The requested file is not a PDF")

        # Read PDF data and load the specific page
        pdf_data = response['Body'].read()
        pdf_document = fitz.open(stream=pdf_data, filetype="pdf")
        if page_data.page_number > len(pdf_document):
            raise HTTPException(status_code=400, detail="Invalid page number")

        page = pdf_document[page_data.page_number - 1]

        # Convert normalized coordinates to absolute values based on page size
        for text in coords:
            box = coords[text]
            rect_x0 = box['top_left']['X'] * page.rect.width
            rect_y0 = box['top_left']['Y'] * page.rect.height
            rect_x1 = box['bottom_right']['X'] * page.rect.width
            rect_y1 = box['bottom_right']['Y'] * page.rect.height

            # Create a rectangle for highlighting
            highlight_rect = fitz.Rect(rect_x0, rect_y0, rect_x1, rect_y1)

            # Add a transparent yellow highlight annotation
            highlight = page.add_highlight_annot(highlight_rect)
            highlight.set_colors(stroke=None, fill=(1, 1, 0))  # Yellow color
            highlight.set_opacity(0.4)  # Semi-transparent
            highlight.update()  # Apply the highlight

        # Render the page with the annotation as an image
        zoom = 2  # Scale factor to increase resolution
        mat = fitz.Matrix(zoom, zoom)
        pix = page.get_pixmap(matrix=mat)

        # Resize if necessary
        if page_data.width and page_data.height:
            pix = pix.resize(page_data.width, page_data.height)

        # Convert the pixmap to PNG image data
        img_data = pix.tobytes("png")
        pdf_document.close()

        # Return the image with highlighted text
        return Response(content=img_data, media_type="image/png")

    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            raise HTTPException(status_code=404, detail="File not found")
        elif e.response['Error']['Code'] == 'AccessDenied':
            raise HTTPException(status_code=403, detail="Access denied to the file")
        else:
            raise HTTPException(status_code=500, detail=f"Error retrieving file: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing PDF: {str(e)}")
