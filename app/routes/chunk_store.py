from fastapi import APIRouter
from app.core.config import settings
from pymilvus import (
    connections,
    utility,
    Collection,
    CollectionSchema,
    FieldSchema,
    DataType
)
from voyageai import Client as VoyageClient
import os
import re
from typing import List
import fitz  # PyMuPDF


router = APIRouter(
    tags=["chunk_and_store"],
    responses={404: {"description": "Not found"}},
)

"""The below endpoints can be used to upload pdfs, create a collection in Milvus, store chunks in Milvus, and test the chunk retrieval from Milvus."""

SECTION_HEADERS = [
    "Environmental",
    "Open Space/Buffering",
    "Transportation/Circulation",
    "Access Management",
    "Dedication of Right-of-Way",
    "Design/Construction Specifications",
    "Utilities/Water Service/Wastewater Disposal",
    "Stormwater",
    "Emergency Management",
    "Land Use",
    "Procedures"
]


def detect_section(text: str) -> str:
    """Strict section header detection that only matches actual section headers"""
    text = text.strip()
    for header in SECTION_HEADERS:
        if text.startswith(header):
            return header

    for header in SECTION_HEADERS:
        if re.match(rf'^\d+\.\s*{re.escape(header)}\b', text, re.IGNORECASE):
            return header

    for header in SECTION_HEADERS:
        if re.match(rf'^{re.escape(header)}(?:\s*\([^)]+\))?\s*$', text, re.IGNORECASE):
            return header

    return None


def pdf_to_chunks(pdf_path: str, max_chunk_size: int = 500) -> List[dict]:
    chunks = []
    try:
        doc = fitz.open(pdf_path)
    except Exception as e:
        print(f"Failed to open {pdf_path}: {e}")
        return chunks

    filename = os.path.basename(pdf_path)
    doc_id = re.sub(r'[^a-z0-9_]', '', filename.replace(".pdf", "").lower())

    current_section = "General"
    buffer = []
    page_number = 1

    def flush_buffer():
        if buffer and current_section:
            chunk_text = " ".join(buffer).strip()
            if len(chunk_text.split()) >= 50:
                chunks.append({
                    "content": chunk_text,
                    "metadata": {
                        "source": filename,
                        "doc_id": doc_id,
                        "page": page_number,
                        "section": current_section
                    }
                })
            buffer.clear()

    for page_num in range(len(doc)):
        page_number = page_num + 1
        page = doc.load_page(page_num)
        text = page.get_text()
        lines = [line.strip() for line in text.split('\n') if line.strip()]

        for line in lines:
            new_section = detect_section(line)
            if new_section:
                flush_buffer()
                current_section = new_section
            else:
                buffer.append(line)

    flush_buffer()
    return chunks


def process_all_pdfs(folder_path: str, chunk_word_size: int = 200) -> List[dict]:
    all_chunks = []
    for filename in os.listdir(folder_path):
        if filename.lower().endswith(".pdf"):
            pdf_path = os.path.join(folder_path, filename)
            chunks = pdf_to_chunks(pdf_path, chunk_word_size)
            all_chunks.extend(chunks)
    return all_chunks


def embed_with_voyage(texts: List[str], batch_size: int = 50) -> List[List[float]]:
    """
    Embed the given list of texts in batches using Voyage AI.
    Adjust batch_size as needed so that the total tokens per batch doesn't exceed the limit.
    """
    voyage_client = VoyageClient(api_key=settings.VOYAGE_API_KEY)
    all_embeddings = []

    for i in range(0, len(texts), batch_size):
        batch = texts[i:i + batch_size]
        try:
            response = voyage_client.embed(batch, model="voyage-3-large")

            all_embeddings.extend(response.embeddings)
        except Exception as e:
            print(f"Error embedding batch starting at index {i}: {e}")

            raise e
    return all_embeddings


def store_chunks_in_milvus(chunks: List[dict], embeddings: List[List[float]], collection_name: str):
    connections.connect(
        token=settings.MILVUS_TOKEN,
        uri=settings.MILVUS_URI,
        db_name=settings.MILVUS_DEFAULT_DATABASE,
    )

    # Pre-process all chunks to ensure content is always a string
    collection = Collection(collection_name)

    # Prepare data lists
    contents = []
    sources = []
    pages = []
    doc_ids = []
    sections = []

    # Process each chunk
    for chunk in chunks:
        metadata = chunk["metadata"]
        contents.append(chunk["content"])
        sources.append(metadata["source"])
        pages.append(int(metadata["page"]))
        doc_ids.append(metadata["doc_id"])
        sections.append(metadata["section"])

    # Create insert data in schema order (excluding auto_id primary key)
    data = [
        contents,
        sources,
        pages,
        doc_ids,
        sections,
        embeddings
    ]
    # Insert data
    collection.insert(data)

    collection.flush()

    return {
        "status": "success",
        "inserted_count": len(chunks),
        "collection": collection_name,
        "message": f"Stored {len(chunks)} MPUD chunks with embeddings"
    }


@router.post("/upload-pdfs")
async def upload_pdfs(pdf_dir: str):
    pdf_chunks = process_all_pdfs(pdf_dir)
    if pdf_chunks:
        texts = [chunk["content"] for chunk in pdf_chunks]
        embeddings = embed_with_voyage(texts)
        store_chunks_in_milvus(pdf_chunks, embeddings, "Mpud_knowledge")
    else:
        print("No PDF chunks were produced.")
    return {"status": "ok", "chunk": pdf_chunks}


@router.post("/create-collection")
async def create_collection(collection_name: str = None):
    connections.connect(
        token=settings.MILVUS_TOKEN,
        uri=settings.MILVUS_URI,
        db_name=settings.MILVUS_DEFAULT_DATABASE,
    )
    collection_name = collection_name

    fields = [
        FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
        FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
        FieldSchema(name="source", dtype=DataType.VARCHAR, max_length=255),
        FieldSchema(name="page", dtype=DataType.INT64),
        FieldSchema(name="doc_id", dtype=DataType.VARCHAR, max_length=255),
        FieldSchema(name="section", dtype=DataType.VARCHAR, max_length=255),
        FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=1024)
    ]

    schema = CollectionSchema(fields, description="MPUD Knowledge", auto_id=True)
    collection = Collection(name=collection_name, schema=schema)

    index_params = {
        "metric_type": "COSINE",
        "index_type": "IVF_FLAT",
        "params": {"nlist": 1024}
    }
    collection.create_index(
        field_name="embedding",
        index_params=index_params
    )

    return {"status": "ok", "message": f"Collection {collection_name} created."}


@router.post("/store-chunk")
async def store_chunk(chunk: List[dict]):
    if chunk:
        texts = [chunk["content"] for chunk in chunk]
        embeddings = embed_with_voyage(texts)
        print("Embeddings--->", embeddings)
        store_chunks_in_milvus(chunk, embeddings, "Mpud_knowledge")
    else:
        print("No PDF chunks were produced.")
    return {"status": "ok", "chunk": chunk}


@router.post("/test-chunk")
async def test_chunk(msg: str):
    connections.connect(
        token=settings.MILVUS_TOKEN,
        uri=settings.MILVUS_URI,
        db_name=settings.MILVUS_DEFAULT_DATABASE,
    )

    collection_name = "Mpud_knowledge"

    if not utility.has_collection(collection_name):
        return {"status": "error", "message": f"Collection '{collection_name}' does not exist."}

    collection = Collection(collection_name)
    collection.load()

    expr = f'source == "{msg}"'
    results = collection.query(expr=expr, output_fields=["content", "source", "page", "doc_id", "section"])

    return {
        "status": "ok",
        "filename": msg,
        "chunks": results
    }
