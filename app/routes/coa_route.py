from fastapi import APIRouter, Depends, Body, HTTPException
from app.utils.coa_utils import create_coa, get_all_coas, update_coa, delete_coa
from app.utils.auth_utils import get_current_user
from typing import List
from app.models.coa import PriorityEnum
from datetime import datetime
from typing import Optional
from fastapi.responses import JSONResponse

router = APIRouter(
    tags=["coa"],
    responses={404: {"description": "Not found"}},
)


@router.post("/coa")
def create_new_coa(
    title: str = Body(..., description="Title of the COA"),
    summary: str = Body(..., description="Summary of the COA"),
    priority: str = Body(
        PriorityEnum.LOW,
        description="Priority of the COA: 'high', 'mid', 'low'",
    ),
    due_date: Optional[datetime] = Body(
        None,
        description="Due date of the COA",
    ),
    assignee: str = Body("", description="Assignee user ID"),
    file_name: str = Body(..., description="File name associated with the COA"),
    s3_url: str = Body(..., description="S3 URL where the file is stored"),
    coordinates: List[float] = Body(..., description="Array of coordinates"),
    page_numbers: List[int] = Body(..., description="Array of page numbers"),
    accepting_user_id: str = Body("", description="ID of the user accepting the COA"),
    application_id: str = Body(..., description="ID of the user accepting the COA"),
    chat_id: str = Body(..., description="ID of the chat this COA is related to"),
    user=Depends(get_current_user),
):
    """
    Create a new COA.
    """
    if due_date:
        due_date = due_date.replace(minute=59, hour=23, second=59, microsecond=999999)

    data = {
        "title": title,
        "summary": summary,
        "priority": priority,
        "due_date": due_date,
        "assignee": assignee,
        "file_name": file_name,
        "s3_url": s3_url,
        "coordinates": coordinates,
        "page_numbers": page_numbers,
        "accepting_user_id": accepting_user_id,
        "application_id": application_id,
        "chat_id": chat_id,
    }

    insert_id, coa = create_coa(data)
    return {"insert_id": str(insert_id), "coa": coa}


@router.get("/coas")
def get_all_coa_entries(application_id: str, user=Depends(get_current_user)):
    """
    Get all COA entries for an application.
    """
    coas = get_all_coas(application_id)
    return {"total_coas": len(coas), "coas": coas}


@router.patch("/coa/{coa_id}")
def update_coa_fields(
    coa_id: str,
    updated_fields: dict = Body(..., description="Fields to update in COA"),
    user=Depends(get_current_user),
):
    """
    Update multiple fields in a COA document.
    """
    result = update_coa(coa_id, updated_fields)

    if result :
        return JSONResponse(status_code=200, content={"message": 'Fields updated successfully'})
    return result


@router.delete("/coa/{coa_id}")
def delete_coa_entry(coa_id: str, user=Depends(get_current_user)):
    """
    Delete a COA entry by coa_id.
    """
    result = delete_coa(coa_id)
    if result == 1:
        return {"message": f"COA with id {coa_id} has been deleted."}
    else:
        raise HTTPException(
            status_code=404,
            detail=f"COA with id {coa_id} not found.",
        )
