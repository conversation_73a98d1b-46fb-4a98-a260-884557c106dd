from fastapi import APIRouter, Depends, HTTPException
from app.utils.base_utils import (
    get_history_from_chat,
    get_all_chats,
    toggle_chat_users,
    parse_all_users_response,
)
from fastapi.responses import StreamingResponse
from app.core.open_ai import OpenAI
from app.core.config import settings
from app.utils.auth_utils import get_current_user
import boto3
from pydantic import BaseModel
from typing import Optional
from botocore.exceptions import ClientError
from app.crud.inspector_repository import InspectorRepository
from app.core.inspector_core import process_coordinates, transform_chat_data
from app.utils.base_utils import (
    get_latest_n_messages_from_chat,
    create_new_message,
)
from app.core.inspector import chat
import json

inspector = InspectorRepository()


class ChatInput(BaseModel):
    message: str
    chat_id: Optional[str] = None
    model: str = "gpt-4o-mini"


client = boto3.client(
    "cognito-idp",
    region_name=settings.AWS_REGION,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
)
user_pool_id = settings.AWS_USER_POOL_ID

router = APIRouter(
    tags=["base"],
    responses={404: {"description": "Not found"}},
)
_OPEN_AI = OpenAI(settings.OPENAI_API_KEY)


# Updated Knowledge Source
@router.post("/chat")
async def create_chat(
    message: str,
    application_id: str,
    chat_id: str = None,
    model_name: str = "gpt-4o-mini",
    user=Depends(get_current_user),
):
    try:
        # Retrieve conversation history and generate response
        history, chat_id, chat_title = get_latest_n_messages_from_chat(
            chat_id,
            user["cognito:username"],
            application_id=application_id,
        )

        # Start streaming conversation response

        def generator():
            # Call the inspector generator
            if model_name not in ["gpt-4o-mini", "gpt-4o"]:
                msg = "Please use gpt-4o-mini or gpt-4o model"
                yield f"data: {json.dumps({'content': msg})}\n\n"
                return
            inspector.model = model_name
            final_response = {}
            # LLm Response
            for response in inspector.inspector(message, transform_chat_data(history)):
                response = process_coordinates(response)
                final_response = response
                # Convert each output to a JSON string formatted for streaming
                yield f"data: {json.dumps({'content': response, 'chat_id': chat_id, 'chat_title': chat_title})}\n\n"

            if "answer" in final_response and "structured_content" in final_response:
                answer = final_response["answer"]
                content = final_response["structured_content"]
                create_new_message(chat_id, answer, message, content, [])

        return StreamingResponse(generator(), media_type="text/event-stream")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chatv2")
async def create_chatv2(
    message: str,
    application_id: str,
    chat_id: str = None,
    model_name: str = "gpt-4o-mini",
    user=Depends(get_current_user),
):
    try:
        # Retrieve conversation history and generate response
        history, chat_id, chat_title = get_latest_n_messages_from_chat(
            chat_id,
            user["cognito:username"],
            application_id=application_id,
        )

        # Start streaming conversation response

        async def generator():
            # Call the inspector generator
            if model_name not in ["gpt-4o-mini", "gpt-4o"]:
                msg = "Please use gpt-4o-mini or gpt-4o model"
                yield f"data: {json.dumps({'content': msg})}\n\n"
                return
            inspector.model = model_name
            final_response = {}
            # LLm Response
            async for response in chat(query=message):
                response = process_coordinates(response)
                final_response = response
                # Convert each output to a JSON string formatted for streaming
                yield f"data: {json.dumps({'content': response, 'chat_id': chat_id, 'chat_title': chat_title})}\n\n"

            if "answer" in final_response and "structured_content" in final_response:
                answer = final_response["answer"]
                content = final_response["structured_content"]
                create_new_message(chat_id, answer, message, content, [])

        return StreamingResponse(generator(), media_type="text/event-stream")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/history")
def get_chat_history(chat_id: str, limit: int = 10, user=Depends(get_current_user)):
    chat_title, chat_history = get_history_from_chat(chat_id, limit, user["cognito:username"])
    return {"total_conversations": len(chat_history), "title": chat_title, "messages": chat_history}


@router.get("/chats")
def get_all_user_chats(user=Depends(get_current_user)):
    all_chats = get_all_chats(user["cognito:username"])
    return {"total_chats": len(all_chats), "chats": all_chats}


@router.post("/chats/toggle_users")
def toggle_users_from_chat(
    chat_id: str,
    chat_users: dict,
    user=Depends(get_current_user),
):
    """Add/remove users to/from a chat. chat_users = {user_id: selected(boolean)}"""
    toggle_chat_users(chat_id, chat_users)
    return {"message": "Users updated for chat"}


@router.get("/chat/users")
async def get_users(chat_id: str):
    """Get all users. TODO: for a specific county only."""
    try:
        response = client.list_users(UserPoolId=user_pool_id)
        users = parse_all_users_response(response, chat_id)

        return {"total_users": len(users), "users": users}

    except client.exceptions.InvalidParameterException as e:
        raise HTTPException(status_code=400, detail=f"Invalid parameter: {e}")

    except client.exceptions.NotAuthorizedException:
        raise HTTPException(
            status_code=401,
            detail="Unauthorized: ID token may be invalid or expired.",
        )

    except ClientError as ce:
        raise HTTPException(status_code=500, detail=f"Cognito API error: {ce}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {e}")


@router.get("/view-file/{bucket_name}/{file_path:path}")
async def view_pdf(bucket_name: str, file_path: str):
    """
    Stream a PDF file from an S3 bucket.
    """
    s3_client = boto3.client(
        "s3",
        region_name=settings.AWS_REGION,
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    )

    try:
        # Get the file object from S3
        response = s3_client.get_object(Bucket=bucket_name, Key=file_path)

        # Check if the file is a PDF
        content_type = response["ContentType"]
        if content_type != "application/pdf":
            raise HTTPException(
                status_code=400,
                detail="The requested file is not a PDF",
            )

        # Create a generator to stream the file content
        def iterate_bytes():
            for chunk in response["Body"].iter_chunks(chunk_size=8192):
                yield chunk

        # Extract the filename from the path
        filename = file_path.split("/")[-1]

        # Return a StreamingResponse
        return StreamingResponse(
            iterate_bytes(),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f'inline; filename="{filename}"',
            },
        )

    except ClientError as e:
        if e.response["Error"]["Code"] == "NoSuchKey":
            raise HTTPException(status_code=404, detail="File not found")
        elif e.response["Error"]["Code"] == "AccessDenied":
            raise HTTPException(status_code=403, detail="Access denied to the file")
        else:
            raise HTTPException(
                status_code=500,
                detail=f"Error retrieving file: {str(e)}",
            )
