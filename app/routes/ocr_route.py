import os
import io
import boto3
from fastapi import (
    APIRouter,
    BackgroundTasks,
    UploadFile,
    File,
    Depends,
    HTTPException,
)
from fastapi.responses import JSONResponse, StreamingResponse
from app.database.database_storage import get_mongo_db, get_milvus_client, Database
from app.crud.ocr.main import Textract
from app.models.document import UploadStatus
from app.models.ocr_model import OcrResponseModel
from app.core.telemetry import get_logger

# from app.crud.knowledge_graph_repository import KnowledgeGraphRepository
from app.crud.knowledge_graph_v2_repository import KnowledgeGraphRepositoryV2
from app.utils.auth_utils import get_current_user
from app.utils.notification_utils import create_notification
from app.utils.appsync_utils import format_size, send_progress_event
from app.core.config import Settings


settings = Settings()
STAGE = settings.STAGE
BUCKET_NAME = f"pasco-ocr-files-{STAGE}"
FOLDER_PREFIX = "MPUD Records/"
_MODEL = OcrResponseModel

router = APIRouter(
    prefix="/ocr",
    tags=["ocr"],
    responses={404: {"description": "Not found"}},
)

logger = get_logger("pasco.ai.connector")
ocr = Textract()


def get_version_data_milvus_client():
    return get_milvus_client()


@router.post("/extract_file_and_store_content", response_model=_MODEL)
async def process_file(
    s3Location: str,
    background_tasks: BackgroundTasks,
    db: Database = Depends(get_mongo_db),
):
    bucket_name = s3Location.split("/")[0]
    location_name = "/".join(s3Location.split("/")[1:])
    background_tasks.add_task(ocr.extract_pdf, bucket_name, location_name, db)
    return {"status": "ok", "message": "extract running in background"}


@router.post("/fetch_file_and_store", response_model=_MODEL)
async def fetch_files_from_S3(
    s3Bucket: str,
    background_tasks: BackgroundTasks,
    db: Database = Depends(get_mongo_db),
    milvusDb=Depends(get_version_data_milvus_client),
):
    background_tasks.add_task(ocr.loop_files_in_s3Bucket, s3Bucket, db)
    return {
        "status": "ok",
        "message": "running ocr for new files in bucket, it can take a few minutes ... ",
    }


@router.post("/upload_and_create_embedding")
async def upload_and_create_embedding(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    db: Database = Depends(get_mongo_db),
    user=Depends(get_current_user),
):
    try:
        # Generate unique upload ID
        upload_id = os.urandom(16).hex()

        # Read file size
        file.file.seek(0, 2)
        file_size = file.file.tell()
        file.file.seek(0)

        logger.info(f"Uploading file: {file.filename} ({format_size(file_size)})")
        # Start upload event
        await send_progress_event(
            upload_id,
            0,
            "started",
            f"Starting upload of {file.filename} ({format_size(file_size)})",
        )

        # Read the file in chunks and track progress
        contents = bytearray()
        chunk_size = 1024 * 1024  # 1MB chunks
        bytes_read = 0

        while True:
            chunk = await file.read(chunk_size)
            if not chunk:
                break

            contents.extend(chunk)
            bytes_read += len(chunk)
            progress = (bytes_read / file_size) * 100

            logger.info(
                f"Uploading: {format_size(bytes_read)} of {format_size(file_size)} ({round(progress, 2)}%)"
            )
            # Send upload progress
            await send_progress_event(
                upload_id,
                round(progress, 2),
                UploadStatus.UPLOADING,
                f"Uploading: {format_size(bytes_read)} of {format_size(file_size)}",
            )

        bucket_name = f"pasco-ocr-files-{STAGE}"
        location_name = f"MPUD Records/User Upload/{file.filename}"

        # Upload completed, start processing
        await send_progress_event(
            upload_id,
            100,
            UploadStatus.UPLOADING,
            "Upload complete, starting OCR processing",
        )

        mongo_result = ocr.upload_file_to_s3(
            io.BytesIO(contents),
            bucket_name,
            location_name,
            file.filename,
            user.get("cognito:username"),
            user.get("name"),
            user.get("custom:county"),
            upload_id,
            UploadStatus.UPLOADING,
            db,
        )

        await send_progress_event(
            upload_id,
            0,
            UploadStatus.OCR,
            "Starting OCR",
        )

        # repo = KnowledgeGraphRepository(db)
        repo = KnowledgeGraphRepositoryV2(db)
        background_tasks.add_task(
            repo.process_data,
            bucket_name,
            location_name,
            user.get("cognito:username"),
            user.get("custom:county"),
            user.get("name"),
            True,
            True,
            upload_id,
            mongo_result.inserted_id,
        )

        notification_title = f"New document uploaded by {user.get('name')}"
        notification_description = f"File name: {file.filename}"
        notification_id, notification_data = create_notification(
            user_id=user.get("cognito:username"),
            user_name=user.get("name"),
            title=notification_title,
            description=notification_description,
        )

        print("Notification created:", notification_id, notification_data)

        return JSONResponse(
            content={
                "upload_id": upload_id,
                "message": "PDF upload started, processing in background",
                "filename": file.filename,
                "notification_id": str(notification_id),
                "size": format_size(file_size),
            },
            status_code=200,
        )

    except Exception as e:
        if "upload_id" in locals():
            await send_progress_event(
                upload_id,
                0,
                "error",
                f"Upload failed: {str(e)}",
            )
        return JSONResponse(
            content={"message": f"An error occurred: {str(e)}"},
            status_code=500,
        )


# AWS S3 client configuration
s3_client = boto3.client(
    "s3",
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID_S3_Textract,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY_S3_Textract,
    region_name=settings.AWS_REGION,
)


@router.get("/fetch_chat_data")
async def chat_data(db: Database = Depends(get_mongo_db)):
    try:
        chats = ocr.chat_data(db)
        return chats
    except Exception as e:
        return {"error": str(e)}


@router.get("/fetch_file_data")
async def pdf_and_docx_view(db: Database = Depends(get_mongo_db)):
    try:
        files = ocr.get_files_data(db)
        return files
    except Exception as e:
        return {"error": str(e)}


@router.get("/view_file")
async def view_file(s3Location: str):
    try:
        # Download file from S3
        if BUCKET_NAME in s3Location.split("/")[0]:
            s3Location = "/".join(s3Location.split("/")[1:])
        print("BUCKET_NAME", BUCKET_NAME)
        print("s3Location", s3Location)
        response = s3_client.get_object(Bucket=BUCKET_NAME, Key=s3Location)
        file_stream = response["Body"].read()
        # Determine content type based on file extension
        file_ext = s3Location.lower().split(".")[-1]
        if file_ext == "pdf":
            content_type = "application/pdf"
        elif file_ext in ["jpg", "jpeg"]:
            content_type = "image/jpeg"
        elif file_ext == "png":
            content_type = "image/png"
        elif file_ext == "docx":
            content_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        elif file_ext == "txt":
            content_type = "text/plain"
        else:
            content_type = "application/octet-stream"  # Default content type for unknown file types
        return StreamingResponse(io.BytesIO(file_stream), media_type=content_type)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching file: {str(e)}")

