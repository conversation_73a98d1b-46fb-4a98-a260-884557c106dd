from fastapi import (
    APIRouter,
    File,
    Path,
    Query,
    Body,
    Depends,
    UploadFile,
)
from fastapi.responses import JSONResponse, StreamingResponse
from app.core.config import Settings
from app.crud.user_managment_repository import save_image_to_s3, get_optimized_image
from app.models.user_model import (
    RoleUpdateModel,
    UserResponseModel,
    UserUpdateRequestModel,
    getAllAttributeModel,
)
from app.utils.auth_utils import get_current_user
import boto3
from botocore.exceptions import ClientError


_SHOW_NAME = "users"
router = APIRouter(
    prefix=f"/{_SHOW_NAME}",
    tags=[_SHOW_NAME],
    responses={404: {"description": "Not found"}},
)
settings = Settings()


cognito_client = boto3.client(
    "cognito-idp",
    region_name=settings.AWS_REGION,
    aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
    aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
)
USER_POOL_ID = settings.AWS_USER_POOL_ID


@router.get("", response_model=UserResponseModel, summary="Get list of Cognito Users")
async def get_cognito_users(
    page: int = Query(1, ge=1, description="Current Page number"),
    page_size: int = Query(
        10, ge=1, le=100, description="Number of users to return per page"
    ),
    search: str = Query(None, description="Search term for filtering users"),
):
    try:
        all_users = []
        pagination_token = None

        # Prepare filter if search term is provided
        filter_expression = None
        if search:
            # Search in name or email
            filter_expression = f'name ^= "{search}" or email ^= "{search}"'

        # Fetch users with filter
        while True:
            params = {
                "UserPoolId": USER_POOL_ID,
            }

            if pagination_token:
                params["PaginationToken"] = pagination_token

            if filter_expression:
                params["Filter"] = filter_expression

            response = cognito_client.list_users(**params)
            all_users.extend(response["Users"])

            if "PaginationToken" in response:
                pagination_token = response["PaginationToken"]
            else:
                break

        # Calculate pagination
        total_users = len(all_users)
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paginated_users = all_users[start_index:end_index]

        simplified_users = []
        for user in paginated_users:
            simplified_user = {
                "username": user["Username"],
            }
            for attr in user["Attributes"]:
                if attr["Name"] == "name":
                    simplified_user["name"] = attr["Value"]
                elif attr["Name"] == "email":
                    simplified_user["email"] = attr["Value"]
                elif attr["Name"] == "custom:role":
                    simplified_user["role"] = attr["Value"]
            simplified_users.append(simplified_user)

        return {
            "users": simplified_users,
            "page": page,
            "page_size": page_size,
            "total": total_users,
            "has_more": end_index < total_users,
        }

    except ClientError as e:
        return JSONResponse(status_code=500, content={"detail": str(e)})


@router.put("/{username}/role", summary="Update user role")
async def update_user_role(
    username: str = Path(..., description="The username of the Cognito user"),
    role_update: RoleUpdateModel = Body(
        ...,
        description="The new role for the user",
        example={"role": "admin"},
    ),
    current_user=Depends(get_current_user),
):
    # Check if the current user has permission to update roles
    if (
        current_user.get("custom:role") != "superadmin"
        and current_user.get("custom:role") != "admin"
    ):
        return JSONResponse(
            status_code=403, content={"message": "Only admins can update user roles"}
        )
    if (
        current_user.get("custom:role") != "superadmin"
        and role_update.role == "superadmin"
    ):
        return JSONResponse(
            status_code=403,
            content={"message": "Only superadmin's can update superadmin role"},
        )
    try:
        # Update the user's custom:role attribute
        cognito_client.admin_update_user_attributes(
            UserPoolId=USER_POOL_ID,
            Username=username,
            UserAttributes=[
                {
                    "Name": "custom:role",
                    "Value": role_update.role.value,
                },
            ],
        )

        return {
            "message": f"Role for user {username} updated to {role_update.role.value}"
        }

    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        error_message = e.response["Error"]["Message"]
        if error_code == "UserNotFoundException":
            return JSONResponse(
                status_code=404, content={"message": f"User not found: {username}"}
            )
        elif error_code == "InvalidParameterException":
            return JSONResponse(
                status_code=400,
                content={"message": f"Invalid parameters: {error_message}"},
            )
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "message": f"An error occurred with the Cognito service: {error_message}"
                },
            )


@router.put("/{username}", summary="Update user details")
async def update_user_details(
    username: str = Path(..., description="The username of the Cognito user"),
    user_update: UserUpdateRequestModel = Body(..., description="The new user details"),
    current_user=Depends(get_current_user),
):
    if current_user.get("cognito:username") != username:
        return JSONResponse(
            status_code=403,
            content={"message": "Only the current user can update their details"},
        )

    try:
        user_attributes = []
        if user_update.name is not None:
            user_attributes.append({"Name": "name", "Value": user_update.name})
        if user_update.designation is not None:
            user_attributes.append(
                {"Name": "custom:designation", "Value": user_update.designation}
            )
        if user_update.department is not None:
            user_attributes.append(
                {"Name": "custom:department", "Value": user_update.department}
            )
        if user_update.timezone is not None:
            user_attributes.append(
                {
                    "Name": "custom:timezone",
                    "Value": (user_update.timezone.model_dump_json()),
                }
            )
        if user_update.chatModel is not None:
            user_attributes.append(
                {"Name": "custom:chatModel", "Value": user_update.chatModel.value}
            )

        if user_attributes:
            cognito_client.admin_update_user_attributes(
                UserPoolId=USER_POOL_ID,
                Username=username,
                UserAttributes=user_attributes,
            )

        return JSONResponse(
            status_code=200,
            content={"message": f"User {username} updated successfully"},
        )

    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        error_message = e.response["Error"]["Message"]
        if error_code == "UserNotFoundException":
            return JSONResponse(
                status_code=404, content={"message": f"User not found: {username}"}
            )
        elif error_code == "InvalidParameterException":
            return JSONResponse(
                status_code=400,
                content={"message": f"Invalid parameters: {error_message}"},
            )
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "message": f"An error occurred with the Cognito service: {error_message}"
                },
            )


@router.put("/{username}/profileImage", summary="Update user profile image")
async def update_user_profile_image(
    username: str = Path(..., description="The username of the Cognito user"),
    profileImage: UploadFile = File(..., description="The new user profile image"),
    current_user=Depends(get_current_user),
):
    if current_user.get("cognito:username") != username:
        return JSONResponse(
            status_code=403,
            content={"message": "Only the current user can update their details"},
        )

    try:
        user_attributes = []

        save_image_to_s3(username, await profileImage.read())
        user_attributes.append(
            {
                "Name": "custom:profileImage",
                "Value": f"/{username}/profileImage/64/64",
            }
        )

        if user_attributes:
            cognito_client.admin_update_user_attributes(
                UserPoolId=USER_POOL_ID,
                Username=username,
                UserAttributes=user_attributes,
            )

        return JSONResponse(
            status_code=200,
            content={"message": f"User {username} profile image updated successfully"},
        )

    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        error_message = e.response["Error"]["Message"]
        if error_code == "UserNotFoundException":
            return JSONResponse(
                status_code=404, content={"message": f"User not found: {username}"}
            )
        elif error_code == "InvalidParameterException":
            return JSONResponse(
                status_code=400,
                content={"message": f"Invalid parameters: {error_message}"},
            )
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "message": f"An error occurred with the Cognito service: {error_message}"
                },
            )


@router.get(
    "/{username}/profileImage/{size1}/{size2}", summary="get user profile image"
)
async def get_user_profile_image(
    username: str = Path(..., description="The username of the Cognito user"),
    size1: int = Path(..., description="The size of the image"),
    size2: int = Path(..., description="The size of the image"),
):
    try:
        img_stream = get_optimized_image(username, size1)
        return StreamingResponse(img_stream, media_type="image/webp")

    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        error_message = e.response["Error"]["Message"]
        if error_code == "UserNotFoundException":
            return JSONResponse(
                status_code=404, content={"message": f"User not found: {username}"}
            )
        elif error_code == "InvalidParameterException":
            return JSONResponse(
                status_code=400,
                content={"message": f"Invalid parameters: {error_message}"},
            )
        else:
            return JSONResponse(
                status_code=500,
                content={
                    "message": f"An error occurred with the Cognito service: {error_message}"
                },
            )


@router.get(
    "/get_all_value_by_attributes",
    response_model=getAllAttributeModel,
    summary="Get a list of all values for a specific attribute",
)
async def get_all_cognito_users(attribute: str, current_user=Depends(get_current_user)):
    try:
        all_users = []
        attribute_list = []
        pagination_token = None

        while True:
            if pagination_token:
                response = cognito_client.list_users(
                    UserPoolId=USER_POOL_ID,
                    AttributesToGet=[attribute],
                )
            else:
                response = cognito_client.list_users(
                    UserPoolId=USER_POOL_ID,
                    AttributesToGet=[attribute],
                )

            all_users.extend(response["Users"])

            if "PaginationToken" in response:
                pagination_token = response["PaginationToken"]
            else:
                break

        for user in all_users:
            if "Attributes" in user and user["Attributes"]:
                usratt = user["Attributes"][0]
                if attribute in usratt["Name"]:
                    attribute_list.append(usratt["Value"])

        return {"attribute": attribute_list}

    except ClientError as e:
        return JSONResponse(
            status_code=500, content={"detail": str(e), "attribute": []}
        )
