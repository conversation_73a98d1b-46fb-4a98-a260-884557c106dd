from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse, RedirectResponse
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from app.utils.accela_utils import (
    make_accela_request,
    format_condition_data,
    validate_record_id,
    extract_error_message,
    get_auth_url,
    exchange_code_for_token,
    refresh_access_token,
    get_auth_status,
    logout,
    TOKEN_STORE
)

router = APIRouter(
    tags=["accela"],
    responses={404: {"description": "Not found"}},
)

# --------------------------
# Authentication Endpoints
# --------------------------


@router.get("/login")
async def accela_login():
    """
    Redirect to Accela OAuth2 authorization URL
    """
    auth_url = get_auth_url()
    return RedirectResponse(url=auth_url)

# --------------------------
# Callback Endpoint (No JWT required)
# --------------------------

# Pydantic model for callback request body


class CallbackRequest(BaseModel):
    code: Optional[str] = None
    error: Optional[str] = None


@router.post("/callback")
async def accela_callback(
    callback_data: CallbackRequest
):
    """
    OAuth2 callback endpoint - no JWT required
    Called by Accela OAuth2 redirect
    Exchanges authorization code for access token
    """
    if callback_data.error:
        return JSONResponse(
            status_code=400,
            content={
                "error": "OAuth error",
                "details": callback_data.error
            }
        )

    if not callback_data.code:
        return JSONResponse(
            status_code=400,
            content={
                "error": "No authorization code provided"
            }
        )

    try:
        # Exchange authorization code for access token
        token_data = await exchange_code_for_token(callback_data.code)

        if not token_data:
            return JSONResponse(
                status_code=400,
                content={
                    "error": "Failed to exchange authorization code for token",
                    "details": "Token exchange failed"
                }
            )

        # Store the token data (in production, store in database)
        TOKEN_STORE['token_data'] = token_data

        # Return success with token information
        return JSONResponse(
            status_code=200,
            content={
                "message": "Authentication successful",
                "access_token": token_data.get('access_token'),
                "token_type": token_data.get('token_type', 'Bearer'),
                "expires_in": token_data.get('expires_in'),
                "expires_at": token_data.get('expires_at'),
                "refresh_token": token_data.get('refresh_token'),
                "scope": token_data.get('scope'),
                "status": "success"
            }
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error during token exchange",
                "details": str(e)
            }
        )

# --------------------------
# Token Management Endpoints
# --------------------------


@router.get("/auth/status")
async def get_auth_status_endpoint():
    """
    Get current authentication status
    """
    status = get_auth_status()
    return JSONResponse(
        status_code=200,
        content=status
    )


@router.post("/auth/logout")
async def logout_endpoint():
    """
    Logout and clear stored tokens
    """
    result = logout()
    return JSONResponse(
        status_code=200,
        content=result
    )


@router.post("/auth/refresh")
async def refresh_token_endpoint():
    """
    Refresh the Accela access token using the stored refresh token
    """
    try:
        # Get the stored token data
        token_data = TOKEN_STORE.get('token_data')

        if not token_data:
            return JSONResponse(
                status_code=401,
                content={
                    "error": "No token data found",
                    "details": "Please authenticate first using the login flow"
                }
            )

        refresh_token = token_data.get('refresh_token')
        if not refresh_token:
            return JSONResponse(
                status_code=401,
                content={
                    "error": "No refresh token available",
                    "details": "Please re-authenticate using the login flow"
                }
            )

        # Refresh the access token
        new_token_data = await refresh_access_token(refresh_token)

        if not new_token_data:
            return JSONResponse(
                status_code=400,
                content={
                    "error": "Failed to refresh access token",
                    "details": "Refresh token may be invalid or expired"
                }
            )

        # Update the stored token data
        TOKEN_STORE['token_data'] = new_token_data

        # Return the new token information
        return JSONResponse(
            status_code=200,
            content={
                "message": "Token refreshed successfully",
                "access_token": new_token_data.get('access_token'),
                "token_type": new_token_data.get('token_type', 'Bearer'),
                "expires_in": new_token_data.get('expires_in'),
                "expires_at": new_token_data.get('expires_at'),
                "refresh_token": new_token_data.get('refresh_token'),
                "scope": new_token_data.get('scope'),
                "status": "success"
            }
        )

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error during token refresh",
                "details": str(e)
            }
        )


@router.get("/auth/debug")
async def debug_token_endpoint():
    """
    Debug endpoint to check stored token data (for development only)
    """
    token_data = TOKEN_STORE.get('token_data')
    if token_data:
        # Don't expose sensitive data in production
        return JSONResponse(
            status_code=200,
            content={
                "has_token": True,
                "expires_at": token_data.get('expires_at'),
                "expires_in": token_data.get('expires_in'),
                "has_refresh_token": bool(token_data.get('refresh_token')),
                "scope": token_data.get('scope')
            }
        )
    else:
        return JSONResponse(
            status_code=200,
            content={
                "has_token": False
            }
        )

# Pydantic models for Accela API requests


class AccelaCondition(BaseModel):
    name: str
    statusType: str
    appliedDate: str
    longComments: str
    shortComments: str
    statusDate: str
    type: Dict[str, str]
    group: Dict[str, str]
    status: Dict[str, str]
    appliedbyUser: Dict[str, str]
    severity: Dict[str, str]
    activeStatus: Dict[str, str]
    inheritable: Dict[str, str]
    displayNoticeInAgency: bool
    isIncludeNameInNotice: bool
    isIncludeShortCommentsInNotice: bool
    displayNoticeInCitizens: bool
    displayNoticeInCitizensFee: bool


class AccelaConfig(BaseModel):
    app_id: str
    app_secret: str
    environment: str = "TEST"
    agency: str = "NULLISLAND"
    base_url: str = "https://apis.accela.com/v4"


class AccelaTokenRequest(BaseModel):
    accela_token: str


@router.post("/conditions/standard")
async def get_standard_conditions(
    token_request: AccelaTokenRequest,
    group_id: Optional[str] = Query(None, description="Filter by condition group ID"),
    type_id: Optional[str] = Query(None, description="Filter by condition type ID")
):
    """
    Get standard conditions from Accela

    This endpoint retrieves standard conditions from Accela API.
    Optional query parameters can be used to filter by group and/or type.

    API Endpoint: GET /v4/conditions/standard
    Scope: conditions
    App Type: Agency
    Authorization Type: Access token
    Civic Platform version: 7.3.2+
    """
    try:
        # Prepare query parameters
        params = {}
        if group_id:
            params["groupId"] = group_id
        if type_id:
            params["typeId"] = type_id

        # Make request to Accela API
        result = await make_accela_request(
            method="GET",
            endpoint="/conditions/standard",
            authorization_token=token_request.accela_token,
            params=params
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Standard conditions retrieved successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to retrieve standard conditions",
                    "details": error_message
                }
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving standard conditions: {str(e)}")


@router.post("/conditionApprovals/standard")
async def get_standard_condition_approvals(
    token_request: AccelaTokenRequest,
    group_id: Optional[str] = Query(None, description="Filter by condition group ID"),
    type_id: Optional[str] = Query(None, description="Filter by condition type ID")
):
    """
    Get standard condition approvals from Accela

    This endpoint retrieves standard condition approvals from Accela API.
    Optional query parameters can be used to filter by group and/or type.

    API Endpoint: GET /v4/conditionApprovals/standard
    Scope: conditions
    App Type: Agency
    Authorization Type: Access token
    Civic Platform version: 7.3.2+
    """
    try:
        # Prepare query parameters
        params = {}
        if group_id:
            params["groupId"] = group_id
        if type_id:
            params["typeId"] = type_id

        # Make request to Accela API
        result = await make_accela_request(
            method="GET",
            endpoint="/conditionApprovals/standard",
            authorization_token=token_request.accela_token,
            params=params
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Standard condition approvals retrieved successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to retrieve standard condition approvals",
                    "details": error_message
                }
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving standard condition approvals: {str(e)}")


@router.post("/conditionApprovals/{record_id}")
async def get_conditionApprovals(
    record_id: str,
    token_request: AccelaTokenRequest
):
    """
    Get condition approvals for a specific record in Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Make request to Accela API
        result = await make_accela_request(
            method="GET",
            endpoint=f"/records/{record_id}/conditionApprovals",
            authorization_token=token_request.accela_token
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Condition approvals retrieved successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to retrieve condition approvals",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving condition approvals: {str(e)}")


class ConditionsRequest(BaseModel):
    conditions: List[Dict[str, Any]]
    token_request: AccelaTokenRequest


@router.post("/conditionApprovals/{record_id}/create")
async def create_conditionApprovals(
    record_id: str,
    request_data: ConditionsRequest
):
    """
    Create condition approvals for a specific record in Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Extract data from request
        conditions_list = request_data.conditions
        token_request = request_data.token_request

        # Format each condition
        conditions_data = [format_condition_data(condition) for condition in conditions_list]

        # Make request to Accela API
        result = await make_accela_request(
            method="POST",
            endpoint=f"/records/{record_id}/conditionApprovals",
            authorization_token=token_request.accela_token,
            data=conditions_data
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Condition approvals created successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to create condition approvals",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating condition approvals: {str(e)}")


@router.post("/conditions/{record_id}/create")
async def create_condition(
    record_id: str,
    request_data: ConditionsRequest
):
    """
    Create conditions for a specific record in Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Extract data from request
        conditions_list = request_data.conditions
        token_request = request_data.token_request

        # Format each condition
        conditions_data = [format_condition_data(condition) for condition in conditions_list]

        # Make request to Accela API
        result = await make_accela_request(
            method="POST",
            endpoint=f"/records/{record_id}/conditionApprovals",
            authorization_token=token_request.accela_token,
            data=conditions_data
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Conditions created successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to create conditions",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating conditions: {str(e)}")


@router.post("/conditions/{record_id}")
async def get_conditions(
    record_id: str,
    token_request: AccelaTokenRequest
):
    """
    Get conditions for a specific record in Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Make request to Accela API
        result = await make_accela_request(
            method="GET",
            endpoint=f"/records/{record_id}/conditions",
            authorization_token=token_request.accela_token
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Conditions retrieved successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to retrieve conditions",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving conditions: {str(e)}")


@router.post("/conditions/{record_id}/{condition_id}/update")
async def update_condition(
    record_id: str,
    condition_id: str,
    condition: AccelaCondition,
    token_request: AccelaTokenRequest
):
    """
    Update a specific condition for a record in Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Format condition data
        condition_data = format_condition_data(condition.dict())

        # Make request to Accela API
        result = await make_accela_request(
            method="PUT",
            endpoint=f"/records/{record_id}/conditions/{condition_id}",
            authorization_token=token_request.accela_token,
            data=condition_data
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Condition updated successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to update condition",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating condition: {str(e)}")


@router.post("/conditions/{record_id}/{condition_id}/delete")
async def delete_condition(
    record_id: str,
    condition_id: str,
    token_request: AccelaTokenRequest
):
    """
    Delete a specific condition for a record in Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Make request to Accela API
        result = await make_accela_request(
            method="DELETE",
            endpoint=f"/records/{record_id}/conditions/{condition_id}",
            authorization_token=token_request.accela_token
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Condition deleted successfully"
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to delete condition",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting condition: {str(e)}")


@router.post("/records/{record_id}")
async def get_record(
    record_id: str,
    token_request: AccelaTokenRequest
):
    """
    Get a specific record from Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Make request to Accela API
        result = await make_accela_request(
            method="GET",
            endpoint=f"/records/{record_id}",
            authorization_token=token_request.accela_token
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Record retrieved successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to retrieve record",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving record: {str(e)}")


@router.post("/records")
async def search_records(
    token_request: AccelaTokenRequest,
    limit: int = 10,
    offset: int = 0
):
    """
    Search records in Accela
    """
    try:
        params = {
            "limit": limit,
            "offset": offset
        }

        # Make request to Accela API
        result = await make_accela_request(
            method="GET",
            endpoint="/records",
            authorization_token=token_request.accela_token,
            params=params
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Records retrieved successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to retrieve records",
                    "details": error_message
                }
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving records: {str(e)}")


@router.post("/records/create")
async def create_record(
    record_data: Dict[str, Any],
    token_request: AccelaTokenRequest
):
    """
    Create a new record in Accela
    """
    try:
        # Make request to Accela API
        result = await make_accela_request(
            method="POST",
            endpoint="/records",
            authorization_token=token_request.accela_token,
            data=record_data
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Record created successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to create record",
                    "details": error_message
                }
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error creating record: {str(e)}")


@router.post("/records/{record_id}/update")
async def update_record(
    record_id: str,
    record_data: Dict[str, Any],
    token_request: AccelaTokenRequest
):
    """
    Update a record in Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Make request to Accela API
        result = await make_accela_request(
            method="PUT",
            endpoint=f"/records/{record_id}",
            authorization_token=token_request.accela_token,
            data=record_data
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Record updated successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to update record",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating record: {str(e)}")


@router.post("/documents/{record_id}")
async def get_documents(
    record_id: str,
    token_request: AccelaTokenRequest
):
    """
    Get documents for a specific record in Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Make request to Accela API
        result = await make_accela_request(
            method="GET",
            endpoint=f"/records/{record_id}/documents",
            authorization_token=token_request.accela_token
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Documents retrieved successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to retrieve documents",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving documents: {str(e)}")


@router.post("/workflow/{record_id}")
async def get_workflow(
    record_id: str,
    token_request: AccelaTokenRequest
):
    """
    Get workflow information for a specific record in Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Make request to Accela API
        result = await make_accela_request(
            method="GET",
            endpoint=f"/records/{record_id}/workflow",
            authorization_token=token_request.accela_token
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Workflow retrieved successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to retrieve workflow",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving workflow: {str(e)}")


@router.post("/workflow/{record_id}/actions")
async def execute_workflow_action(
    record_id: str,
    action_data: Dict[str, Any],
    token_request: AccelaTokenRequest
):
    """
    Execute a workflow action for a record in Accela
    """
    try:
        # Validate record ID
        if not validate_record_id(record_id):
            raise HTTPException(status_code=400, detail="Invalid record ID format")

        # Make request to Accela API
        result = await make_accela_request(
            method="POST",
            endpoint=f"/records/{record_id}/workflow/actions",
            authorization_token=token_request.accela_token,
            data=action_data
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Workflow action executed successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to execute workflow action",
                    "details": error_message
                }
            )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error executing workflow action: {str(e)}")


@router.post("/settings/types")
async def get_record_types(
    token_request: AccelaTokenRequest
):
    """
    Get record types from Accela settings
    """
    try:
        # Make request to Accela API
        result = await make_accela_request(
            method="GET",
            endpoint="/settings/records/types",
            authorization_token=token_request.accela_token
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Record types retrieved successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to retrieve record types",
                    "details": error_message
                }
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving record types: {str(e)}")


@router.post("/settings/conditions/types")
async def get_condition_types(
    token_request: AccelaTokenRequest
):
    """
    Get condition types from Accela settings
    """
    try:
        # Make request to Accela API
        result = await make_accela_request(
            method="GET",
            endpoint="/settings/conditions/types",
            authorization_token=token_request.accela_token
        )

        if result["status_code"] == 200:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Condition types retrieved successfully",
                    "data": result["data"]
                }
            )
        else:
            error_message = extract_error_message(result["error"])
            return JSONResponse(
                status_code=result["status_code"],
                content={
                    "error": "Failed to retrieve condition types",
                    "details": error_message
                }
            )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving condition types: {str(e)}")
