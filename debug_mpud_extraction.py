#!/usr/bin/env python3
"""
Debug script to analyze MPUD extraction issues
"""

import os
import sys
import re
from pymongo import MongoClient
from app.core.config import settings

def check_file_status(filename):
    """Check the status of a file in the database"""
    client = MongoClient(settings.MONGO_URL)
    db = client[settings.MONGO_DEFAULT_DATABASE]
    
    # Check files_status collection
    status_doc = db.files_status.find_one({"file_name": filename})
    print(f"\n=== Status for {filename} ===")
    if status_doc:
        print(f"Status document found:")
        print(f"  - OCR Status: {status_doc.get('status', {}).get('ocr', {}).get('status', 'N/A')}")
        print(f"  - MPUD File: {status_doc.get('mpudFile', 'Not set')}")
        print(f"  - Bucket: {status_doc.get('bucket_name', 'N/A')}")
        if 'mpud_detection_failure' in status_doc:
            failure = status_doc['mpud_detection_failure']
            print(f"  - Detection Failure:")
            print(f"    * Conditions found: {failure.get('conditions_found', 'N/A')}")
            print(f"    * MPUD found: {failure.get('mpud_found', 'N/A')}")
            print(f"    * Text sample: {failure.get('text_sample', 'N/A')[:200]}...")
    else:
        print("No status document found")
    
    # Check files_data collection
    data_doc = db.files_data.find_one({"filePath": {"$regex": filename}})
    if data_doc:
        print(f"\nData document found:")
        print(f"  - File Path: {data_doc.get('filePath', 'N/A')}")
        
        # Check for MPUD patterns in the text
        combined_text = " ".join(
            str(value) for key, value in data_doc.items() 
            if isinstance(value, str) and key not in ['_id', 'filePath']
        )
        
        # Test patterns
        pattern_conditions = re.compile('Conditions of Approval', re.IGNORECASE)
        pattern_master_planned = re.compile('MASTER PLANNED UNIT DEVELOPMENT', re.IGNORECASE)
        pattern_conditions_alt = re.compile(r"CONDITIONS?\s+OF\s+APPROVAL", re.IGNORECASE)
        pattern_mpud_alt = re.compile(r"MPUD|MASTER\s+PLAN|PLANNED\s+UNIT", re.IGNORECASE)
        
        conditions_found = pattern_conditions.search(combined_text)
        mpud_found = pattern_master_planned.search(combined_text)
        conditions_alt_found = pattern_conditions_alt.search(combined_text)
        mpud_alt_found = pattern_mpud_alt.search(combined_text)
        
        print(f"  - Pattern Analysis:")
        print(f"    * 'Conditions of Approval': {bool(conditions_found)}")
        print(f"    * 'MASTER PLANNED UNIT DEVELOPMENT': {bool(mpud_found)}")
        print(f"    * Alternative conditions pattern: {bool(conditions_alt_found)}")
        print(f"    * Alternative MPUD pattern: {bool(mpud_alt_found)}")
        print(f"    * Text length: {len(combined_text)} characters")
        print(f"    * Text sample: {combined_text[:300]}...")
        
        if conditions_found:
            print(f"    * Conditions match: '{conditions_found.group()}'")
        if mpud_found:
            print(f"    * MPUD match: '{mpud_found.group()}'")
            
    else:
        print("No data document found")
    
    client.close()

def reset_file_status(filename):
    """Reset the MPUD status for a file to allow reprocessing"""
    client = MongoClient(settings.MONGO_URL)
    db = client[settings.MONGO_DEFAULT_DATABASE]
    
    result = db.files_status.update_one(
        {"file_name": filename},
        {"$unset": {"mpudFile": "", "mpud_detection_failure": ""}}
    )
    
    print(f"\nReset status for {filename}: {result.modified_count} documents modified")
    client.close()

def main():
    if len(sys.argv) < 2:
        print("Usage: python debug_mpud_extraction.py <command> [filename]")
        print("Commands:")
        print("  check <filename> - Check file status")
        print("  reset <filename> - Reset file status")
        print("  compare - Compare both files")
        return
    
    command = sys.argv[1]
    
    if command == "check" and len(sys.argv) >= 3:
        filename = sys.argv[2]
        check_file_status(filename)
    elif command == "reset" and len(sys.argv) >= 3:
        filename = sys.argv[2]
        reset_file_status(filename)
    elif command == "compare":
        print("Comparing both files:")
        check_file_status("R3.A._-_TM_CONDITIONS_OF_APPROVAL(1-5).pdf")
        check_file_status("R3.A._-_TM_CONDITIONS_OF_APPROVAL.pdf")
    else:
        print("Invalid command or missing filename")

if __name__ == "__main__":
    main()
