useDotenv: true
service: pasco-govtech-ai # Service name
provider:
  name: aws
  region: ${env:REGION}
  stage: ${opt:stage, 'dev'}

resources:
  Resources:
    CognitoUserPool:
      Type: AWS::Cognito::UserPool
      Properties:
        UserPoolName: !Sub pasco-govtech-ai-userpool-${self:provider.stage}
        AccountRecoverySetting:
          RecoveryMechanisms:
            - Name: verified_email
              Priority: 1
        AdminCreateUserConfig:
          AllowAdminCreateUserOnly: false
        MfaConfiguration: "OFF"
        AutoVerifiedAttributes:
          - email
        UsernameAttributes:
          - email
        Policies:
          PasswordPolicy:
            MinimumLength: 6
            RequireLowercase: False
            RequireNumbers: False
            RequireSymbols: False
            RequireUppercase: False
        Schema:
          - Name: email
            AttributeDataType: String
            Mutable: true
            Required: true
          - Name: name
            AttributeDataType: String
            Mutable: true
            Required: true
          - Name: role
            AttributeDataType: String
            Mutable: true
            Required: false
          - Name: county
            AttributeDataType: String
            Mutable: true
            Required: false
        VerificationMessageTemplate:
          DefaultEmailOption: CONFIRM_WITH_CODE
          EmailSubject: "Pasco Govtech Verification Code"

    CognitoUserPoolClient:
      Type: AWS::Cognito::UserPoolClient
      Properties:
        ClientName: !Sub AppClient-${self:provider.stage}
        UserPoolId: !Ref CognitoUserPool
        SupportedIdentityProviders:
          - COGNITO
        ExplicitAuthFlows:
          - ALLOW_ADMIN_USER_PASSWORD_AUTH
          - ALLOW_USER_PASSWORD_AUTH
          - ALLOW_REFRESH_TOKEN_AUTH
          - ALLOW_USER_SRP_AUTH
          - ALLOW_CUSTOM_AUTH
        GenerateSecret: false
        IdTokenValidity: 1413 # 23.55 Hours
        AccessTokenValidity: 30
        RefreshTokenValidity: 1
        TokenValidityUnits:
          AccessToken: "minutes"
          IdToken: "minutes"
          RefreshToken: "days"
