version: 0.2

cache:
  paths:
    - ~/.cache/pip

phases:
  install:
    commands:
      - echo Installing Flake8...
      - pip install flake8
      - echo Installing dependencies...
      - pip install -r requirements.txt
  pre_build:
    commands:
      - echo Linting with Flake8...
      - flake8 ./
artifacts:
  files:
    - appspec.yml
    - docker-compose.yml
    - Dockerfile
    - scripts/**/*
    - fetch-ssm.sh
    - app/**/*
    - tests/**/*
    - requirements.txt
