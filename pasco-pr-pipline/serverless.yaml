useDotenv: true
service: pasco-pr-pipeline

provider:
  name: aws
  runtime: python3.12
  stage: ${opt:stage, 'dev'}
  region: ${env:REGION}
  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - codecommit:PostCommentForPullRequest
            - codecommit:GetPullRequest
          Resource: arn:aws:codecommit:${self:provider.region}:${aws:accountId}:${self:custom.repositoryName}
        - Effect: Allow
          Action:
            - codebuild:BatchGetBuilds
          Resource: "*"
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: arn:aws:logs:*:*:*

custom:
  repositoryName: ${env:REPOSITORY}

functions:
  prCommentLambda:
    name: pasco-pr-pipeline
    handler: src/index.lambda_handler
    environment:
      SERVICE_NAME: ${self:service}
      STAGE: ${self:provider.stage}
      REPOSITORY_NAME: ${self:custom.repositoryName}
    events:
      - eventBridge:
          pattern:
            source:
              - aws.codecommit
            detail-type:
              - CodeCommit Pull Request State Change
            detail:
              repositoryNames:
                - ${self:custom.repositoryName}
              event:
                - pullRequestCreated
                - pullRequestSourceBranchUpdated
      - eventBridge:
          pattern:
            source:
              - aws.codebuild
            detail-type:
              - CodeBuild Build State Change
            detail:
              project-name:
                - ${self:service}-build
              build-status:
                - IN_PROGRESS
                - SUCCEEDED
                - FAILED
                - STOPPED

resources:
  Resources:
    CodeBuildProject:
      Type: AWS::CodeBuild::Project
      Properties:
        Name: ${self:service}-build
        ServiceRole: !GetAtt CodeBuildServiceRole.Arn
        Artifacts:
          Type: NO_ARTIFACTS
        Environment:
          ComputeType: BUILD_GENERAL1_SMALL
          Image: aws/codebuild/amazonlinux2-x86_64-standard:3.0
          Type: LINUX_CONTAINER
        Source:
          Type: CODECOMMIT
          Location: https://git-codecommit.${self:provider.region}.amazonaws.com/v1/repos/${self:custom.repositoryName}
          BuildSpec: buildspec-pr.yml

    CodeBuildServiceRole:
      Type: AWS::IAM::Role
      Properties:
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Principal:
                Service: codebuild.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: CodeBuildServiceRolePolicy
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: Allow
                  Action:
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                    - s3:*
                    - codecommit:*
                    - events:*
                    - codebuild:*
                    - sns:*
                    - ecr:*
                  Resource: "*"

    CodeBuildEventBridgeRule:
      Type: AWS::Events::Rule
      Properties:
        EventPattern:
          source:
            - aws.codecommit
          detail-type:
            - CodeCommit Pull Request State Change
          detail:
            event:
              - pullRequestCreated
              - pullRequestSourceBranchUpdated
            repositoryNames:
              - ${self:custom.repositoryName}
        Targets:
          - Arn: !GetAtt CodeBuildProject.Arn
            RoleArn: !GetAtt EventBridgeInvokeRole.Arn
            Id: codebuild-target
            InputTransformer:
              InputPathsMap:
                sourceCommit: "$.detail.sourceCommit"
                destinationCommit: "$.detail.destinationCommit"
                pullRequestId: "$.detail.pullRequestId"
                sourceReference: "$.detail.sourceReference"
              InputTemplate: |
                {
                  "sourceVersion": <sourceCommit>,
                  "environmentVariablesOverride": [
                    {
                      "name": "PULL_REQUEST_ID",
                      "value": <pullRequestId>,
                      "type": "PLAINTEXT"

                    },
                    {
                      "name": "SOURCE_COMMIT",
                      "value": <sourceCommit>,
                      "type": "PLAINTEXT"
                    },
                    {
                      "name": "DESTINATION_COMMIT",
                      "value": <destinationCommit>,
                      "type": "PLAINTEXT"
                    },
                    {
                      "name": "SOURCE_BRANCH",
                      "value": <sourceReference>,
                      "type": "PLAINTEXT"
                    }
                  ]
                }

    EventBridgeInvokeRole:
      Type: AWS::IAM::Role
      Properties:
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Principal:
                Service: events.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: InvokeCodeBuild
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: Allow
                  Action:
                    - codebuild:StartBuild
                  Resource: !GetAtt CodeBuildProject.Arn

  Outputs:
    LambdaFunctionArn:
      Description: Lambda Function ARN
      Value: !GetAtt PrCommentLambdaLambdaFunction.Arn
    CodeBuildProjectArn:
      Description: CodeBuild Project ARN
      Value: !GetAtt CodeBuildProject.Arn
