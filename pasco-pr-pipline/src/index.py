import boto3
import json
import os


def lambda_handler(event, context):
    print(f"Received event: {json.dumps(event)}")

    if event['source'] == 'aws.codecommit':
        return handle_codecommit_event(event)
    elif event['source'] == 'aws.codebuild':
        return handle_codebuild_event(event)
    else:
        print(f"Unsupported event source: {event['source']}")
        return {
            'statusCode': 400,
            'body': json.dumps('Unsupported event source'),
        }


def handle_codecommit_event(event):
    detail = event['detail']
    repository_name = detail['repositoryNames'][0]
    pull_request_id = detail['pullRequestId']

    codecommit = boto3.client('codecommit')

    codecommit.post_comment_for_pull_request(
        pullRequestId=pull_request_id,
        repositoryName=repository_name,
        beforeCommitId=detail['sourceCommit'],
        afterCommitId=detail['destinationCommit'],
        content="""### PR status update

This pull request has been processed by the automated system. Build is starting.""",
    )

    print(f"Comment posted on PR {pull_request_id} in repository {repository_name}")
    return {
        'statusCode': 200,
        'body': json.dumps('Comment posted successfully'),
    }


def handle_codebuild_event(event):
    detail = event['detail']
    build_status = detail['build-status']
    build_id = detail['build-id']

    additional_info = detail.get('additional-information', {})
    environment = additional_info.get('environment', {})
    env_vars = {var['name']: var['value'] for var in environment.get('environment-variables', [])}

    source_branch = env_vars.get('SOURCE_BRANCH', '')
    source_branch = remove_refs_heads(source_branch)
    pull_request_id = env_vars.get('PULL_REQUEST_ID')
    source_commit = env_vars.get('SOURCE_COMMIT')
    destination_commit = env_vars.get('DESTINATION_COMMIT')

    if not pull_request_id:
        print(f"Build {build_id} is not associated with a pull request. Source branch: {source_branch}")
        return {
            'statusCode': 200,
            'body': json.dumps('Build not associated with a pull request'),
        }

    codecommit = boto3.client('codecommit')

    # Get repository name from environment variable
    repository_name = os.environ['REPOSITORY_NAME']

    message_list = {
        'IN_PROGRESS': 'The build is in progress. 🔵',
        'SUCCEEDED': 'The build has succeeded. 🟢',
        'FAILED': 'The build has failed. 🔴',
        'STOPPED': 'The build has been stopped. 🟡',
    }
    status_message = message_list.get(build_status, f"Unknown build status: {build_status}")

    # Extract log information
    logs_info = additional_info.get('logs', {})
    logs_deep_link = logs_info.get('deep-link', '')

    # Extract error information
    error_message = extract_error_message(additional_info.get('phases', []))

    # Construct CodeBuild badge URL

    comment_content = f"""### PR status update
Build status update: **{status_message}**

[View Full Build Logs]({logs_deep_link})

Branch: **{source_branch}**

{error_message}"""

    try:
        codecommit.post_comment_for_pull_request(
            pullRequestId=pull_request_id,
            repositoryName=repository_name,
            beforeCommitId=source_commit,
            afterCommitId=destination_commit,
            content=comment_content,
        )
        print(f"Build status comment posted on PR {pull_request_id} in repository {repository_name}")
    except codecommit.exceptions.PullRequestDoesNotExistException:
        print(f"Pull request {pull_request_id} not found in repository {repository_name}")
    except Exception as e:
        print(f"Error posting comment: {str(e)}")

    return {
        'statusCode': 200,
        'body': json.dumps('Build status processed'),
    }


def remove_refs_heads(branch_name):
    return branch_name.replace('refs/heads/', '')


def extract_error_message(phases):
    for phase in phases:
        if phase.get('phase-status') == 'FAILED':
            context = phase.get('phase-context', [])
            if context:
                return f"**Error:** {context[0]}"
    return "No specific error message found."
