services:
  mongo:
    image: mongo
    container_name: mongo-db
    environment:
      - MONGO_DATA_DIR=/data/db
      - MONGO_LOG_DIR=/dev/null
    command: mongod --logpath=/dev/null
    ports:
      - 27017:27017
    volumes:
      - mongo-data:/data/db

  etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd

  minio:
    container_name: milvus-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    volumes:
      - minio:/minio_data
    command: minio server /minio_data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  milvus:
    container_name: milvus-standalone
    image: milvusdb/milvus:latest
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - milvus-data:/var/lib/milvus
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"

  connector:
    container_name: fastapi-connector-${STAGE:-dev}
    image: pasco-connector-${STAGE:-dev}
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - ${ENV_FILE:-.env}
    command:
      [
        "python",
        "-m",
        "uvicorn",
        "app.main:app",
        "--host",
        "0.0.0.0",
        "--port",
        "8000",
        "--reload",
      ]
    ports:
      - ${API_PORT:-8000}:8000
    volumes:
      - ./:/fastapi
    depends_on:
      - "mongo"
      - "milvus"
      - "otel-collector"

  attu:
    container_name: attu
    image: zilliz/attu:latest
    environment:
      MILVUS_URL: milvus-standalone:19530
      MILVUS_DEFAULT_DATABASE: ${MILVUS_DEFAULT_DATABASE}
    ports:
      - "5000:3000"
    depends_on:
      - "milvus"

  neo4j:
    image: neo4j:5.26.3-ubi9
    container_name: neo4j-db
    ports:
      - "7474:7474" # HTTP
      - "7687:7687" # Bolt
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4J_apoc_export_file_enabled=true
      - NEO4J_apoc_import_file_enabled=true
      - NEO4J_apoc_import_file_use__neo4j__config=true
    volumes:
      - neo4j-data:/data
      - neo4j-logs:/logs
      - neo4j-import:/var/lib/neo4j/import
      - neo4j-plugins:/plugins

  otel-collector:
    container_name: pasco-collector
    image: otel/opentelemetry-collector-contrib:latest
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317" # OTLP gRPC receiver
      - "8888:8888" # Metrics exposed by the collector
      - "8889:8889" # Prometheus exporter metrics
    depends_on:
      - openobserve

  openobserve:
    container_name: pasco-openobserve
    image: public.ecr.aws/zinclabs/openobserve:latest
    environment:
      ZO_ROOT_USER_EMAIL: "<EMAIL>"
      ZO_ROOT_USER_PASSWORD: "Complexpass#123"
    ports:
      - "5080:5080"
    volumes:
      - openobserve-data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5080/healthz"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  mongo-data:
    name: pasco-mongo-data-${STAGE:-dev}
  milvus-data:
    name: pasco-milvus-data-${STAGE:-dev}
  etcd:
    name: pasco-etcd-data-${STAGE:-dev}
  minio:
    name: pasco-mino-data-${STAGE:-dev}
  neo4j-data:
    name: pasco-neo4j-data-${STAGE:-dev}
  neo4j-logs:
    name: pasco-neo4j-logs-${STAGE:-dev}
  neo4j-import:
    name: pasco-neo4j-import-${STAGE:-dev}
  neo4j-plugins:
    name: pasco-neo4j-plugins-${STAGE:-dev}
  openobserve-data:
    name: pasco-openobserve-data-${STAGE:-dev}

networks:
  default:
    name: pasco-fastapi-${STAGE:-dev}
